import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/network/network_info.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

/// A custom app bar widget that can be reused across the app.
///
/// This app bar includes a back button and a title, with customizable styling.
/// Also supports offline indicator functionality.
class CustomAppBar extends StatefulWidget implements PreferredSizeWidget {
  /// The title to display in the app bar.
  final String title;

  /// Callback function when the back button is pressed.
  /// If null, Navigator.pop will be used as the default behavior.
  final VoidCallback? onBackPressed;

  /// Whether to center the title. Defaults to false.
  final bool centerTitle;

  /// The spacing between the leading widget and the title. Defaults to 8.
  final double titleSpacing;

  /// Additional actions to display at the end of the app bar.
  final List<Widget>? actions;

  /// The background color of the app bar. Defaults to white.
  final Color backgroundColor;

  /// The elevation of the app bar. Defaults to 0.
  final double elevation;

  /// The system overlay style to apply. If null, a default style will be used.
  final SystemUiOverlayStyle? systemOverlayStyle;

  final Color titleColor;

  /// Widget to display at the bottom of the AppBar
  final PreferredSizeWidget? bottom;

  /// Whether to show the back button. Defaults to true.
  final bool showBackButton;

  /// Whether to show the offline indicator. Defaults to false.
  /// When true, displays an offline indicator regardless of network status.
  final bool isOffline;

  /// Whether to check network connectivity automatically
  /// Set to false in tests to avoid network dependency
  final bool checkNetworkConnectivity;

  /// Creates a custom app bar.
  const CustomAppBar({
    super.key,
    required this.title,
    this.onBackPressed,
    this.centerTitle = false,
    this.titleSpacing = 0,
    this.actions,
    this.backgroundColor = Colors.white,
    this.elevation = 4,
    this.systemOverlayStyle,
    this.titleColor = Colors.black,
    this.bottom,
    this.showBackButton = true,
    this.isOffline = false,
    this.checkNetworkConnectivity = true,
  });

  @override
  State<CustomAppBar> createState() => _CustomAppBarState();

  @override
  Size get preferredSize {
    // Calculate height including offline indicator if needed
    double baseHeight =
        kToolbarHeight + (bottom?.preferredSize.height ?? 0.0) + 0.5;
    // Add height for offline indicator (text + padding) when explicitly set to offline
    // Note: We can only check the explicit isOffline parameter here since preferredSize
    // is called before the widget is built and we can't access the dynamic state
    double offlineIndicatorHeight = 0.0;
    if (isOffline) {
      // Height for text (14px) + vertical padding (8px * 2)
      offlineIndicatorHeight = 30.0;
    }
    return Size.fromHeight(baseHeight + offlineIndicatorHeight);
  }
}

class _CustomAppBarState extends State<CustomAppBar> {
  late NetworkInfo _networkInfo;
  bool _isConnected = true;

  @override
  void initState() {
    super.initState();
    _networkInfo = NetworkInfo(InternetConnection());
    if (widget.checkNetworkConnectivity) {
      _checkConnectivity();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// Checks the current network connectivity status
  Future<void> _checkConnectivity() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (mounted) {
        setState(() {
          _isConnected = isConnected;
        });
      }
    } catch (e) {
      // If there's an error checking connectivity, assume connected for better UX
      // This prevents false offline indicators in test environments
      if (mounted) {
        setState(() {
          _isConnected = true;
        });
      }
    }
  }

  /// Determines if the offline indicator should be shown
  bool get _shouldShowOfflineIndicator {
    // If explicitly set to offline, always show indicator
    if (widget.isOffline) return true;

    // If explicitly set to not offline, only show if network check detects offline
    // This allows for explicit control in tests
    return !_isConnected;
  }

  /// Builds the offline indicator widget
  Widget _buildOfflineIndicator() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: const EdgeInsets.symmetric(vertical: 0),
      child: Center(
        child: Text(
          'Working offline',
          style: Theme.of(context).textTheme.montserratTitleExtraSmall.copyWith(
                color: AppColors.primaryBlue,
                fontSize: 12,
              ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return AppBar(
      backgroundColor: widget.backgroundColor,
      elevation: 0,
      centerTitle: widget.centerTitle,
      systemOverlayStyle: widget.systemOverlayStyle ??
          const SystemUiOverlayStyle(
            statusBarColor: Colors.white,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: Colors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light,
          ),
      // Use flexibleSpace to add offline indicator
      flexibleSpace: _shouldShowOfflineIndicator
          ? SafeArea(
              child: Column(
                children: [
                  _buildOfflineIndicator(),
                  Expanded(child: Container()),
                ],
              ),
            )
          : null,
      title: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.showBackButton) ...[
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: widget.onBackPressed ??
                    () {
                      Navigator.pop(context);
                    },
                borderRadius: BorderRadius.circular(22),
                child: Container(
                  width: 44,
                  height: 44,
                  alignment: Alignment.center,
                  child: Icon(
                    Icons.arrow_back_ios_new,
                    color: widget.titleColor,
                    size: 20,
                  ),
                ),
              ),
            ),
          ],
          Padding(
            padding: EdgeInsets.only(left: widget.showBackButton ? 0 : 16),
            child: Transform.translate(
              offset: Offset(widget.showBackButton ? -8.0 : 0.0, 0.0),
              child: Text(
                widget.title,
                style: textTheme.montserratTitleExtraSmall
                    .copyWith(color: widget.titleColor),
              ),
            ),
          ),
        ],
      ),
      titleSpacing: 0,
      automaticallyImplyLeading: false,
      actions: widget.actions,
      bottom: widget.bottom != null
          ? PreferredSize(
              preferredSize:
                  Size.fromHeight(widget.bottom!.preferredSize.height + 1),
              child: Column(
                children: [
                  widget.bottom!,
                  Container(
                    height: 1,
                    color: AppColors.appBarBorderBlack,
                  ),
                ],
              ),
            )
          : PreferredSize(
              preferredSize: const Size.fromHeight(1),
              child: Container(
                height: 0.5,
                color: AppColors.appBarBorderBlack,
              ),
            ),
    );
  }
}
