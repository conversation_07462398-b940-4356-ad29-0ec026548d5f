import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

class OdometerInputDialog extends StatefulWidget {
  final String title;
  final String message;
  final Function(int) onConfirm;
  final VoidCallback? onCancel;

  const OdometerInputDialog({
    super.key,
    required this.title,
    required this.message,
    required this.onConfirm,
    this.onCancel,
  });

  @override
  State<OdometerInputDialog> createState() => _OdometerInputDialogState();

  static Future<void> show({
    required BuildContext context,
    required String title,
    required String message,
    required Function(int) onConfirm,
    VoidCallback? onCancel,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return OdometerInputDialog(
          title: title,
          message: message,
          onConfirm: onConfirm,
          onCancel: onCancel,
        );
      },
    );
  }
}

class _OdometerInputDialogState extends State<OdometerInputDialog> {
  final TextEditingController _controller = TextEditingController();
  bool _isValid = true;
  String? _errorMessage;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _validateAndSubmit() {
    final text = _controller.text.trim();

    if (text.isEmpty) {
      setState(() {
        _isValid = false;
        _errorMessage = 'Please enter a valid odometer reading';
      });
      return;
    }

    final kms = int.tryParse(text);
    if (kms == null || kms < 0) {
      setState(() {
        _isValid = false;
        _errorMessage = 'Please enter a valid number';
      });
      return;
    }

    Navigator.of(context).pop();
    widget.onConfirm(kms);
  }

  void _cancel() {
    Navigator.of(context).pop();
    widget.onCancel?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              widget.title,
              style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
            ),
            const SizedBox(height: 16),
            Text(
              widget.message,
              textAlign: TextAlign.center,
              style: Theme.of(context)
                  .textTheme
                  .montserratTitleExtraSmall
                  .copyWith(
                    color: AppColors.black.withAlpha(196),
                  ),
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _controller,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              decoration: InputDecoration(
                labelText: 'Odometer Reading (km)',
                hintText: 'Enter kilometers',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: AppColors.primaryBlue,
                    width: 2,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: Colors.red,
                    width: 2,
                  ),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: Colors.red,
                    width: 2,
                  ),
                ),
                errorText: _isValid ? null : _errorMessage,
              ),
              onChanged: (value) {
                if (!_isValid) {
                  setState(() {
                    _isValid = true;
                    _errorMessage = null;
                  });
                }
              },
              onSubmitted: (_) => _validateAndSubmit(),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _cancel,
                  child: Text(
                    'Cancel',
                    style:
                        Theme.of(context).textTheme.montserratTitleExtraSmall,
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _validateAndSubmit,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryBlue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: Text(
                    'Confirm',
                    style: Theme.of(context)
                        .textTheme
                        .montserratTitleExtraSmall
                        .copyWith(
                          color: Colors.white,
                        ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
