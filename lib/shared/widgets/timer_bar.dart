import 'dart:async';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

enum TimerBarStatus { initial, running, paused }

class TimerBar extends StatefulWidget {
  final Duration duration;
  const TimerBar({super.key, this.duration = const Duration(minutes: 5)});

  @override
  State<TimerBar> createState() => _TimerBarState();
}

class _TimerBarState extends State<TimerBar> {
  final _stopwatch = Stopwatch();
  Timer? _timer;
  TimerBarStatus _status = TimerBarStatus.initial;

  @override
  void dispose() {
    _timer?.cancel();
    _stopwatch.stop();
    super.dispose();
  }

  void _startTimer() {
    setState(() {
      _status = TimerBarStatus.running;
    });
    _stopwatch.start();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_stopwatch.elapsed >= widget.duration) {
        _stopTimer(completed: true);
      } else {
        setState(() {});
      }
    });
  }

  void _pauseTimer() {
    if (_status == TimerBarStatus.running) {
      setState(() {
        _status = TimerBarStatus.paused;
      });
      _stopwatch.stop();
      _timer?.cancel();
    }
  }

  void _resumeTimer() {
    if (_status == TimerBarStatus.paused) {
      _startTimer();
    }
  }

  void _stopTimer({bool completed = false}) {
    setState(() {
      _status = TimerBarStatus.initial;
    });
    _stopwatch.stop();
    _stopwatch.reset();
    _timer?.cancel();
  }

  String get _elapsedTimeString {
    final elapsed = _stopwatch.elapsed;
    final totalMinutes = widget.duration.inMinutes;

    if (elapsed.inMinutes > 0) {
      return "${elapsed.inMinutes}m ${elapsed.inSeconds.remainder(60)}s / ${totalMinutes}m";
    }
    return "${elapsed.inSeconds}s / ${totalMinutes}m";
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: AppColors.appBarBorderBlack, width: 0.5),
        ),
      ),
      child: _buildContent(),
    );
  }

  Widget _buildContent() {
    switch (_status) {
      case TimerBarStatus.running:
      case TimerBarStatus.paused:
        return _buildRunningPausedView();
      case TimerBarStatus.initial:
        return _buildInitialView();
    }
  }

  Widget _buildInitialView() {
    return InkWell(
      onTap: _startTimer,
      borderRadius: BorderRadius.circular(10),
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: AppColors.primaryBlue),
        ),
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.play_arrow_rounded,
                color: AppColors.primaryBlue,
              ),
              const Gap(8),
              Text(
                'Start timer',
                style: Theme.of(context).textTheme.montserratSemiBold.copyWith(
                      color: AppColors.primaryBlue,
                      fontSize: 14,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRunningPausedView() {
    final isRunning = _status == TimerBarStatus.running;

    return SizedBox(
      height: 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Time elapsed',
                style: Theme.of(context)
                    .textTheme
                    .montserratFormsField
                    .copyWith(color: AppColors.blackTint1),
              ),
              // const Gap(4),
              Text(
                _elapsedTimeString,
                style: Theme.of(context)
                    .textTheme
                    .montserratTitleSmall
                    .copyWith(
                        color: AppColors.black,
                        letterSpacing: 0.5,
                        fontSize: 14),
              ),
            ],
          ),
          Row(
            children: [
              _buildControlButton(
                icon: isRunning ? Icons.pause : Icons.play_arrow,
                onTap: isRunning ? _pauseTimer : _resumeTimer,
                backgroundColor:
                    isRunning ? AppColors.warningBgColor : AppColors.green15,
                iconColor:
                    isRunning ? AppColors.richOrange : AppColors.loginGreen,
              ),
              const Gap(12),
              _buildControlButton(
                icon: Icons.stop,
                onTap: _stopTimer,
                backgroundColor: AppColors.loginRed.withOpacity(0.15),
                iconColor: AppColors.loginRed,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
    required Color backgroundColor,
    required Color iconColor,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(10),
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Icon(icon, color: iconColor),
      ),
    );
  }
}
