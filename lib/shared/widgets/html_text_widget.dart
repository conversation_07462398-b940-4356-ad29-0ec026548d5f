import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

/// A widget that renders HTML content if it contains HTML tags,
/// otherwise renders it as plain text.
class HtmlTextWidget extends StatelessWidget {
  final String? text;
  final TextStyle? style;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextAlign? textAlign;

  const HtmlTextWidget({
    super.key,
    required this.text,
    this.style,
    this.maxLines,
    this.overflow,
    this.textAlign,
  });

  @override
  Widget build(BuildContext context) {
    if (text == null || text!.isEmpty) {
      return const SizedBox.shrink();
    }

    // Check if the text contains HTML tags
    if (_containsHtmlTags(text!)) {
      return Html(
        data: text!,
        style: {
          "body": Style(
            margin: Margins.zero,
            padding: HtmlPaddings.zero,
            fontSize:
                style?.fontSize != null ? FontSize(style!.fontSize!) : null,
            color: style?.color,
            fontWeight: style?.fontWeight,
            fontFamily: style?.fontFamily,
            textAlign: _getHtmlTextAlign(textAlign),
          ),
          "p": Style(
            margin: Margins.zero,
            padding: HtmlPaddings.zero,
          ),
          "div": Style(
            margin: Margins.zero,
            padding: HtmlPaddings.zero,
          ),
        },
        // Disable all extensions to prevent unwanted formatting
        extensions: const [],
      );
    } else {
      // Render as plain text
      return Text(
        text!,
        style: style,
        maxLines: maxLines,
        overflow: overflow,
        textAlign: textAlign,
      );
    }
  }

  /// Check if the text contains HTML tags
  bool _containsHtmlTags(String text) {
    // Simple regex to check for HTML tags
    final htmlTagRegex = RegExp(r'<[^>]+>');
    return htmlTagRegex.hasMatch(text);
  }

  /// Convert Flutter TextAlign to HTML TextAlign
  TextAlign _getHtmlTextAlign(TextAlign? textAlign) {
    switch (textAlign) {
      case TextAlign.left:
        return TextAlign.left;
      case TextAlign.right:
        return TextAlign.right;
      case TextAlign.center:
        return TextAlign.center;
      case TextAlign.justify:
        return TextAlign.justify;
      case TextAlign.start:
        return TextAlign.start;
      case TextAlign.end:
        return TextAlign.end;
      default:
        return TextAlign.start;
    }
  }
}
