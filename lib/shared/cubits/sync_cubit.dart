import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';
import 'package:storetrack_app/shared/cubits/sync_state.dart';

export 'sync_state.dart';

class SyncCubit extends Cubit<SyncState> {
  final SyncService _syncService;
  bool _wasSyncing = false;

  SyncCubit(this._syncService) : super(SyncInitial()) {
    _wasSyncing = _syncService.isSyncing.value;
    _syncService.isSyncing.addListener(_onSyncStatusChanged);
  }

  void _onSyncStatusChanged() {
    final isSyncing = _syncService.isSyncing.value;
    if (isSyncing) {
      emit(SyncInProgress());
    } else {
      if (_wasSyncing) {
        emit(SyncSuccess());
      }
    }
    _wasSyncing = isSyncing;
  }

  @override
  Future<void> close() {
    _syncService.isSyncing.removeListener(_onSyncStatusChanged);
    return super.close();
  }
}
