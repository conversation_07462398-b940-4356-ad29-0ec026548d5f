/// A generic class to handle operation results with success and failure states
class Result<T> {
  const Result._({
    this.data,
    this.error,
  });

  // Creates a success result with data
  factory Result.success(T data) {
    return Result._(data: data);
  }

  // Creates a failure result with error message
  factory Result.failure(String error) {
    return Result._(error: error);
  }
  final T? data;
  final String? error;

  // Helper method to check if result is success
  bool get isSuccess => error == null;

  // Helper method to check if result is failure
  bool get isFailure => error != null;

  // Pattern matching for different states
  R when<R>({
    required R Function(T data) success,
    required R Function(String error) failure,
  }) {
    if (isSuccess) {
      return success(data as T);
    } else {
      return failure(error!);
    }
  }
}

extension ResultListExtensions on List<Result> {
  bool get hasFailures => any((result) => result.isFailure);

  List<String> get allErrors => where((result) => result.isFailure)
      .map((result) => result.error!)
      .toList();

  String getCombinedErrorMessage(List<String> operationNames) {
    final errors = <String>[];

    for (int i = 0; i < length; i++) {
      if (this[i].isFailure) {
        final operationName = i < operationNames.length
            ? operationNames[i]
            : 'Operation ${i + 1}';
        errors.add('$operationName: ${this[i].error}');
      }
    }

    if (errors.isEmpty) return '';
    if (errors.length == 1) return errors.first;

    return 'Multiple errors occurred:\n${errors.join('\n')}';
  }

  List<T> getSuccessData<T>() {
    return where((result) => result.isSuccess)
        .map((result) => result.data as T)
        .toList();
  }
}
