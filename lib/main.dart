import 'package:flutter/material.dart';
import 'package:storetrack_app/features/auth/presentation/blocs/auth/auth_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/availability/availability_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/dashboard/dashboard_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/history/history_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/induction/induction_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/leave/leave_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/pos/pos_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/skill/skills_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/today/today_page_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/useful_links/useful_links_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/notification_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/edit_profile/edit_profile_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/profile/profile_cubit.dart';
import 'package:storetrack_app/shared/widgets/global_sync_indicator.dart';
import 'config/routes/app_router.dart';
import 'config/themes/app_theme.dart';
import 'di/service_locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'core/utils/app_scroll.dart';
import 'features/auth/presentation/blocs/reset_password/reset_password_cubit.dart';
import 'features/home/<USER>/blocs/unschedule/unschedule_cubit.dart';
import 'features/home/<USER>/blocs/schedule/schedule_cubit.dart';
import 'features/home/<USER>/blocs/form_refresh/form_refresh_cubit.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'features/home/<USER>/blocs/auto_schedule/auto_schedule_cubit.dart';
import 'features/home/<USER>/blocs/vacancies/vacancies_cubit.dart';
import 'features/home/<USER>/blocs/open_tasks/open_tasks_cubit.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  tz.initializeTimeZones();
  // Initialize the service locator
  await initLocator();
  runApp(const MyApp());
}

/// The root widget of the app.
///
/// This widget creates a [MaterialApp] which is configured to use the
/// [AppRouter] for navigation and the [NavObserver] for observing route changes.
/// It also sets the title of the app, disables the debug banner, and sets the
/// theme to [AppTheme.light].
class MyApp extends StatelessWidget {
  /// Creates a new instance of [MyApp].
  ///
  /// The [key] parameter is optional and is used to identify the widget in the
  /// widget tree.
  const MyApp({super.key});

  /// The router used by the app.
  // final _appRouter = AppRouter();

  @override

  /// The build method of [MyApp].
  ///
  /// This method is called when the widget is first created and whenever the
  /// widget's dependencies change.
  ///
  /// It returns a [MaterialApp] which is configured to use the [AppRouter]
  /// for navigation and the [NavObserver] for observing route changes.
  /// It also sets the title of the app, disables the debug banner, and sets the
  /// theme to [AppTheme.light].
  Widget build(BuildContext context) {
    final appRouter = sl<AppRouter>();
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => sl<AuthCubit>()),
        BlocProvider(create: (_) => sl<ResetPasswordCubit>()),
        BlocProvider(create: (_) => sl<UnscheduleTaskCubit>()),
        BlocProvider(create: (_) => sl<DashboardCubit>()),
        BlocProvider(create: (_) => sl<NotificationCubit>()),
        BlocProvider(create: (_) => sl<ScheduleTaskCubit>()),
        BlocProvider(create: (_) => sl<TodayPageCubit>()),
        BlocProvider(create: (_) => sl<LeaveCubit>()),
        BlocProvider(create: (_) => sl<AvailabilityCubit>()),
        BlocProvider(create: (_) => sl<SkillsCubit>()),
        BlocProvider(create: (_) => sl<InductionCubit>()),
        BlocProvider(create: (_) => sl<HistoryCubit>()),
        BlocProvider(create: (_) => sl<EditProfileCubit>()),
        BlocProvider(create: (_) => sl<ProfileCubit>()),
        BlocProvider(create: (_) => sl<UsefulLinksCubit>()),
        BlocProvider(create: (_) => sl<AutoScheduleCubit>()),
        BlocProvider(create: (_) => sl<FormRefreshCubit>()),
        BlocProvider(create: (_) => sl<PosCubit>()),
        BlocProvider(create: (_) => sl<VacanciesCubit>()),
        BlocProvider(create: (_) => sl<OpenTasksCubit>()),
      ],
      child: MaterialApp.router(
        routerConfig: appRouter.config(),
        title: 'StoreTrack',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.light,
        scrollBehavior: AppScrollBehavior(),
        builder: (context, child) {
          return GlobalSyncIndicator(
            child: child ?? const SizedBox.shrink(),
          );
        },
      ),
    );
  }
}
