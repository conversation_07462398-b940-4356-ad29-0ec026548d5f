import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:storetrack_app/core/utils/logger.dart';

/// Service for handling camera-related functionality
abstract class CameraService {
  /// Checks if the app has permission to access camera
  Future<bool> hasCameraPermission();

  /// Requests camera permission from the user
  Future<bool> requestCameraPermission();

  /// Checks if camera is available and permission is granted
  Future<bool> isCameraAvailable();

  /// Captures an image from camera
  Future<File?> captureImageFromCamera({int? imageQuality});

  /// Picks an image from gallery
  Future<File?> pickImageFromGallery({int? imageQuality});

  /// Shows image source selection dialog (camera or gallery)
  Future<File?> pickImage(
      {ImageSource? source, int? imageQuality, bool liveImagesOnly = false});
}

/// Implementation of [CameraService]
class CameraServiceImpl implements CameraService {
  final ImagePicker _imagePicker = ImagePicker();

  @override
  Future<bool> hasCameraPermission() async {
    try {
      final permission = await Permission.camera.status;
      return permission == PermissionStatus.granted;
    } catch (e) {
      logger('Error checking camera permission: $e');
      return false;
    }
  }

  @override
  Future<bool> requestCameraPermission() async {
    try {
      final permission = await Permission.camera.request();
      return permission == PermissionStatus.granted;
    } catch (e) {
      logger('Error requesting camera permission: $e');
      return false;
    }
  }

  @override
  Future<bool> isCameraAvailable() async {
    final hasPermission = await hasCameraPermission();
    if (!hasPermission) {
      final permissionGranted = await requestCameraPermission();
      return permissionGranted;
    }
    return true;
  }

  @override
  Future<File?> captureImageFromCamera({int? imageQuality}) async {
    try {
      // final isAvailable = await isCameraAvailable();
      // if (!isAvailable) {
      //   logger('Camera permission not granted');
      //   return null;
      // }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality:
            imageQuality ?? 85, // Use provided quality or default to 85
        maxWidth: 1920,
        maxHeight: 1920,
        requestFullMetadata: false,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      logger('Error capturing image from camera: $e');
      return null;
    }
  }

  @override
  Future<File?> pickImageFromGallery({int? imageQuality}) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality:
            imageQuality ?? 85, // Use provided quality or default to 85
        maxWidth: 1920,
        maxHeight: 1920,
        requestFullMetadata: false,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      logger('Error picking image from gallery: $e');
      return null;
    }
  }

  @override
  Future<File?> pickImage({
    ImageSource? source,
    int? imageQuality,
    bool liveImagesOnly = false,
  }) async {
    // If liveImagesOnly is true, force camera source
    if (liveImagesOnly) {
      return await captureImageFromCamera(imageQuality: imageQuality);
    }

    if (source == ImageSource.camera) {
      return await captureImageFromCamera(imageQuality: imageQuality);
    } else if (source == ImageSource.gallery) {
      return await pickImageFromGallery(imageQuality: imageQuality);
    } else {
      // Default to camera if no source specified
      return await captureImageFromCamera(imageQuality: imageQuality);
    }
  }
}
