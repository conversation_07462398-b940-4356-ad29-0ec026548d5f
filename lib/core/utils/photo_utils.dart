import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/image_storage_utils.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/task_utils.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';

/// Utility class for photo-related database operations
///
/// This class provides reusable methods for managing photo records in the Realm database,
/// including deletion, updates, and local file management operations.
class PhotoUtils {
  /// Completely deletes a photo record from the Realm database
  ///
  /// This method permanently removes the photo record from the database and
  /// optionally deletes the associated local file if it exists.
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task containing the photo
  /// - [photoId]: The ID of the photo to delete (optional, used for server photos)
  /// - [localPath]: The local path of the photo to delete (optional, used for local photos)
  /// - [folderId]: The ID of the specific folder to search in (optional)
  /// - [deleteLocalFile]: Whether to delete the local file if it exists (default: true)
  ///
  /// Returns `true` if deletion was successful, `false` otherwise
  static Future<bool> deletePhotoRecord({
    required int taskId,
    int? photoId,
    String? localPath,
    int? folderId,
    bool deleteLocalFile = true,
  }) async {
    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task
      final task =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      // Search for photo in PhotoFolders
      if (folderId != null) {
        // Search in specific folder
        for (final folder in task.photoFolder) {
          if (folder.folderId == folderId) {
            if (await _deletePhotoFromFolder(
                folder, photoId, localPath, deleteLocalFile)) {
              await TaskUtils.updateTaskPhotosTimestamp(taskId.toString());
              return true;
            }
            break;
          }
        }
      } else {
        // Search in all folders
        for (final folder in task.photoFolder) {
          if (await _deletePhotoFromFolder(
              folder, photoId, localPath, deleteLocalFile)) {
            await TaskUtils.updateTaskPhotosTimestamp(taskId.toString());
            return true;
          }
        }
      }

      logger(
          'Photo not found for deletion - photoId: $photoId, localPath: $localPath');
      return false;
    } catch (e) {
      logger('Error deleting photo record: $e');
      return false;
    }
  }

  /// Helper method to delete photo from a specific folder
  static Future<bool> _deletePhotoFromFolder(
    PhotoFolderModel folder,
    int? photoId,
    String? localPath,
    bool deleteLocalFile,
  ) async {
    final realm = RealmDatabase.instance.realm;

    for (int i = folder.photos.length - 1; i >= 0; i--) {
      final photo = folder.photos[i];
      bool shouldDelete = false;

      // Check if this is the photo to delete
      if (photoId != null && photo.photoId == photoId) {
        shouldDelete = true;
      } else if (localPath != null && photo.localPath == localPath) {
        shouldDelete = true;
      }

      if (shouldDelete) {
        // Delete local file if requested and exists
        if (deleteLocalFile &&
            photo.localPath != null &&
            photo.localPath!.isNotEmpty) {
          if (ImageStorageUtils.isLocalFile(photo.localPath!)) {
            await ImageStorageUtils.deleteImage(photo.localPath!);
          }
        }

        realm.write(() {
          // Permanently delete the record
          folder.photos.removeAt(i);

          // Update folder picture amount
          if (folder.folderPictureAmount != null &&
              folder.folderPictureAmount! > 0) {
            folder.folderPictureAmount = folder.folderPictureAmount! - 1;
          }
          folder.modifiedTimeStampPhototype = DateTime.now();
        });

        logger(
            'Photo record permanently deleted - photoId: ${photo.photoId}, localPath: ${photo.localPath}');
        return true;
      }
    }

    return false;
  }

  /// Updates a photo record with server response data
  ///
  /// This method finds a photo by its local path and updates it with server data,
  /// then optionally deletes the local file to free up storage space.
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task containing the photo
  /// - [localPath]: The local path of the photo to update
  /// - [photoId]: The server photo ID
  /// - [photoUrl]: The server photo URL
  /// - [caption]: The photo caption
  /// - [cannotUploadMandatory]: Whether the photo cannot be uploaded as mandatory
  /// - [modifiedTimeStamp]: The last modified timestamp
  /// - [deleteLocalFile]: Whether to delete the local file after update (default: true)
  ///
  /// Returns `true` if update was successful, `false` otherwise
  static Future<bool> updatePhotoWithServerData({
    required int taskId,
    required String localPath,
    int? photoId,
    String? photoUrl,
    String? caption,
    bool? cannotUploadMandatory,
    DateTime? modifiedTimeStamp,
    bool deleteLocalFile = true,
  }) async {
    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task
      final task =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      // Find the photo with the matching local path
      for (final photoFolder in task.photoFolder) {
        for (final photo in photoFolder.photos) {
          if (photo.localPath == localPath) {
            realm.write(() {
              // Update photo record with server response data
              if (photoId != null) photo.photoId = photoId;
              if (photoUrl != null) photo.photoUrl = photoUrl;
              if (caption != null) photo.caption = caption;
              if (cannotUploadMandatory != null) {
                photo.cannotUploadMandatory = cannotUploadMandatory;
              }
              photo.modifiedTimeStampPhoto =
                  modifiedTimeStamp ?? DateTime.now();

              // Clear local path since file will be deleted
              if (deleteLocalFile) {
                photo.localPath = null;
              }

              // Reset edit flags
              photo.isEdited = false;
              photo.userDeletedPhoto = false;

              // Update folder timestamp
              photoFolder.modifiedTimeStampPhototype = DateTime.now();
            });

            // Delete the local file to free up space
            if (deleteLocalFile && ImageStorageUtils.isLocalFile(localPath)) {
              await ImageStorageUtils.deleteImage(localPath);
            }

            // Update task photos timestamp
            await TaskUtils.updateTaskPhotosTimestamp(taskId.toString());

            logger(
                'Photo updated with server data - photoId: $photoId, localPath: $localPath');
            return true;
          }
        }
      }

      logger('Photo not found for update with local path: $localPath');
      return false;
    } catch (e) {
      logger('Error updating photo with server data: $e');
      return false;
    }
  }

  /// Checks if a photo with the given photoId already exists in a task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to check
  /// - [photoId]: The photo ID to check for
  ///
  /// Returns `true` if photo exists, `false` otherwise
  static Future<bool> photoExists({
    required int taskId,
    required int photoId,
  }) async {
    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task
      final task =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
      if (task == null) {
        return false;
      }

      // Check all photo folders
      for (final photoFolder in task.photoFolder) {
        for (final photo in photoFolder.photos) {
          if (photo.photoId == photoId) {
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      logger('Error checking if photo exists: $e');
      return false;
    }
  }

  /// Finds a photo by local path in a task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to search
  /// - [localPath]: The local path to search for
  ///
  /// Returns the PhotoModel if found, null otherwise
  static Future<PhotoModel?> findPhotoByLocalPath({
    required int taskId,
    required String localPath,
  }) async {
    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task
      final task =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
      if (task == null) {
        return null;
      }

      // Search all photo folders
      for (final photoFolder in task.photoFolder) {
        for (final photo in photoFolder.photos) {
          if (photo.localPath == localPath) {
            return photo;
          }
        }
      }

      return null;
    } catch (e) {
      logger('Error finding photo by local path: $e');
      return null;
    }
  }

  /// Cleans up all soft-deleted photos from the database and local storage
  ///
  /// This method queries all PhotoModel records across all tasks and:
  /// 1. Finds photos where userDeletedPhoto == true
  /// 2. Deletes the local file if localPath exists
  /// 3. Permanently removes the PhotoModel record from Realm database
  /// 4. Updates folder metadata
  ///
  /// Returns a map with cleanup statistics:
  /// - 'deletedCount': Number of photos successfully deleted
  /// - 'errorCount': Number of photos that failed to delete
  /// - 'success': Boolean indicating overall success
  static Future<Map<String, dynamic>> cleanupSoftDeletedPhotos() async {
    try {
      logger('🧹 Starting cleanup of soft-deleted photos');

      final realm = RealmDatabase.instance.realm;

      // Query all tasks to find soft-deleted photos
      final allTasks = realm.all<TaskDetailModel>();

      int deletedCount = 0;
      int errorCount = 0;

      for (final task in allTasks) {
        for (final photoFolder in task.photoFolder) {
          // Find photos marked for deletion
          final photosToDelete = photoFolder.photos
              .where((photo) => photo.userDeletedPhoto == true)
              .toList();

          for (final photo in photosToDelete) {
            try {
              // Delete local file if it exists
              if (photo.localPath != null && photo.localPath!.isNotEmpty) {
                if (ImageStorageUtils.isLocalFile(photo.localPath!)) {
                  final fileDeleted =
                      await ImageStorageUtils.deleteImage(photo.localPath!);
                  if (fileDeleted) {
                    logger('🗑️ Deleted local file: ${photo.localPath}');
                  }
                }
              }

              // Permanently remove the photo record from database
              realm.write(() {
                photoFolder.photos.remove(photo);

                // Update folder picture amount
                if (photoFolder.folderPictureAmount != null &&
                    photoFolder.folderPictureAmount! > 0) {
                  photoFolder.folderPictureAmount =
                      photoFolder.folderPictureAmount! - 1;
                }
                photoFolder.modifiedTimeStampPhototype = DateTime.now();
              });

              deletedCount++;
              logger('🗑️ Permanently deleted photo record: ${photo.photoId}');
            } catch (e) {
              errorCount++;
              logger('❌ Error deleting photo ${photo.photoId}: $e');
            }
          }
        }
      }

      logger('🧹 Cleanup completed: $deletedCount deleted, $errorCount errors');

      return {
        'deletedCount': deletedCount,
        'errorCount': errorCount,
        'success': errorCount == 0 || deletedCount > 0,
      };
    } catch (e) {
      logger('❌ Error in photo cleanup: $e');
      return {
        'deletedCount': 0,
        'errorCount': 1,
        'success': false,
      };
    }
  }
}
