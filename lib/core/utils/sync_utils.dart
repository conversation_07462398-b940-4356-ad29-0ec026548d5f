import 'dart:convert';
import 'dart:io';

import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/photo_utils.dart';
import 'package:storetrack_app/core/utils/signature_utils.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_photo_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_photo_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_sign_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_signature_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/sync_pic_request_entity.dart'
    as sync_pic;
import 'package:storetrack_app/features/home/<USER>/entities/sync_sign_request_entity.dart'
    as sync_sign;
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart'
    as submit_report;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/di/service_locator.dart';

/// Utility class for sync-related operations
///
/// This class provides helper methods for creating API request entities
/// and processing data transformations needed for the four new sync endpoints:
///
/// 1. `/send_task_pic_v4_11` - Upload individual photos
/// 2. `/sync_pic_info_mpt` - Sync photo metadata after all uploads
/// 3. `/send_task_sig_v4_11` - Upload individual signatures
/// 4. `/sync_sig_info` - Sync signature metadata after all uploads
///
/// Usage example:
/// ```dart
/// // 1. Upload individual photos first
/// final photosToUpload = SyncUtils.getPhotosToUpload(tasks);
/// for (final photoData in photosToUpload) {
///   final request = await SyncUtils.createUploadPhotoRequest(
///     photo: photoData['photo'],
///     taskId: photoData['taskId'],
///     folderId: photoData['folderId'],
///   );
///   final result = await homeRepository.uploadPhoto(request);
/// }
///
/// // 2. Then sync photo metadata
/// final syncRequest = await SyncUtils.createSyncPicInfoRequest(tasks: tasks);
/// final syncResult = await homeRepository.syncPhotoInfo(syncRequest);
/// ```
class SyncUtils {
  static const String deviceUid = "8b7a6774c878a206";

  /// Encode file to base64 string
  static Future<String> encodeFileToBase64(String filePath) async {
    final file = File(filePath);
    final bytes = await file.readAsBytes();
    final base64String = base64Encode(bytes);
    return base64String;
  }

  /// Create upload photo request entity from photo data
  static Future<UploadPhotoRequestEntity> createUploadPhotoRequest({
    required Photo photo,
    required int taskId,
    required int folderId,
  }) async {
    final dataManager = sl<DataManager>();

    final request = UploadPhotoRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = int.parse(await dataManager.getUserId() ?? "0");
    request.taskId = taskId;
    request.folderId = folderId;
    request.photoId = photo.photoId?.toInt();
    request.photoDate = null;
    request.photoCaption = photo.caption;
    request.cannotUploadMandatory = photo.cannotUploadMandatory;
    request.formId = photo.formId?.toInt();
    request.questionId = photo.questionId?.toInt();
    request.measurementId = photo.measurementId?.toInt();
    request.questionpartId = photo.questionpartId?.toInt();
    request.questionPartMultiId = photo.questionPartMultiId;
    request.measurementPhototypeId = photo.measurementPhototypeId?.toInt();
    request.deviceuid = deviceUid;

    if (photo.cannotUploadMandatory == true) {
      request.pictureBlob = "-1";
    } else {
      // Use localPath if available, otherwise use photoUrl
      final imagePath = photo.localPath ?? photo.photoUrl ?? "";
      request.pictureBlob = await encodeFileToBase64(imagePath);
    }

    return request;
  }

  /// Create upload signature request entity from signature data
  static Future<UploadSignRequestEntity> createUploadSignatureRequest({
    required Signature signature,
    required int taskId,
    required int folderId,
  }) async {
    final dataManager = sl<DataManager>();

    final request = UploadSignRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = int.parse(await dataManager.getUserId() ?? "0");
    request.taskId = taskId;
    request.folderId = folderId;
    request.signatureId = signature.signatureId?.toInt();
    request.signedBy = signature.signedBy;
    request.cannotUploadMandatory = signature.cannotUploadMandatory;
    request.formId = signature.formId?.toInt();
    request.questionId = signature.questionId?.toInt();
    request.deviceuid = deviceUid;

    if (signature.cannotUploadMandatory == true) {
      request.signatureBlob = "-1";
    } else {
      // Use localPath if available, otherwise use signatureUrl
      final signaturePath = signature.localPath ?? signature.signatureUrl ?? "";
      request.signatureBlob = await encodeFileToBase64(signaturePath);
    }

    return request;
  }

  /// Create sync pic info request entity for photo metadata sync
  static Future<sync_pic.SyncPicInfoRequestEntity> createSyncPicInfoRequest({
    required List<TaskDetail> tasks,
  }) async {
    final dataManager = sl<DataManager>();

    final syncPicRequest = sync_pic.SyncPicInfoRequestEntity();
    final List<sync_pic.Task> tasksSync = [];

    for (var task in tasks) {
      final List<sync_pic.PhotoFolder> photoFolderList = [];

      for (var photoFolder in task.photoFolder ?? []) {
        final deletePhotosIds = <int>[];

        for (var photo in photoFolder.photos ?? []) {
          if (photo.userDeletedPhoto == true) {
            deletePhotosIds.add(photo.photoId?.toInt() ?? 0);
          }
        }

        if (deletePhotosIds.isNotEmpty) {
          photoFolderList.add(sync_pic.PhotoFolder(
            folderId: photoFolder.folderId?.toInt(),
            deletePhotosIds: deletePhotosIds.join(","),
          ));
        }
      }

      if (photoFolderList.isNotEmpty) {
        tasksSync.add(sync_pic.Task(
          taskId: task.taskId?.toInt(),
          uploadPhotosSuccess: true,
          modifiedTimeStampPhotos: task.modifiedTimeStampPhotos,
          photoFolder: photoFolderList,
        ));
      }
    }

    syncPicRequest.tasks = tasksSync;
    syncPicRequest.token = await dataManager.getAuthToken();
    syncPicRequest.userId = int.parse(await dataManager.getUserId() ?? "0");
    syncPicRequest.deviceuid = deviceUid;

    return syncPicRequest;
  }

  /// Create sync signature info request entity for signature metadata sync
  static Future<sync_sign.SyncSignInfoRequestEntity>
      createSyncSignatureInfoRequest({
    required List<TaskDetail> tasks,
  }) async {
    final dataManager = sl<DataManager>();

    final syncSignRequest = sync_sign.SyncSignInfoRequestEntity();
    final List<sync_sign.Task> tasksSync = [];

    for (var task in tasks) {
      final List<sync_sign.SignatureFolder> signatureFolderList = [];

      for (var signatureFolder in task.signatureFolder ?? []) {
        final deleteSignatureIds = <int>[];

        for (var signature in signatureFolder.signatures ?? []) {
          if (signature.userDeletedSignature == true) {
            deleteSignatureIds.add(signature.signatureId?.toInt() ?? 0);
          }
        }

        if (deleteSignatureIds.isNotEmpty) {
          signatureFolderList.add(sync_sign.SignatureFolder(
            folderId: signatureFolder.folderId?.toInt(),
            deleteSignaturesIds: deleteSignatureIds.join(","),
          ));
        }
      }

      if (signatureFolderList.isNotEmpty) {
        tasksSync.add(sync_sign.Task(
          taskId: task.taskId?.toInt(),
          uploadSignatureSuccess: true,
          modifiedTimeStampSignatures: task.modifiedTimeStampSignatures,
          signatureFolder: signatureFolderList,
        ));
      }
    }

    syncSignRequest.tasks = tasksSync;
    syncSignRequest.token = await dataManager.getAuthToken();
    syncSignRequest.userId = int.parse(await dataManager.getUserId() ?? "0");
    syncSignRequest.deviceuid = deviceUid;

    return syncSignRequest;
  }

  /// Get all photos that need to be uploaded from tasks
  static List<Map<String, dynamic>> getPhotosToUpload(List<TaskDetail> tasks) {
    final List<Map<String, dynamic>> photosToUpload = [];

    for (var task in tasks) {
      for (var photoFolder in task.photoFolder ?? []) {
        for (var photo in photoFolder.photos ?? []) {
          // Only include photos that haven't been deleted and have content
          if (photo.userDeletedPhoto != true &&
              (photo.photoUrl != null || photo.localPath != null)) {
            photosToUpload.add({
              'photo': photo,
              'taskId': task.taskId?.toInt() ?? 0,
              'folderId': photoFolder.folderId?.toInt() ?? 0,
            });
          }
        }
      }
    }

    return photosToUpload;
  }

  /// Get all signatures that need to be uploaded from tasks
  static List<Map<String, dynamic>> getSignaturesToUpload(
      List<TaskDetail> tasks) {
    final List<Map<String, dynamic>> signaturesToUpload = [];

    for (var task in tasks) {
      for (var signatureFolder in task.signatureFolder ?? []) {
        for (var signature in signatureFolder.signatures ?? []) {
          // Only include signatures that haven't been deleted and have content
          if (signature.userDeletedSignature != true &&
              (signature.signatureUrl != null || signature.localPath != null)) {
            signaturesToUpload.add({
              'signature': signature,
              'taskId': task.taskId?.toInt() ?? 0,
              'folderId': signatureFolder.folderId?.toInt() ?? 0,
            });
          }
        }
      }
    }

    return signaturesToUpload;
  }

  /// Check if a given `photoId` already exists inside any of the photo folders
  /// of the provided [task].
  ///
  /// The API that handles `/api/send_task_pic_v4_11` returns the definitive
  /// `photoId` generated by the server.  When the same image is uploaded twice
  /// (user selects the same image again, or device retries a previous upload),
  /// the backend may respond with an **existing** `photoId` instead of a newly
  /// created one.  This helper makes it easy to detect that situation so that
  /// callers can decide to ignore the duplicate or clean up the temporary
  /// local record.
  ///
  /// Returns `true` if any photo inside `task.photoFolder` has `photoId` equal
  /// to [serverPhotoId]; otherwise returns `false`.
  static bool isDuplicatePhoto({
    required TaskDetail task,
    required int serverPhotoId,
  }) {
    if (serverPhotoId == 0) return false;

    for (final photoFolder in task.photoFolder ?? []) {
      for (final photo in photoFolder.photos ?? []) {
        if ((photo.photoId ?? 0).toInt() == serverPhotoId) {
          return true;
        }
      }
    }
    return false;
  }

  /// Handles photo upload response with duplicate detection and local storage optimization
  ///
  /// This method processes the server response from photo upload API and:
  /// 1. Checks if the uploaded photo is a duplicate (already exists in database)
  /// 2. If duplicate: Deletes the duplicate photo record from database
  /// 3. If not duplicate: Updates the local photo record with server data and deletes local file
  ///
  /// Parameters:
  /// - [uploadResponse]: The response from the photo upload API
  /// - [taskId]: The ID of the task containing the photo
  /// - [originalLocalPath]: The original local path of the uploaded photo (for matching)
  ///
  /// Returns `true` if processing was successful, `false` otherwise
  static Future<bool> handlePhotoUploadResponse({
    required UploadPhotoResponseEntity uploadResponse,
    required int taskId,
    required String originalLocalPath,
  }) async {
    try {
      final serverPhoto = uploadResponse.data;
      if (serverPhoto == null) {
        logger('Invalid server photo response: data is null');
        return false;
      }

      final serverPhotoId = serverPhoto.photoId?.toInt() ?? 0;

      if (serverPhotoId == 0) {
        logger('Invalid server photo ID received');
        return false;
      }

      // Check for duplicate photo using PhotoUtils
      final isDuplicate = await PhotoUtils.photoExists(
        taskId: taskId,
        photoId: serverPhotoId,
      );

      if (isDuplicate) {
        // Handle duplicate: delete the duplicate photo record
        logger(
            'Duplicate photo detected with ID: $serverPhotoId, deleting local record');
        return await PhotoUtils.deletePhotoRecord(
          taskId: taskId,
          localPath: originalLocalPath,
          deleteLocalFile: true,
        );
      } else {
        // Handle successful upload: update photo record and delete local file
        logger('Updating photo record with server data for ID: $serverPhotoId');
        return await PhotoUtils.updatePhotoWithServerData(
          taskId: taskId,
          localPath: originalLocalPath,
          photoId: serverPhotoId,
          photoUrl: serverPhoto.photoUrl,
          caption: serverPhoto.photoCaption,
          cannotUploadMandatory: serverPhoto.cannotUploadMandatory,
          modifiedTimeStamp: serverPhoto.modifiedTimeStampPhoto,
          deleteLocalFile: true,
        );
      }
    } catch (e) {
      logger('Error handling photo upload response: $e');
      return false;
    }
  }

  /// Handles signature upload response with duplicate detection and local storage optimization
  ///
  /// This method processes the server response from signature upload API and:
  /// 1. Checks if the uploaded signature is a duplicate (already exists in database)
  /// 2. If duplicate: Deletes the duplicate signature record from database
  /// 3. If not duplicate: Updates the local signature record with server data and deletes local file
  ///
  /// Parameters:
  /// - [uploadResponse]: The response from the signature upload API
  /// - [taskId]: The ID of the task containing the signature
  /// - [originalLocalPath]: The original local path of the uploaded signature (for matching)
  ///
  /// Returns `true` if processing was successful, `false` otherwise
  static Future<bool> handleSignatureUploadResponse({
    required UploadSignatureResponseEntity uploadResponse,
    required int taskId,
    required String originalLocalPath,
  }) async {
    try {
      final serverSignature = uploadResponse.data;
      if (serverSignature == null) {
        logger('Invalid server signature response: data is null');
        return false;
      }

      final serverSignatureId = serverSignature.signatureId?.toInt() ?? 0;

      if (serverSignatureId == 0) {
        logger('Invalid server signature ID received');
        return false;
      }

      // Check for duplicate signature using SignatureUtils
      final isDuplicate = await SignatureUtils.signatureExists(
        taskId: taskId,
        signatureId: serverSignatureId,
      );

      if (isDuplicate) {
        // Handle duplicate: delete the duplicate signature record
        logger(
            'Duplicate signature detected with ID: $serverSignatureId, deleting local record');
        return await SignatureUtils.deleteSignatureRecord(
          taskId: taskId,
          localPath: originalLocalPath,
          deleteLocalFile: true,
        );
      } else {
        // Handle successful upload: update signature record and delete local file
        logger(
            'Updating signature record with server data for ID: $serverSignatureId');
        return await SignatureUtils.updateSignatureWithServerData(
          taskId: taskId,
          localPath: originalLocalPath,
          signatureId: serverSignatureId,
          signatureUrl: serverSignature.signatureUrl,
          signedBy: serverSignature.signedBy,
          cannotUploadMandatory: serverSignature.cannotUploadMandatory,
          modifiedTimeStamp: serverSignature.modifiedTimeStampSignature,
          deleteLocalFile: true,
        );
      }
    } catch (e) {
      logger('Error handling signature upload response: $e');
      return false;
    }
  }

  /// Create submit report request entity from task data
  ///
  /// This method prepares the request body for the submit report API,
  /// taking inspiration from the submitReportData() method in syncing.dart.
  /// It structures the request with all necessary task data including forms,
  /// question answers, and followup tasks.
  ///
  /// Parameters:
  /// - [task]: The task detail containing all data to be submitted
  ///
  /// Returns a properly structured SubmitReportRequestEntity
  static Future<submit_report.SubmitReportRequestEntity>
      createSubmitReportRequest({
    required TaskDetail task,
  }) async {
    final dataManager = sl<DataManager>();
    const String actualDeviceUid = "8b7a6774c878a206";
    const String actualAppVersion = "9.9.9";

    final request = submit_report.SubmitReportRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = await dataManager.getUserId();
    request.deviceUid = actualDeviceUid;
    request.appversion = actualAppVersion;
    request.taskId = task.taskId.toString();
    request.comment = task.comment;
    request.minutes = task.minutes?.toInt();
    request.timerMinutes = 0;
    request.claimableKms = task.claimableKms?.toInt();
    request.pages = task.pages?.toInt();
    request.taskStatus = task.taskStatus;
    request.submissionState = task.submissionState?.toInt();
    request.taskCommencementTimeStamp = task.taskCommencementTimeStamp;
    request.taskStoppedTimeStamp = task.taskStoppedTimeStamp;
    request.scheduledTimeStamp = task.scheduledTimeStamp;
    request.submissionTimeStamp = DateTime.now();
    request.startTaskLatitude = task.taskLatitude?.toInt();
    request.startTaskLongitude = task.taskLongitude?.toInt();
    request.taskLatitude = task.latitude?.toInt();
    request.taskLongitude = task.longitude?.toInt();
    request.budgetCalculated = 0;

    // Initialize lists
    request.forms = [];
    request.followupTasks = [];
    request.resumePauseItems = [];

    // Process forms and question answers
    for (var form in task.forms ?? []) {
      var formPost = Form();
      formPost.formId = form.formId;
      formPost.questionAnswers = [];

      for (var questionAnswer in form.questionAnswers ?? []) {
        if ((questionAnswer.isComment == false) ||
            (questionAnswer.isComment == true &&
                questionAnswer.measurementTextResult != null)) {
          var questionAnswerPost = QuestionAnswer(
            questionId: questionAnswer.questionId,
            questionpartId: questionAnswer.questionpartId,
            questionPartMultiId: questionAnswer.questionPartMultiId,
            measurementId: questionAnswer.measurementId,
            measurementTypeId: questionAnswer.measurementTypeId,
            measurementOptionId: questionAnswer.measurementOptionId,
            measurementOptionIds: questionAnswer.measurementOptionIds,
            measurementTextResult: questionAnswer.measurementTextResult,
            isComment: questionAnswer.isComment,
            commentTypeId: questionAnswer.commentTypeId,
          );
          formPost.questionAnswers?.add(questionAnswerPost);
        }
      }

      if (formPost.questionAnswers?.isNotEmpty == true) {
        request.forms?.add(formPost);
      }
    }

    // Process followup tasks
    for (var followupTask in task.followupTasks ?? []) {
      var followupTaskPost = submit_report.FollowupTask();
      followupTaskPost.taskId = task.taskId.toString();
      followupTaskPost.visitDate = followupTask.selectedVisitDate;
      followupTaskPost.followupTypeId = 0;
      followupTaskPost.followupItemId = 0;
      followupTaskPost.budget = followupTask.selectedBudget?.toInt();
      followupTaskPost.scheduleNote = followupTask.selectedScheduleNote;
      followupTaskPost.followupNumber = followupTask.followupNumber?.toInt();
      request.followupTasks?.add(followupTaskPost);
    }

    return request;
  }

  /// Get all tasks that need to be submitted (have sync pending status)
  ///
  /// Returns a list of TaskDetail entities that are marked for sync
  static List<TaskDetail> getTasksToSubmit() {
    final realm = RealmDatabase.instance.realm;
    final taskModels = realm.all<TaskDetailModel>();

    // Convert models to entities
    final tasks =
        taskModels.map((model) => TaskDetailMapper.toEntity(model)).toList();

    return tasks.where((task) => task.syncPending == true).toList();
  }

  /// Prepare tasks array for getTasks API request
  ///
  /// This method replicates the exact request body preparation logic from
  /// downloadTaskDataSimplified() method in lib/syncing.dart. It creates
  /// a list of Task objects with all the required timestamp and metadata
  /// properties needed for the getTasks API endpoint.
  ///
  /// Returns a list of Task objects formatted exactly as in downloadTaskDataSimplified()
  static Future<List<Task>> prepareTasksForGetTasks() async {
    final realm = RealmDatabase.instance.realm;
    final taskModels = realm.all<TaskDetailModel>();
    final tasksPost =
        taskModels.map((e) => TaskDetailMapper.toEntity(e)).toList();

    List<Task> taskList = [];
    for (var task in tasksPost) {
      var taskItem = Task();
      taskItem.taskId = task.taskId?.toString();
      taskItem.scheduledTimeStamp = task.scheduledTimeStamp;
      taskItem.submissionTimeStamp = task.submissionTimeStamp;
      taskItem.modifiedTimeStampTask = task.modifiedTimeStampTask;
      taskItem.modifiedTimeStampPhotos = task.modifiedTimeStampPhotos;
      taskItem.modifiedTimeStampSignatures = task.modifiedTimeStampSignatures;
      taskItem.modifiedTimeStampSignaturetypes =
          task.modifiedTimeStampSignaturetypes;
      taskItem.modifiedTimeStampPhototypes = task.modifiedTimeStampPhototypes;
      taskItem.modifiedTimeStampForms = task.modifiedTimeStampForms;
      taskItem.modifiedTimeStampDocuments = task.modifiedTimeStampDocuments;
      taskItem.phototypeIds = [];
      taskItem.forceImportFollowupTask = false;
      taskList.add(taskItem);
    }

    return taskList;
  }

  /// Process TasksResponseEntity according to process_core.md specifications
  ///
  /// This method handles all response fields from the getTasks API and processes them
  /// according to the exact specifications outlined in process_core.md:
  /// - delete_task_ids: Process task deletion operations
  /// - update_tasks_documents: Process document updates
  /// - update_tasks_forms: Process form updates
  /// - update_task_members: Process member/helper updates
  /// - update_tasks_tasks: Process task updates
  /// - update_tasks_photos: Process photo updates
  /// - add_tasks: Process new task additions
  /// - update_tasks_submission: Process submission updates
  /// - update_tasks_signatures: Process signature updates
  ///
  /// Parameters:
  /// - [response]: The TasksResponseEntity from the API
  ///
  /// Returns `true` if processing was successful, `false` otherwise
  static Future<bool> processTasksResponse(TasksResponseEntity response) async {
    try {
      logger('🔄 Starting TasksResponse processing...');

      // Process in the exact order specified in process_core.md

      // (11-10) Process tasks to be deleted
      await _processDeleteTaskIds(response.deleteTaskIds);

      // (11-2) Update task documents data
      await _processUpdateTasksDocuments(response.updateTasksDocuments);

      // (11-3) Update tasks forms data
      await _processUpdateTasksForms(response.updateTasksForms);

      // (11-6) Update tasks member data
      await _processUpdateTaskMembers(response.updateTaskMembers);

      // (11-6) Update tasks information data
      await _processUpdateTasksTasks(response.updateTasksTasks);

      // (11-4) Update tasks photos data
      await _processUpdateTasksPhotos(response.updateTasksPhotos);

      // (11-1) Add new tasks
      await _processAddTasks(response.addTasks);

      // (11-5) Update task submission data (moved to end as per process_core.md)
      await _processUpdateTasksSubmission(response.updateTasksSubmission);

      // Update tasks signatures data
      await _processUpdateTasksSignatures(response.updateTasksSignatures);

      logger('✅ TasksResponse processing completed successfully');
      return true;
    } catch (e) {
      logger('❌ Error processing TasksResponse: $e');
      return false;
    }
  }

  /// Process delete_task_ids array - Delete tasks from local database
  ///
  /// Following process_core.md specification (11-10):
  /// - Iterate through delete_task_ids array
  /// - Find and delete corresponding TaskDetailModel from Realm
  ///
  /// Parameters:
  /// - [deleteTaskIds]: List of task IDs to delete
  static Future<void> _processDeleteTaskIds(List<String>? deleteTaskIds) async {
    if (deleteTaskIds == null || deleteTaskIds.isEmpty) {
      logger('No tasks to delete');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;
      logger('Processing ${deleteTaskIds.length} tasks for deletion');

      realm.write(() {
        for (final taskId in deleteTaskIds) {
          if (taskId.isNotEmpty) {
            final taskIdInt = int.tryParse(taskId);
            if (taskIdInt != null) {
              final task = realm.query<TaskDetailModel>(
                'taskId == \$0',
                [taskIdInt],
              ).firstOrNull;

              if (task != null) {
                logger('Deleting task with ID: $taskId');
                realm.delete(task);
              } else {
                logger('Task not found for deletion: $taskId');
              }
            }
          }
        }
      });

      logger('✅ Task deletion processing completed');
    } catch (e) {
      logger('❌ Error processing task deletions: $e');
      rethrow;
    }
  }

  /// Process update_tasks_documents array - Update document data for tasks
  ///
  /// Following process_core.md specification (11-2):
  /// - Update task documents data
  /// - Compare file modification dates and delete outdated local files
  /// - Replace document collections with server data
  ///
  /// Parameters:
  /// - [updateTasksDocuments]: List of TaskDetail entities with document updates
  static Future<void> _processUpdateTasksDocuments(
      List<TaskDetail>? updateTasksDocuments) async {
    if (updateTasksDocuments == null || updateTasksDocuments.isEmpty) {
      logger('No document updates to process');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;
      logger('Processing ${updateTasksDocuments.length} document updates');

      realm.write(() {
        for (final serverTask in updateTasksDocuments) {
          final taskIdInt = serverTask.taskId?.toInt();
          if (taskIdInt == null) continue;

          final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0',
            [taskIdInt],
          ).firstOrNull;

          if (localTask != null) {
            logger('Updating documents for task: ${serverTask.taskId}');

            // Clear existing documents
            localTask.documents.clear();

            // Add new documents from server
            if (serverTask.documents != null) {
              for (final document in serverTask.documents!) {
                final documentModel = TaskDetailMapper.toModel(
                  TaskDetail(documents: [document]),
                  0,
                ).documents.first;
                localTask.documents.add(documentModel);
              }
            }

            // Update timestamp
            localTask.modifiedTimeStampDocuments =
                serverTask.modifiedTimeStampDocuments;
          }
        }
      });

      logger('✅ Document updates processing completed');
    } catch (e) {
      logger('❌ Error processing document updates: $e');
      rethrow;
    }
  }

  /// Process update_tasks_forms array - Update form data for tasks
  ///
  /// Following process_core.md specification (11-3):
  /// - Update form structures and metadata
  /// - Update form completion counts
  /// - Preserve existing form answers (only structure changes)
  /// - Remove deleted forms
  ///
  /// Parameters:
  /// - [updateTasksForms]: List of TaskDetail entities with form updates
  static Future<void> _processUpdateTasksForms(
      List<TaskDetail>? updateTasksForms) async {
    if (updateTasksForms == null || updateTasksForms.isEmpty) {
      logger('No form updates to process');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;
      logger('Processing ${updateTasksForms.length} form updates');

      realm.write(() {
        for (final serverTask in updateTasksForms) {
          final taskIdInt = serverTask.taskId?.toInt();
          if (taskIdInt == null) continue;

          final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0',
            [taskIdInt],
          ).firstOrNull;

          if (localTask != null) {
            logger('Updating forms for task: ${serverTask.taskId}');

            // Update form counts
            localTask.ctFormsTotalCnt = serverTask.ctFormsTotalCnt?.toInt();
            localTask.ctFormsCompletedCnt =
                serverTask.ctFormsCompletedCnt?.toInt();

            // Update forms structure (preserving answers)
            if (serverTask.forms != null) {
              for (final serverForm in serverTask.forms!) {
                final existingForm = localTask.forms
                    .where((f) => f.formId == serverForm.formId?.toInt())
                    .firstOrNull;

                if (existingForm != null) {
                  // Update existing form structure
                  existingForm.formName = serverForm.formName;
                  existingForm.briefUrl = serverForm.briefUrl;
                  existingForm.visionFormUrl = serverForm.visionFormUrl;
                  existingForm.isVisionForm = serverForm.isVisionForm;
                  existingForm.isMandatory = serverForm.isMandatory;
                  existingForm.modifiedTimeStampForm =
                      serverForm.modifiedTimeStampForm;
                  existingForm.formAllowForward = serverForm.formAllowForward;

                  // Clear and update questions (structure only)
                  existingForm.questions.clear();
                  if (serverForm.questions != null) {
                    for (final question in serverForm.questions!) {
                      final questionModel = TaskDetailMapper.toModel(
                        TaskDetail(forms: [
                          Form(questions: [question])
                        ]),
                        0,
                      ).forms.first.questions.first;
                      existingForm.questions.add(questionModel);
                    }
                  }
                } else {
                  // Add new form
                  final formModel = TaskDetailMapper.toModel(
                    TaskDetail(forms: [serverForm]),
                    0,
                  ).forms.first;
                  localTask.forms.add(formModel);
                }
              }

              // Remove deleted forms
              final formsToRemove = <FormModel>[];
              for (final localForm in localTask.forms) {
                final stillExists = serverTask.forms!
                    .any((serverForm) => serverForm.formId == localForm.formId);
                if (!stillExists) {
                  formsToRemove.add(localForm);
                }
              }
              for (final formToRemove in formsToRemove) {
                localTask.forms.remove(formToRemove);
              }
            }

            // Update timestamp
            localTask.modifiedTimeStampForms =
                serverTask.modifiedTimeStampForms;
          }
        }
      });

      logger('✅ Form updates processing completed');
    } catch (e) {
      logger('❌ Error processing form updates: $e');
      rethrow;
    }
  }

  /// Process update_task_members array - Update task member data
  ///
  /// Following process_core.md specification (11-6):
  /// - Update task members/helpers for each task
  /// - Replace existing members with server data
  ///
  /// Parameters:
  /// - [updateTaskMembers]: List of TaskMembersHelper entities with member updates
  static Future<void> _processUpdateTaskMembers(
      List<TaskMembersHelper>? updateTaskMembers) async {
    if (updateTaskMembers == null || updateTaskMembers.isEmpty) {
      logger('No member updates to process');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;
      logger('Processing ${updateTaskMembers.length} member updates');

      realm.write(() {
        for (final helper in updateTaskMembers) {
          final taskIdInt = helper.taskId;
          if (taskIdInt == null) continue;

          final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0',
            [taskIdInt],
          ).firstOrNull;

          if (localTask != null) {
            logger('Updating members for task: ${helper.taskId}');

            // Clear existing members
            localTask.taskmembers.clear();

            // Add new members from server
            if (helper.taskMembers != null) {
              for (final member in helper.taskMembers!) {
                final memberModel = TaskDetailMapper.toModel(
                  TaskDetail(taskmembers: [member]),
                  0,
                ).taskmembers.first;
                localTask.taskmembers.add(memberModel);
              }
            }
          }
        }
      });

      logger('✅ Member updates processing completed');
    } catch (e) {
      logger('❌ Error processing member updates: $e');
      rethrow;
    }
  }

  /// Process update_tasks_tasks array - Update task information data
  ///
  /// Following process_core.md specification (11-6):
  /// - Update task basic information (client, store, cycle, etc.)
  /// - Update task status and completion state
  /// - Clear sync pending status
  /// - Update POS items and followup tasks
  ///
  /// Parameters:
  /// - [updateTasksTasks]: List of TaskDetail entities with task updates
  static Future<void> _processUpdateTasksTasks(
      List<TaskDetail>? updateTasksTasks) async {
    if (updateTasksTasks == null || updateTasksTasks.isEmpty) {
      logger('No task information updates to process');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;
      logger('Processing ${updateTasksTasks.length} task information updates');

      realm.write(() {
        for (final serverTask in updateTasksTasks) {
          final taskIdInt = serverTask.taskId?.toInt();
          if (taskIdInt == null) continue;

          final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0',
            [taskIdInt],
          ).firstOrNull;

          if (localTask != null) {
            logger('Updating task information for: ${serverTask.taskId}');

            // Update client information
            localTask.clientId = serverTask.clientId?.toInt();
            localTask.client = serverTask.client;
            localTask.clientLogoUrl = serverTask.clientLogoUrl;

            // Update store information
            localTask.storeId = serverTask.storeId?.toInt();
            localTask.storeGroupId = serverTask.storeGroupId?.toInt();
            localTask.storeGroup = serverTask.storeGroup;
            localTask.storeName = serverTask.storeName;
            localTask.location = serverTask.location;
            localTask.suburb = serverTask.suburb;
            localTask.phone = serverTask.phone;
            localTask.latitude = serverTask.latitude?.toDouble();
            localTask.longitude = serverTask.longitude?.toDouble();

            // Update cycle information
            localTask.cycleId = serverTask.cycleId?.toInt();
            localTask.cycle = serverTask.cycle;

            // Update budget and task details
            localTask.budget = serverTask.budget?.toInt();
            localTask.reOpened = serverTask.reOpened;
            localTask.reOpenedReason = serverTask.reOpenedReason;
            localTask.taskStatus = serverTask.taskStatus;

            // Update dates
            localTask.rangeStart = serverTask.rangeStart;
            localTask.rangeEnd = serverTask.rangeEnd;
            localTask.scheduledTimeStamp = serverTask.scheduledTimeStamp;
            localTask.expires = serverTask.expires;
            localTask.connoteUrl = serverTask.connoteUrl;

            // Update task completion state (isOpen = false when completed)
            final isCompleted = (serverTask.taskStatus == 'Successful' ||
                serverTask.taskStatus == 'Unsuccessful');
            localTask.isOpen = !isCompleted;

            // Clear sync status
            localTask.syncPending = false;
            localTask.isSynced = true;

            // Update POS items
            localTask.posItems.clear();
            if (serverTask.posItems != null) {
              for (final posItem in serverTask.posItems!) {
                final posItemModel = TaskDetailMapper.toModel(
                  TaskDetail(posItems: [posItem]),
                  0,
                ).posItems.first;
                localTask.posItems.add(posItemModel);
              }
            }

            // Update followup tasks
            localTask.followupTasks.clear();
            if (serverTask.followupTasks != null) {
              for (final followupTask in serverTask.followupTasks!) {
                final followupTaskModel = TaskDetailMapper.toModel(
                  TaskDetail(followupTasks: [followupTask]),
                  0,
                ).followupTasks.first;
                localTask.followupTasks.add(followupTaskModel);
              }
            }

            // Update followup flags
            localTask.showFollowupIconMulti = serverTask.showFollowupIconMulti;
            localTask.followupSelectedMulti = serverTask.followupSelectedMulti;

            // Update task note and reschedule settings
            localTask.taskNote = serverTask.taskNote;
            localTask.disallowReschedule = serverTask.disallowReschedule;

            // Update POS settings
            localTask.posRequired = serverTask.posRequired;
            localTask.photoResPerc = serverTask.photoResPerc?.toInt();
            localTask.liveImagesOnly = serverTask.liveImagesOnly;

            // Update timestamp
            localTask.modifiedTimeStampTask = serverTask.modifiedTimeStampTask;
          }
        }
      });

      logger('✅ Task information updates processing completed');
    } catch (e) {
      logger('❌ Error processing task information updates: $e');
      rethrow;
    }
  }

  /// Process update_tasks_photos array - Update photo data for tasks
  ///
  /// Following process_core.md specification (11-4):
  /// - Update photo information and metadata
  /// - Handle photo synchronization and conflicts
  /// - Preserve unsynced photos to avoid data loss
  ///
  /// Parameters:
  /// - [updateTasksPhotos]: List of TaskDetail entities with photo updates
  static Future<void> _processUpdateTasksPhotos(
      List<TaskDetail>? updateTasksPhotos) async {
    if (updateTasksPhotos == null || updateTasksPhotos.isEmpty) {
      logger('No photo updates to process');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;
      logger('Processing ${updateTasksPhotos.length} photo updates');

      realm.write(() {
        for (final serverTask in updateTasksPhotos) {
          final taskIdInt = serverTask.taskId?.toInt();
          if (taskIdInt == null) continue;

          final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0',
            [taskIdInt],
          ).firstOrNull;

          if (localTask != null) {
            logger('Updating photos for task: ${serverTask.taskId}');

            // Process photo folders and photos
            if (serverTask.photoFolder != null) {
              for (final serverPhotoFolder in serverTask.photoFolder!) {
                final localPhotoFolder = localTask.photoFolder
                    .where((f) =>
                        f.folderId == serverPhotoFolder.folderId?.toInt())
                    .firstOrNull;

                if (localPhotoFolder != null &&
                    serverPhotoFolder.photos != null) {
                  for (final serverPhoto in serverPhotoFolder.photos!) {
                    final localPhoto = localPhotoFolder.photos
                        .where((p) => p.photoId == serverPhoto.photoId?.toInt())
                        .firstOrNull;

                    if (localPhoto != null) {
                      // Update existing photo if server has newer data
                      if (serverPhoto.modifiedTimeStampPhoto != null &&
                          (localPhoto.modifiedTimeStampPhoto == null ||
                              serverPhoto.modifiedTimeStampPhoto!.isAfter(
                                  localPhoto.modifiedTimeStampPhoto!))) {
                        localPhoto.photoUrl = serverPhoto.photoUrl;
                        localPhoto.thumbnailUrl = serverPhoto.thumbnailUrl;
                        localPhoto.caption = serverPhoto.caption;
                        localPhoto.modifiedTimeStampPhoto =
                            serverPhoto.modifiedTimeStampPhoto;
                        localPhoto.cannotUploadMandatory =
                            serverPhoto.cannotUploadMandatory;
                        localPhoto.imageRec = serverPhoto.imageRec;
                        // Clear local path since we have server URL
                        localPhoto.localPath = null;
                      }
                    } else {
                      // Add new photo from server
                      final photoModel = TaskDetailMapper.toModel(
                        TaskDetail(photoFolder: [
                          PhotoFolder(photos: [serverPhoto])
                        ]),
                        0,
                      ).photoFolder.first.photos.first;
                      localPhotoFolder.photos.add(photoModel);
                    }
                  }
                }
              }
            }

            // Update timestamp
            localTask.modifiedTimeStampPhotos =
                serverTask.modifiedTimeStampPhotos;
          }
        }
      });

      logger('✅ Photo updates processing completed');
    } catch (e) {
      logger('❌ Error processing photo updates: $e');
      rethrow;
    }
  }

  /// Process add_tasks array - Add new tasks to local database
  ///
  /// Following process_core.md specification (11-1):
  /// - Add new tasks from server to local database
  /// - Use copyToRealmOrUpdate for proper Realm handling
  ///
  /// Parameters:
  /// - [addTasks]: List of TaskDetail entities to add
  static Future<void> _processAddTasks(List<TaskDetail>? addTasks) async {
    if (addTasks == null || addTasks.isEmpty) {
      logger('No new tasks to add');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;
      logger('Processing ${addTasks.length} new tasks');

      realm.write(() {
        for (final serverTask in addTasks) {
          final taskIdInt = serverTask.taskId?.toInt();
          if (taskIdInt == null) continue;

          logger('Adding new task: ${serverTask.taskId}');

          // Check if task already exists
          final existingTask = realm.query<TaskDetailModel>(
            'taskId == \$0',
            [taskIdInt],
          ).firstOrNull;

          if (existingTask == null) {
            // Create new task model
            final taskModel = TaskDetailMapper.toModel(serverTask, taskIdInt);
            realm.add(taskModel);
          } else {
            logger('Task ${serverTask.taskId} already exists, skipping');
          }
        }
      });

      logger('✅ New tasks processing completed');
    } catch (e) {
      logger('❌ Error processing new tasks: $e');
      rethrow;
    }
  }

  /// Process update_tasks_submission array - Update task submission data
  ///
  /// Following process_core.md specification (11-5):
  /// - Update form answers and submission data
  /// - Update task completion counts
  /// - Update task status and submission details
  /// - This is processed last as per process_core.md
  ///
  /// Parameters:
  /// - [updateTasksSubmission]: List of TaskDetail entities with submission updates
  static Future<void> _processUpdateTasksSubmission(
      List<TaskDetail>? updateTasksSubmission) async {
    if (updateTasksSubmission == null || updateTasksSubmission.isEmpty) {
      logger('No submission updates to process');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;
      logger('Processing ${updateTasksSubmission.length} submission updates');

      realm.write(() {
        for (final serverTask in updateTasksSubmission) {
          final taskIdInt = serverTask.taskId?.toInt();
          if (taskIdInt == null) continue;

          final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0',
            [taskIdInt],
          ).firstOrNull;

          if (localTask != null) {
            logger('Updating submission for task: ${serverTask.taskId}');

            // Update form counts
            localTask.ctFormsTotalCnt = serverTask.ctFormsTotalCnt?.toInt();
            localTask.ctFormsCompletedCnt =
                serverTask.ctFormsCompletedCnt?.toInt();

            // Update task submission details
            localTask.comment = serverTask.comment;
            localTask.minutes = serverTask.minutes?.toInt();
            localTask.claimableKms = serverTask.claimableKms?.toInt();
            localTask.pages = serverTask.pages?.toInt();
            localTask.taskStatus = serverTask.taskStatus;
            localTask.submissionState = 0; // TASK_SUBMISSION_STATE_DEFAULT

            localTask.scheduledTimeStamp = serverTask.scheduledTimeStamp;
            localTask.submissionTimeStamp = serverTask.submissionTimeStamp;

            // Update task coordinates
            localTask.taskLatitude = serverTask.taskLatitude?.toDouble();
            localTask.taskLongitude = serverTask.taskLongitude?.toDouble();

            // Update form answers
            if (serverTask.forms != null) {
              for (final serverForm in serverTask.forms!) {
                final existingForm = localTask.forms
                    .where((f) => f.formId == serverForm.formId?.toInt())
                    .firstOrNull;

                if (existingForm != null) {
                  // Clear and update question answers
                  existingForm.questionAnswers.clear();
                  if (serverForm.questionAnswers != null) {
                    for (final answer in serverForm.questionAnswers!) {
                      final answerModel = TaskDetailMapper.toModel(
                        TaskDetail(forms: [
                          Form(questionAnswers: [answer])
                        ]),
                        0,
                      ).forms.first.questionAnswers.first;
                      existingForm.questionAnswers.add(answerModel);
                    }
                  }

                  // Update form completion status
                  existingForm.formCompleted = serverForm.formCompleted;
                }
              }
            }

            // Update task completion state
            final isCompleted = (serverTask.taskStatus == 'Successful' ||
                serverTask.taskStatus == 'Unsuccessful');
            localTask.isOpen = !isCompleted;

            // Clear sync status
            localTask.syncPending = false;
            localTask.isSynced = true;
          }
        }
      });

      logger('✅ Submission updates processing completed');
    } catch (e) {
      logger('❌ Error processing submission updates: $e');
      rethrow;
    }
  }

  /// Process update_tasks_signatures array - Update signature data for tasks
  ///
  /// Following process_core.md specification:
  /// - Update signature information and metadata
  /// - Handle signature synchronization and conflicts
  /// - Preserve unsynced signatures to avoid data loss
  ///
  /// Parameters:
  /// - [updateTasksSignatures]: List of TaskDetail entities with signature updates
  static Future<void> _processUpdateTasksSignatures(
      List<TaskDetail>? updateTasksSignatures) async {
    if (updateTasksSignatures == null || updateTasksSignatures.isEmpty) {
      logger('No signature updates to process');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;
      logger('Processing ${updateTasksSignatures.length} signature updates');

      realm.write(() {
        for (final serverTask in updateTasksSignatures) {
          final taskIdInt = serverTask.taskId?.toInt();
          if (taskIdInt == null) continue;

          final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0',
            [taskIdInt],
          ).firstOrNull;

          if (localTask != null) {
            logger('Updating signatures for task: ${serverTask.taskId}');

            // Process signature folders and signatures
            if (serverTask.signatureFolder != null) {
              for (final serverSignatureFolder in serverTask.signatureFolder!) {
                final localSignatureFolder = localTask.signatureFolder
                    .where((f) =>
                        f.folderId == serverSignatureFolder.folderId?.toInt())
                    .firstOrNull;

                if (localSignatureFolder != null &&
                    serverSignatureFolder.signatures != null) {
                  for (final serverSignature
                      in serverSignatureFolder.signatures!) {
                    final localSignature = localSignatureFolder.signatures
                        .where((s) =>
                            s.signatureId ==
                            serverSignature.signatureId?.toInt())
                        .firstOrNull;

                    if (localSignature != null) {
                      // Update existing signature if server has newer data
                      if (serverSignature.modifiedTimeStampSignature != null &&
                          (localSignature.modifiedTimeStampSignature == null ||
                              serverSignature.modifiedTimeStampSignature!
                                  .isAfter(localSignature
                                      .modifiedTimeStampSignature!))) {
                        localSignature.signatureUrl =
                            serverSignature.signatureUrl;
                        localSignature.signedBy = serverSignature.signedBy;
                        localSignature.modifiedTimeStampSignature =
                            serverSignature.modifiedTimeStampSignature;
                        localSignature.cannotUploadMandatory =
                            serverSignature.cannotUploadMandatory;
                        // Clear local path since we have server URL
                        localSignature.localPath = null;
                      }
                    } else {
                      // Add new signature from server
                      final signatureModel = TaskDetailMapper.toModel(
                        TaskDetail(signatureFolder: [
                          SignatureFolder(signatures: [serverSignature])
                        ]),
                        0,
                      ).signatureFolder.first.signatures.first;
                      localSignatureFolder.signatures.add(signatureModel);
                    }
                  }
                }
              }
            }

            // Update timestamp
            localTask.modifiedTimeStampSignatures =
                serverTask.modifiedTimeStampSignatures;
          }
        }
      });

      logger('✅ Signature updates processing completed');
    } catch (e) {
      logger('❌ Error processing signature updates: $e');
      rethrow;
    }
  }

}
