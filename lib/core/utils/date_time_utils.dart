import 'package:timezone/data/latest.dart' as tzdata;
import 'package:timezone/timezone.dart' as tz;
import 'package:intl/intl.dart';

/// Returns a human-readable string representing the time elapsed since [dateTime].
///
/// Examples:
/// - "just now" (less than a minute ago)
/// - "1 minute ago" (1-59 minutes ago)
/// - "2 hours ago" (1-23 hours ago)
/// - "1 day ago" (1-6 days ago)
/// - "1 week ago" (1-3 weeks ago)
/// - "1 month ago" (1-11 months ago)
/// - "1 year ago" (1+ years ago)
///
/// If [dateTime] is in the future, returns "in the future".
String getTimeAgo(DateTime dateTime) {
  final now = DateTime.now();
  final difference = now.difference(dateTime);

  // Handle future dates
  if (difference.isNegative) {
    return 'in the future';
  }

  // Less than a minute
  if (difference.inSeconds < 60) {
    return 'just now';
  }

  // Less than an hour
  if (difference.inMinutes < 60) {
    final minutes = difference.inMinutes;
    return '$minutes ${minutes == 1 ? 'minute' : 'minutes'} ago';
  }

  // Less than a day
  if (difference.inHours < 24) {
    final hours = difference.inHours;
    return '$hours ${hours == 1 ? 'hour' : 'hours'} ago';
  }

  // Less than a week
  if (difference.inDays < 7) {
    final days = difference.inDays;
    return '$days ${days == 1 ? 'day' : 'days'} ago';
  }

  // Less than a month (approximately)
  if (difference.inDays < 30) {
    final weeks = (difference.inDays / 7).floor();
    return '$weeks ${weeks == 1 ? 'week' : 'weeks'} ago';
  }

  // Less than a year
  if (difference.inDays < 365) {
    final months = (difference.inDays / 30).floor();
    return '$months ${months == 1 ? 'month' : 'months'} ago';
  }

  // More than a year
  final years = (difference.inDays / 365).floor();
  return '$years ${years == 1 ? 'year' : 'years'} ago';
}

/// A utility class for handling date and time operations,
/// specifically focusing on the Australia/Sydney timezone.
class DateTimeUtils {
  // Private constructor to prevent instantiation
  DateTimeUtils._();

  // The IANA timezone identifier for Sydney (NSW)
  static const String _sydneyTimezoneIdentifier = 'Australia/Sydney';

  // The default locale for formatting
  static const String _defaultLocale = 'en_AU';

  /// Initializes the timezone database.
  /// This method MUST be called once, typically at the start of your application
  /// (e.g., in the `main` function before `runApp`).
  static void initializeTimeZones() {
    tzdata.initializeTimeZones();
  }

  /// Gets the current UTC time and converts it to the Australia/Sydney timezone.
  ///
  /// Returns a [tz.TZDateTime] object representing the current time in Sydney.
  ///
  /// Throws [tz.LocationNotFoundException] if the Sydney timezone data
  /// is not available (ensure [initializeTimeZones] has been called).
  static tz.TZDateTime getCurrentTimeInSydney() {
    // Get the current time in UTC
    DateTime nowUtc = DateTime.now().toUtc();

    // Find the timezone location for Sydney
    try {
      tz.Location sydneyLocation = tz.getLocation(_sydneyTimezoneIdentifier);

      // Convert the UTC time to the Sydney timezone
      return tz.TZDateTime.from(nowUtc, sydneyLocation);
    } catch (e) {
      // Rethrow the exception or handle it as appropriate for your application
      print('Error getting Sydney timezone location: $e');
      rethrow; // It's often better to let the caller handle this error
    }
  }

  /// Gets the current time in the Australia/Sydney timezone and formats it.
  ///
  /// [format]: The date/time format pattern string (e.g., 'yyyy-MM-dd HH:mm:ss Z').
  ///           If null or omitted, defaults to 'yyyy-MM-dd HH:mm:ss Z'.
  /// [locale]: The locale for formatting (defaults to 'en_AU').
  ///
  /// Returns a formatted string representing the current time in Sydney.
  ///
  /// Throws [tz.LocationNotFoundException] if the Sydney timezone data
  /// is not available (ensure [initializeTimeZones] has been called).
  /// Throws [FormatException] or other errors if the format pattern is invalid.
  static String getFormattedSydneyCurrentTime({
    String? format,
    String locale = _defaultLocale,
  }) {
    // Get the current time in Sydney
    tz.TZDateTime nowSydney = getCurrentTimeInSydney();

    // Define the default format pattern
    const String defaultFormat =
        'yyyy-MM-dd HH:mm:ss Z'; // Example format with offset

    // Determine the format pattern to use
    final formatPattern = format ?? defaultFormat;

    // Format the time using intl with the specified locale
    final formatter = DateFormat(formatPattern, locale);
    return formatter.format(nowSydney);
  }

  /// Calculates the week commencing (Monday) date for a given day offset.
  /// Similar to Android Calendar logic.
  ///
  /// [dayOffset]: Number of days to add to current week (0 = current week, 7 = next week, etc.)
  ///
  /// Returns a [DateTime] representing the Monday of the target week.
  static DateTime getWeekCommencingDate(int dayOffset) {
    final now = DateTime.now();

    // Get the day of the week (1 = Monday, 7 = Sunday)
    final dayOfWeek = now.weekday;

    // Calculate offset to Monday of current week
    // If today is Monday (1), offset is 0
    // If today is Tuesday (2), offset is -1
    // If today is Sunday (7), offset is -6
    final mondayOffset = 1 - dayOfWeek;

    // Get Monday of current week
    final currentWeekMonday = now.add(Duration(days: mondayOffset));

    // Add the day offset to get the target week's Monday
    final targetWeekMonday = currentWeekMonday.add(Duration(days: dayOffset));

    // Return date only (without time)
    return DateTime(
        targetWeekMonday.year, targetWeekMonday.month, targetWeekMonday.day);
  }

  /// Formats a week commencing date for display in tabs.
  ///
  /// [date]: The DateTime to format
  ///
  /// Returns a formatted string like "Jun 23" for tab display.
  static String formatWeekCommencingForTab(DateTime date) {
    return DateFormat('MMM d').format(date);
  }

  // You can add other utility methods here if needed,
  // e.g., methods to format a given TZDateTime object,
  // compare times in different zones, etc.
}
