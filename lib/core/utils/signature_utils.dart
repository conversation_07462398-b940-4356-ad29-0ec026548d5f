import 'dart:io';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';

/// Utility class for signature-related database operations
///
/// This class provides reusable methods for managing signature records in the Realm database,
/// including deletion, updates, and local file management operations.
class SignatureUtils {
  /// Completely deletes a signature record from the Realm database
  ///
  /// This method permanently removes the signature record from the database and
  /// optionally deletes the associated local file if it exists.
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task containing the signature
  /// - [signatureId]: The ID of the signature to delete (optional, used for server signatures)
  /// - [localPath]: The local path of the signature to delete (optional, used for local signatures)
  /// - [folderId]: The ID of the specific folder to search in (optional)
  /// - [deleteLocalFile]: Whether to delete the local file (default: true)
  ///
  /// Returns `true` if deletion was successful, `false` otherwise
  static Future<bool> deleteSignatureRecord({
    required int taskId,
    int? signatureId,
    String? localPath,
    int? folderId,
    bool deleteLocalFile = true,
  }) async {
    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task
      final task =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      bool signatureFound = false;
      String? filePathToDelete;

      // Search through signature folders
      for (var signatureFolder in task.signatureFolder) {
        if (folderId != null && signatureFolder.folderId != folderId) {
          continue; // Skip if specific folder ID is provided and doesn't match
        }

        // Find and remove the signature
        final signaturesToRemove = <SignatureModel>[];
        for (var signature in signatureFolder.signatures) {
          bool shouldRemove = false;

          if (signatureId != null && signature.signatureId == signatureId) {
            shouldRemove = true;
          } else if (localPath != null && signature.localPath == localPath) {
            shouldRemove = true;
          }

          if (shouldRemove) {
            signatureFound = true;
            if (deleteLocalFile && signature.localPath != null) {
              filePathToDelete = signature.localPath;
            }
            signaturesToRemove.add(signature);
          }
        }

        // Remove signatures from the folder
        if (signaturesToRemove.isNotEmpty) {
          realm.write(() {
            for (var signature in signaturesToRemove) {
              signatureFolder.signatures.remove(signature);
              realm.delete(signature);
            }
          });
        }
      }

      // Delete local file if requested and path exists
      if (deleteLocalFile && filePathToDelete != null) {
        try {
          final file = File(filePathToDelete);
          if (await file.exists()) {
            await file.delete();
            logger('Deleted local signature file: $filePathToDelete');
          }
        } catch (e) {
          logger('Error deleting local signature file: $e');
          // Don't return false here as the database operation might have succeeded
        }
      }

      if (signatureFound) {
        logger('Successfully deleted signature record(s) for task: $taskId');
        return true;
      } else {
        logger('No signature found to delete for task: $taskId');
        return false;
      }
    } catch (e) {
      logger('Error deleting signature record: $e');
      return false;
    }
  }

  /// Checks if a signature exists in the database
  ///
  /// This method searches for a signature by its ID across all signature folders
  /// in the specified task.
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to search in
  /// - [signatureId]: The ID of the signature to check for
  ///
  /// Returns `true` if signature exists, `false` otherwise
  static Future<bool> signatureExists({
    required int taskId,
    required int signatureId,
  }) async {
    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task
      final task =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
      if (task == null) {
        return false;
      }

      // Search through all signature folders
      for (var signatureFolder in task.signatureFolder) {
        for (var signature in signatureFolder.signatures) {
          if (signature.signatureId == signatureId) {
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      logger('Error checking signature existence: $e');
      return false;
    }
  }

  /// Updates a signature record with server response data
  ///
  /// This method finds a signature by its local path and updates it with server data,
  /// then optionally deletes the local file to free up storage space.
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task containing the signature
  /// - [localPath]: The local path of the signature to update
  /// - [signatureId]: The server signature ID
  /// - [signatureUrl]: The server signature URL
  /// - [signedBy]: The person who signed
  /// - [cannotUploadMandatory]: Whether the signature cannot be uploaded as mandatory
  /// - [modifiedTimeStamp]: The last modified timestamp
  /// - [deleteLocalFile]: Whether to delete the local file after update (default: true)
  ///
  /// Returns `true` if update was successful, `false` otherwise
  static Future<bool> updateSignatureWithServerData({
    required int taskId,
    required String localPath,
    int? signatureId,
    String? signatureUrl,
    String? signedBy,
    bool? cannotUploadMandatory,
    DateTime? modifiedTimeStamp,
    bool deleteLocalFile = true,
  }) async {
    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task
      final task =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      bool signatureFound = false;

      // Search through signature folders to find the signature with matching localPath
      for (var signatureFolder in task.signatureFolder) {
        for (var signature in signatureFolder.signatures) {
          if (signature.localPath == localPath) {
            signatureFound = true;

            // Update the signature with server data
            realm.write(() {
              if (signatureId != null) signature.signatureId = signatureId;
              if (signatureUrl != null) signature.signatureUrl = signatureUrl;
              if (signedBy != null) signature.signedBy = signedBy;
              if (cannotUploadMandatory != null) {
                signature.cannotUploadMandatory = cannotUploadMandatory;
              }
              if (modifiedTimeStamp != null) {
                signature.modifiedTimeStampSignature = modifiedTimeStamp;
              }

              // Clear local-only properties since we now have server data
              if (deleteLocalFile) {
                signature.localPath = null;
              }
              signature.userDeletedSignature = false;
              signature.isEdited = false;
            });

            logger(
                'Updated signature record with server data for signature ID: $signatureId');
            break;
          }
        }
        if (signatureFound) break;
      }

      // Delete local file if requested and signature was found
      if (deleteLocalFile && signatureFound) {
        try {
          final file = File(localPath);
          if (await file.exists()) {
            await file.delete();
            logger('Deleted local signature file: $localPath');
          }
        } catch (e) {
          logger('Error deleting local signature file: $e');
          // Don't return false here as the database update succeeded
        }
      }

      if (signatureFound) {
        logger(
            'Successfully updated signature with server data for task: $taskId');
        return true;
      } else {
        logger(
            'No signature found with localPath: $localPath for task: $taskId');
        return false;
      }
    } catch (e) {
      logger('Error updating signature with server data: $e');
      return false;
    }
  }

  /// Cleans up all soft-deleted signatures from the database and local storage
  ///
  /// This method queries all SignatureModel records across all tasks and:
  /// 1. Finds signatures where userDeletedSignature == true
  /// 2. Deletes the local file if localPath exists
  /// 3. Permanently removes the SignatureModel record from Realm database
  /// 4. Updates folder metadata
  ///
  /// Returns a map with cleanup statistics:
  /// - 'deletedCount': Number of signatures successfully deleted
  /// - 'errorCount': Number of signatures that failed to delete
  /// - 'success': Boolean indicating overall success
  static Future<Map<String, dynamic>> cleanupSoftDeletedSignatures() async {
    try {
      logger('🧹 Starting cleanup of soft-deleted signatures');

      final realm = RealmDatabase.instance.realm;
      int deletedCount = 0;
      int errorCount = 0;

      // Get all tasks
      final tasks = realm.all<TaskDetailModel>();

      for (var task in tasks) {
        for (var signatureFolder in task.signatureFolder) {
          // Find signatures marked for deletion
          final signaturesToDelete = signatureFolder.signatures
              .where((signature) => signature.userDeletedSignature == true)
              .toList();

          for (var signature in signaturesToDelete) {
            try {
              // Delete local file if it exists
              if (signature.localPath != null) {
                final file = File(signature.localPath!);
                if (await file.exists()) {
                  await file.delete();
                  logger(
                      'Deleted local signature file: ${signature.localPath}');
                }
              }

              // Remove from database
              realm.write(() {
                signatureFolder.signatures.remove(signature);
                realm.delete(signature);
              });

              deletedCount++;
              logger(
                  'Cleaned up signature ID: ${signature.signatureId} from task: ${task.taskId}');
            } catch (e) {
              errorCount++;
              logger(
                  'Error cleaning up signature ID: ${signature.signatureId}: $e');
            }
          }
        }
      }

      final success = errorCount == 0;
      logger(
          '✅ Signature cleanup completed. Deleted: $deletedCount, Errors: $errorCount');

      return {
        'deletedCount': deletedCount,
        'errorCount': errorCount,
        'success': success,
      };
    } catch (e) {
      logger('❌ Error during signature cleanup: $e');
      return {
        'deletedCount': 0,
        'errorCount': 1,
        'success': false,
      };
    }
  }
}
