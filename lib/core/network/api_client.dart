import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter/foundation.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

import '../storage/token_manager.dart';
import 'network_config.dart';
import 'network_info.dart';

/// Network client
class ApiClient {
  /// Create network client
  factory ApiClient({
    required NetworkConfig config,
    required TokenManager tokenManager,
    required NetworkInfo networkInfo,
  }) {
    return _instance ??= ApiClient._(
      config: config,
      tokenManager: tokenManager,
      networkInfo: networkInfo,
    );
  }

  ApiClient._({
    required this.config,
    required this.tokenManager,
    required this.networkInfo,
  }) {
    _dio = _createDio();
  }
  static ApiClient? _instance;

  /// Network configuration
  final NetworkConfig config;

  /// Token manager
  final TokenManager tokenManager;

  /// Network info
  final NetworkInfo networkInfo;

  late final Dio _dio;

  /// Get dio instance
  Dio get instance => _dio;

  /// Create dio instance with configuration
  Dio _createDio() {
    final dio = Dio(
      BaseOptions(
        baseUrl: config.baseUrl,
        connectTimeout: config.connectTimeout,
        receiveTimeout: config.receiveTimeout,
        headers: {'Content-Type': 'application/json'},
      ),
    );

    dio.interceptors.addAll(_createInterceptors(dio));
    return dio;
  }

  /// Create interceptors for dio
  List<Interceptor> _createInterceptors(Dio dio) {
    final interceptors = <Interceptor>[];

    // Add logging interceptor in debug mode
    if (kDebugMode) {
      interceptors.add(
        PrettyDioLogger(
          requestBody: true,
          responseBody: false,
          requestHeader: true,
        ),
      );
    }

    // Add retry interceptor
    interceptors
      ..add(
        RetryInterceptor(
          dio: dio,
          logPrint: debugPrint,
          retries: config.maxRetries,
          retryDelays: [config.retryDelay],
          retryableExtraStatuses: {400},
        ),
      )

      // Add connectivity interceptor
      ..add(
        InterceptorsWrapper(
          onRequest: (options, handler) async {
            try {
              if (!await networkInfo.isConnected) {
                return handler.reject(
                  DioException(
                    requestOptions: options,
                    type: DioExceptionType.connectionError,
                    error: Exception(
                      'No internet connection.',
                    ),
                  ),
                );
              }
              return handler.next(options);
            } catch (e) {
              return handler.reject(
                DioException(
                  requestOptions: options,
                  type: DioExceptionType.unknown,
                  error: e,
                ),
              );
            }
          },
        ),
      )

      // Add authentication interceptor
      ..add(
        QueuedInterceptorsWrapper(
          onRequest: (options, handler) async {
            try {
              // Set the Authorization header with the access token
              final accessToken = await tokenManager.getAccessToken();
              if (accessToken != null) {
                options.headers['Authorization'] = "Bearer $accessToken";
              }
              return handler.next(options);
            } catch (e) {
              return handler.reject(
                DioException(
                  requestOptions: options,
                  type: DioExceptionType.unknown,
                  error: e,
                ),
              );
            }
          },
          onResponse: (response, handler) {
            return handler.next(response);
          },
          onError: (DioException e, handler) async {
            // Token refresh logic
            if (e.response != null && e.response?.statusCode == 401) {
              try {
                final refToken = await tokenManager.getAccessToken() ?? "";
                final accessToken = await tokenManager.getAccessToken() ?? "";
                final refreshResult = await refreshToken(refToken, accessToken);

                // if (refreshResult == null ||
                //     refreshResult.data?.accessToken == null ||
                //     refreshResult.data?.refreshToken == null) {

                // }

                // await tokenManager.saveTokens(
                //   accessToken: refreshResult.data!.accessToken!,
                //   refreshToken: refreshResult.data!.refreshToken!,
                // );

                // Retry the original request after token refresh
                final response = await _dio.request(
                  e.requestOptions.path,
                  options: Options(
                    method: e.requestOptions.method,
                    headers: {'Authorization': 'Bearer '},
                  ),
                  cancelToken: e.requestOptions.cancelToken,
                  onReceiveProgress: e.requestOptions.onReceiveProgress,
                  data: e.requestOptions.data,
                  queryParameters: e.requestOptions.queryParameters,
                );
                return handler.resolve(response);
              } catch (error) {
                return handler.reject(
                  DioException(
                    requestOptions: e.requestOptions,
                    type: DioExceptionType.unknown,
                    error: error,
                  ),
                );
              }
            } else if (e.type == DioExceptionType.connectionTimeout ||
                e.type == DioExceptionType.sendTimeout ||
                e.type == DioExceptionType.receiveTimeout) {
              // Handle timeout errors
              return handler.reject(
                DioException(
                  requestOptions: e.requestOptions,
                  type: e.type,
                  error: Exception('Connection timed out. Please try again.'),
                ),
              );
            } else if (e.type == DioExceptionType.connectionError) {
              // Handle connection errors
              return handler.reject(
                DioException(
                  requestOptions: e.requestOptions,
                  type: e.type,
                  error: Exception(
                      'Connection error. Please check your internet connection.'),
                ),
              );
            } else {
              return handler.next(e);
            }
          },
        ),
      );
    return interceptors;
  }

  /// Close dio client
  void dispose() {
    _dio.close();
    _instance = null;
  }

  Future<void> refreshToken(
    String refreshToken,
    String accessToken,
  ) async {
    final params = {
      "refresh_token": refreshToken,
      "access_token": accessToken,
    };

    final refreshDio = Dio(
      BaseOptions(
        connectTimeout: config.connectTimeout,
        receiveTimeout: config.receiveTimeout,
        headers: {'Content-Type': 'application/json'},
      ),
    );

    try {
      final response = await refreshDio.post(
        "${config.baseUrl}/app/user/token",
        data: params,
        options: Options(
          validateStatus: (status) => status == 200,
          headers: {'Content-Type': 'application/json'},
        ),
      );

      if (response.data == null) {}

      // return RefreshTokenResponse.fromJson(response.data);
    } on DioException catch (e) {
      String errorMessage;

      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          errorMessage = 'Connection timed out while refreshing token';
          break;
        case DioExceptionType.badResponse:
          errorMessage =
              'Server error: ${e.response?.statusCode} - ${e.response?.statusMessage}';
          break;
        case DioExceptionType.connectionError:
          errorMessage = 'Connection error while refreshing token';
          break;
        default:
          errorMessage = e.message ?? 'Unknown network error';
      }

      // throw AuthenticationException(errorMessage);
    } catch (e) {
      // info('Error refreshing token: $e');
      // throw AuthenticationException(e.toString());
    } finally {
      refreshDio.close();
    }
  }
}
