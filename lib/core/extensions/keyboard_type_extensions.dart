import 'package:flutter/material.dart';

extension KeyboardTypeExtensions on num {
  // 1 - Decimal
  // 2 - Text
  // 7 - Number
  TextInputType get keyboardType {
    switch (this) {
      case 1:
        return const TextInputType.numberWithOptions(decimal: true);
      case 2:
        return TextInputType.text;
      case 7:
        return TextInputType.number;
      default:
        return TextInputType.text;
    }
  }
}
