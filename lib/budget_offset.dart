import 'package:realm/realm.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';

/// BudgetOffset class to represent budget offset data
class BudgetOffset {
  String? formId;
  String? questionId;
  String? measurementId;
  String? measurementOptionId;
  String? budgetOffset;
  String? budgetOffsetType;

  /// Default constructor
  BudgetOffset();

  /// Constructor with parameters
  BudgetOffset.withParams({
    required this.formId,
    required this.questionId,
    required this.measurementId,
    required this.measurementOptionId,
    required this.budgetOffset,
    required this.budgetOffsetType,
  });

  @override
  String toString() {
    return 'BudgetOffset - fid: $formId, qid: $questionId, mid: $measurementId, moid: $measurementOptionId, offset: $budgetOffset, offsetType: $budgetOffsetType';
  }
}

/// Global variable to track if budget has changed
bool budgetHasChanged = false;

/// Helper function to get TaskDetailModel by taskId
TaskDetailModel? getTaskDetailModelWithTaskId(
    Realm realm, String taskId, bool detailed) {
  try {
    final taskIdInt = int.tryParse(taskId);
    if (taskIdInt == null) return null;

    return realm
        .query<TaskDetailModel>('taskId == \$0', [taskIdInt]).firstOrNull;
  } catch (e) {
    logger('Error getting task detail model: $e');
    return null;
  }
}

/// Helper function to determine if budget offset should be applied
int shouldApplyBudgetOffset(
    QuestionAnswerModel answerModel, List<BudgetOffset> budgetOffsetStructure) {
  for (final budgetOffset in budgetOffsetStructure) {
    if (budgetOffset.formId == answerModel.formId?.toString() &&
        budgetOffset.questionId == answerModel.questionId?.toString() &&
        budgetOffset.measurementId == answerModel.measurementId?.toString() &&
        budgetOffset.measurementOptionId ==
            answerModel.measurementOptionId?.toString() &&
        budgetOffset.budgetOffsetType == '1') {
      return int.tryParse(budgetOffset.budgetOffset ?? '0') ?? 0;
    }
  }
  return 0;
}

/// Main function to calculate budget core
void calculateBudgetCore(Realm realm, String taskId) {
  try {
    final taskDetailModel = getTaskDetailModelWithTaskId(realm, taskId, false);
    if (taskDetailModel == null) {
      logger('Task not found with ID: $taskId');
      return;
    }

    final List<BudgetOffset> totalBudgetOffsetStructure = [];
    List<BudgetOffset> totalBudgetOffsetStructureType1;
    final List<BudgetOffset> totalBudgetOffsetStructureType2 = [];
    int tmpBudget = taskDetailModel.budget ?? 0;
    final int originalBudget = taskDetailModel.budget ?? 0;

    int index = 0;

    // (1) structure
    for (final formModel in taskDetailModel.forms) {
      if (formModel.formCompleted == true) {
        if (formModel.questions.isNotEmpty) {
          for (final questionModel in formModel.questions) {
            if (questionModel.isComment != true) {
              // Not-comment question
              index++;

              for (final measurementModel in questionModel.measurements) {
                for (final optionModel in measurementModel.measurementOptions) {
                  // Check for budget offset type 1
                  if (optionModel.budgetOffset != null &&
                      optionModel.budgetOffset != 0 &&
                      optionModel.budgetOffsetType != null &&
                      optionModel.budgetOffsetType == 1) {
                    // Has budget offset
                    final String fid = formModel.formId?.toString() ?? '';
                    final String qid =
                        questionModel.questionId?.toString() ?? '';
                    final String mid =
                        measurementModel.measurementId?.toString() ?? '';
                    final String moid =
                        optionModel.measurementOptionId?.toString() ?? '';
                    final String offset =
                        optionModel.budgetOffset?.toString() ?? '';
                    final String offsetType =
                        optionModel.budgetOffsetType?.toString() ?? '';

                    // Print debug information
                    logger(
                        'fid: $fid, ${formModel.formName}, qid: $qid, ${questionModel.questionDescription}, mid: $mid, ${measurementModel.measurementDescription}, moid: $moid, ${optionModel.measurementOptionDescription}, offset: $offset, offsetType: $offsetType');

                    final budgetOffset = BudgetOffset.withParams(
                      formId: fid,
                      questionId: qid,
                      measurementId: mid,
                      measurementOptionId: moid,
                      budgetOffset: offset,
                      budgetOffsetType: offsetType,
                    );
                    totalBudgetOffsetStructure.add(budgetOffset);
                  }

                  // Check for budget offset type 2
                  if (optionModel.budgetOffset != null &&
                      optionModel.budgetOffset != 0 &&
                      optionModel.budgetOffsetType != null &&
                      optionModel.budgetOffsetType == 2) {
                    // Has budget offset
                    final String fid = formModel.formId?.toString() ?? '';
                    final String qid =
                        questionModel.questionId?.toString() ?? '';
                    final String mid =
                        measurementModel.measurementId?.toString() ?? '';
                    final String moid =
                        optionModel.measurementOptionId?.toString() ?? '';
                    final String offset =
                        optionModel.budgetOffset?.toString() ?? '';
                    final String offsetType =
                        optionModel.budgetOffsetType?.toString() ?? '';

                    // Print debug information
                    logger(
                        'fid: $fid, ${formModel.formName}, qid: $qid, ${questionModel.questionDescription}, mid: $mid, ${measurementModel.measurementDescription}, moid: $moid, ${optionModel.measurementOptionDescription}, offset: $offset, offsetType: $offsetType');
                    logger('TMPBUDGET2: $tmpBudget');
                    logger(
                        '----------- INDEX : SIZE ---------- $index ${formModel.questions.length}');

                    final budgetOffset = BudgetOffset.withParams(
                      formId: fid,
                      questionId: qid,
                      measurementId: mid,
                      measurementOptionId: moid,
                      budgetOffset: offset,
                      budgetOffsetType: offsetType,
                    );
                    totalBudgetOffsetStructureType2.add(budgetOffset);
                  }
                }
              }
            }
          }
        }
      }
    }

    logger('----------- answers ----------');
    totalBudgetOffsetStructureType1 = List.from(totalBudgetOffsetStructure);
    totalBudgetOffsetStructure.addAll(totalBudgetOffsetStructureType2);

    // (2) answers of all forms
    for (final formModel in taskDetailModel.forms) {
      if (formModel.formCompleted == true ||
          formModel.formAllowForward == true) {
        if (formModel.questionAnswers.isNotEmpty) {
          for (final answerModel in formModel.questionAnswers) {
            logger('Answer Model: $answerModel');
            final String fid = answerModel.formId?.toString() ?? '';
            final String qid = answerModel.questionId?.toString() ?? '';
            final String mid = answerModel.measurementId?.toString() ?? '';
            final String moid =
                answerModel.measurementOptionId?.toString() ?? '';

            int offset = 0;

            for (final objTmp in totalBudgetOffsetStructure) {
              if (objTmp.formId == fid &&
                  objTmp.questionId == qid &&
                  objTmp.measurementId == mid &&
                  objTmp.measurementOptionId == moid &&
                  objTmp.budgetOffsetType == '2') {
                tmpBudget = 0;
                offset = int.tryParse(objTmp.budgetOffset ?? '0') ?? 0;
              } else {
                offset = shouldApplyBudgetOffset(
                    answerModel, totalBudgetOffsetStructureType1);
              }
            }

            logger('OFFSETTT: $offset');
            logger('INITIAL BUDGET: $originalBudget');

            if (offset != 0) {
              tmpBudget = tmpBudget + offset;
            }
          }
        }
      }
    }

    // mo, 21/2/19, budget minus value becomes 0
    if (tmpBudget < 0) {
      tmpBudget = 0;
    }

    if (tmpBudget != originalBudget) {
      logger('BUDGET Changed Test: $originalBudget : $tmpBudget');
      budgetHasChanged = true;
    } else {
      budgetHasChanged = false;
    }

    logger('----------- BUDGET Changed Test2 :: ---------- $budgetHasChanged');

    // Update the calculated budget in the task model
    realm.write(() {
      // Note: We need to add a taskBudgetCalculated field to the model
      // For now, we'll update the budget field or add a custom field
      // taskDetailModel.taskBudgetCalculated = tmpBudget;

      // Since the model doesn't have taskBudgetCalculated field,
      // we'll need to modify the model or use a different approach
      logger('Updated budget calculation: $tmpBudget');
    });
  } catch (e) {
    logger('Error in calculateBudgetCore: $e');
    rethrow;
  }

  logger('----------- summary ----------');
  final tmpModel = getTaskDetailModelWithTaskId(realm, taskId, true);
  if (tmpModel != null) {
    logger(
        'taskID: ${tmpModel.taskId}, org: ${tmpModel.budget}, new budget: calculated budget would be here');
  }
}
