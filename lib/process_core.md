<!-- private void processCore(final Realm bgRealm, final JSONObject dataObject) {
        //mo, 7/8/18, will be updated in (a)(b)(c)
        final ArrayList<String> taskIDsToCalculateBudget = new ArrayList<>();
        final HashSet<String> tmpTaskIDsToBeDeleted = new HashSet<>();      //debuging purpose only

        // (11-10) Process tasks to be deleted and record the IDs
        JSONArray deletedTaskIDs = dataObject.optJSONArray("delete_task_ids");
        if (deletedTaskIDs != null) {
            for (int i = 0; i < deletedTaskIDs.length(); i++) {
                String taskID = deletedTaskIDs.optString(i);
                if (!CommonFunction.isEmptyStringField(taskID)) {
                    tmpTaskIDsToBeDeleted.add(taskID);

                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, taskID);

                    TaskDetailModel localTask = localTaskQuery.findFirst();

                    if (localTask != null) {
                        //delete realm core
                        removeTaskDetailModelFromRealm(localTask);
                    }
                }
            }
        }



       // CommonFunction.print("Deleted tasks count: " + tmpTaskIDsToBeDeleted.size());

        // Get task models to be uploaded (11-11)
        // mo, 11/10/16, taskDetailModelsToBeUploaded is now decided from findUploadTaskIDs().
        // Although we still pass taskDetailModelsToBeUploaded to EventBus at the end, it is empty and API.java will do nothing with the variable when event is receivd.


        // (11-2) Update task documents data
        JSONArray updateTasksDocumentsArray = dataObject.optJSONArray("update_tasks_documents");
        if (updateTasksDocumentsArray != null) {
          //  CommonFunction.print("Updated tasks documents count: " + updateTasksDocumentsArray.length());
            //CommonFunction.printLongString(updateTasksDocumentsArray.toString());

            for (int i = 0; i < updateTasksDocumentsArray.length(); i++) {
                try {
                    JSONObject object = updateTasksDocumentsArray.getJSONObject(i);

                    // Server side task detail model
                    TaskDetailModel serverTask = DataModelManager.taskDetailModelFromJSON(object);

                    if (serverTask != null) {
                        RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                        localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverTask.getTaskID());

                        TaskDetailModel localTask = localTaskQuery.findFirst();

                        if (localTask != null) {
                            for (DocumentModel serverDocumentModel : serverTask.getTaskDocuments()) {
                                for (FileModel serverFileModel : serverDocumentModel.getDocumentFiles()) {
                                    DocumentModel localDocumentModel = localTask.getTaskDocuments().where().equalTo(DocumentModel.DOCUMENT_ID_KEY, serverDocumentModel.getDocumentID()).findFirst();

                                    if (localDocumentModel != null) {
                                        FileModel localFileModel = localDocumentModel.getDocumentFiles().where().equalTo(FileModel.FILE_ID_KEY, serverFileModel.getFileID()).findFirst();

                                        if (localFileModel != null) {
                                            try {
                                                if ((serverFileModel.getFileID().equalsIgnoreCase(localFileModel.getFileID())) &&
                                                        (serverFileModel.getFileLastModifiedDate().after(localFileModel.getFileLastModifiedDate()))) {
                                                    // Local file exist and it is modified. Needs to delete first
                                                    File localFile = new File(localFileModel.getFileLocalPath());
                                                    localFile.delete();
                                                }
                                            } catch (Exception e) {
                                                CommonFunction.print(">>>error: " + e);
                                            }
                                        }
                                    }
                                }
                            }

                            // Deleting all realm record
                            removeTaskDocumentsFromRealm(localTask, false);

                            // Copy new list data to realm
                            localTask.setTaskDocuments(new RealmList<>(bgRealm.copyToRealm(serverTask.getTaskDocuments()).toArray(new DocumentModel[serverTask.getTaskDocuments().size()])));

                            localTask.setTaskDocumentsModifiedDate(serverTask.getTaskDocumentsModifiedDate());
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }


        // (11-3) Update tasks forms data
        JSONArray updateTasksFormsArray = dataObject.optJSONArray("update_tasks_forms");
        if (updateTasksFormsArray != null) {

            for (int i = 0; i < updateTasksFormsArray.length(); i++) {
                try {
                    JSONObject object = updateTasksFormsArray.getJSONObject(i);

                    // Server side task detail model
                    TaskDetailModel serverTask = DataModelManager.taskDetailModelFromJSON(object);

                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverTask.getTaskID());

                    TaskDetailModel localTask = localTaskQuery.findFirst();

                    if (localTask != null) {
                        //mo, 9/5/19
                        localTask.setCtFormsTotalCnt(serverTask.getCtFormsTotalCnt());
                        localTask.setCtFormsCompletedCnt(serverTask.getCtFormsCompletedCnt());

                        for (FormModel serverFormModel : serverTask.getTaskForms()) {
                            FormModel localFormModel = localTask.getTaskForms().where().equalTo(FormModel.FORM_ID_KEY, serverFormModel.getFormID()).findFirst();

                            if (localFormModel != null) {
                                // Form exist and needs to be updated
                                localFormModel.setFormName(serverFormModel.getFormName());
                                localFormModel.setFormBriefURL(serverFormModel.getFormBriefURL());
                                localFormModel.setFormVisionURL(serverFormModel.getFormVisionURL());
                                localFormModel.setFormVision(serverFormModel.isFormVision());
                                localFormModel.setFormMandatory(serverFormModel.isFormMandatory());
                                localFormModel.setFormLastModifiedDate(serverFormModel.getFormLastModifiedDate());
                                localFormModel.setAllowForward(serverFormModel.isAllowForward());

                                //mo, 30/5/17, I have confirmed that form answers are not overwritten by here. Only form structures change.
                                removeFormQuestionsFromRealm(localFormModel);

                                localFormModel.setFormQuestions(new RealmList<>(bgRealm.copyToRealm(serverFormModel.getFormQuestions()).toArray(new QuestionModel[serverFormModel.getFormQuestions().size()])));
                            } else {
                                // Form not exist and needs to be added
                                localTask.getTaskForms().add(bgRealm.copyToRealm(serverFormModel));
                            }
                        }

                        // Check whether local form is deleted or not
                        RealmList<FormModel> localTaskForms = new RealmList();
                        localTaskForms.addAll(localTask.getTaskForms());

                        //delete realm example best3
                        Iterator<FormModel> localFormsIterator = localTaskForms.iterator();
                        while (localFormsIterator.hasNext()) {
                            FormModel localFormModel = localFormsIterator.next();

                            if (localFormModel.isValid()) {
                                boolean toBeDeleted = true;

                                for (FormModel serverFormModel : serverTask.getTaskForms()) {
                                    if (serverFormModel.getFormID().equalsIgnoreCase(localFormModel.getFormID())) {
                                        toBeDeleted = false;
                                        break;
                                    }
                                }

                                if (toBeDeleted) {
                                    removeFormQuestionsFromRealm(localFormModel);
                                    localFormModel.getFormQuestionAnswers().deleteAllFromRealm();
                                    localFormModel.deleteFromRealm();

                                    localFormsIterator.remove();
                                }
                            }
                        }

                        localTask.setTaskFormsModifiedDate(serverTask.getTaskFormsModifiedDate());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }


        // (11-6) Update tasks information data
        // mo, 4/11/16, DateSchedule is updated here second time (by comparing DateModifiedChangeable).
        JSONArray updateTeamMember = dataObject.optJSONArray("update_task_members");

        // Nirvik, extract helpers value from JSONArray and save in realm by task_id
        if (updateTeamMember != null) {
            //  CommonFunction.print("Updated phototypes count: " + updatePhotoTypesArray.length());

            for (int i = 0; i < updateTeamMember.length(); i++) {
                try {
                    JSONObject object = updateTeamMember.getJSONObject(i);

                    // Server side task detail model
                    Helpers helper = DataModelManager.helpersFromJSON(object);

//                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
////                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverTask.getTaskID());
////
////                    TaskDetailModel localTask = localTaskQuery.findFirst();
//
//                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, helper.getTaskId());
//
//                    TaskDetailModel localTask = localTaskQuery.findFirst();
//                    if(localTask != null){
//
//                        localTask.setTaskMembers(new RealmList<>(bgRealm.copyToRealm(helper.getMembers()).toArray(new TaskMember[helper.getMembers().size()])));
//                    }

                        // Fetch the latest instance of localTask
                        RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
//                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverTask.getTaskID());
//
//                    TaskDetailModel localTask = localTaskQuery.findFirst();

                        localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, helper.getTaskId());

                        TaskDetailModel localTask = localTaskQuery.findFirst();
                        if (localTask != null) {
                            // Ensure you are adding new members correctly
                            RealmList<TaskMember> membersToAdd = new RealmList<>();
                            for (TaskMember member : helper.getMembers()) {
                                membersToAdd.add(bgRealm.copyToRealm(member));
                            }

                            // Update the task members
                            localTask.setTaskMembers(membersToAdd);
                        }

                    EventBus.getDefault().post(new DatabaseEvent(DatabaseEvent.RECYCLERVIEW_PHOTO_DELETE_EVENT_TAG, DatabaseEvent.DATABASE_EVENT_SUCCESS_RESULT_TYPE, ""));



                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }



        JSONArray updateTasksInformationArray = dataObject.optJSONArray("update_tasks_tasks");
        if (updateTasksInformationArray != null) {
            //CommonFunction.print("Updated tasks information count: " + updateTasksInformationArray.length());
            //CommonFunction.printLongString(updateTasksInformationArray.toString());

            for (int i = 0; i < updateTasksInformationArray.length(); i++) {
                try {
                    JSONObject object = updateTasksInformationArray.getJSONObject(i);

                    // Server side task detail model
                    TaskDetailModel serverTask = DataModelManager.taskDetailModelFromJSON(object);

                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverTask.getTaskID());

                    TaskDetailModel localTask = localTaskQuery.findFirst();

                    if (localTask != null) {
                        localTask.setClientID(serverTask.getClientID());
                        localTask.setClientName(serverTask.getClientName());
                        localTask.setClientLogoURL(serverTask.getClientLogoURL());

                        // Deleting local file
                        try {
                            File localFile = new File(localTask.getClientLogoLocalPath());
                            localFile.delete();
                        } catch (Exception e) {
                            CommonFunction.print(">>>error: " + e);
                        }

                        localTask.setClientLogoLocalPath(null);

                        localTask.setStoreID(serverTask.getStoreID());
                        localTask.setStoreGroupID(serverTask.getStoreGroupID());
                        localTask.setStoreGroupName(serverTask.getStoreGroupName());
                        localTask.setStoreName(serverTask.getStoreName());
                        localTask.setStoreLocation(serverTask.getStoreLocation());
                        localTask.setStoreSuburb(serverTask.getStoreSuburb());
                        localTask.setStorePhoneNumber(serverTask.getStorePhoneNumber());
                        localTask.setStoreLatitude(serverTask.getStoreLatitude());
                        localTask.setStoreLongitude(serverTask.getStoreLongitude());

                        localTask.setTaskCycleID(serverTask.getTaskCycleID());
                        localTask.setTaskCycleName(serverTask.getTaskCycleName());

                        //(b) mo, 7/8/18, update taskBudgetCalculated here as well (<< server's original budgeted duration has changed)
                        localTask.setTaskBudget(serverTask.getTaskBudget());
                        if (!taskIDsToCalculateBudget.contains(localTask.getTaskID())) {
                            taskIDsToCalculateBudget.add(localTask.getTaskID());
                        }

                        localTask.setTaskReopened(serverTask.isTaskReopened());
                        localTask.setTaskReopenedReason(serverTask.getTaskReopenedReason());
                        localTask.setTaskAssignment(serverTask.getTaskAssignment());
                        localTask.setTaskStatus(serverTask.getTaskStatus());
                        localTask.setTaskSubmissionState(TaskDetailModel.TASK_SUBMISSION_STATE_DEFAULT);

                        localTask.setTaskStartDate(serverTask.getTaskStartDate());
                        localTask.setTaskEndDate(serverTask.getTaskEndDate());

                        localTask.setTaskScheduledDate(serverTask.getTaskScheduledDate());
                        localTask.setTaskExpiresDate(serverTask.getTaskExpiresDate());

                        localTask.setTaskConnoteURL(serverTask.getTaskConnoteURL());


                        localTask.setTaskTicked(false);


                        // Check whether task is completed or not
                        localTask.setTaskCompleted((localTask.getTaskStatus().equalsIgnoreCase(TaskDetailModel.TASK_SUCCESSFUL_STATUS_VALUE)) ||
                                (localTask.getTaskStatus().equalsIgnoreCase(TaskDetailModel.TASK_UNSUCCESSFUL_STATUS_VALUE)));

                        localTask.setTaskSyncPending(false);
                        localTask.setTaskFailedToSync(false);
                        localTask.setTaskSyncFailureReason("");

                        // Delete all realm record
                        localTask.getTaskPosItems().deleteAllFromRealm();

                        // Copy new list data to realm
                        localTask.setTaskPosItems(new RealmList<>(bgRealm.copyToRealm(serverTask.getTaskPosItems()).toArray(new PosItemModel[serverTask.getTaskPosItems().size()])));

                        //mo updated, 28/11/16 --------
                        localTask.getFollowupTasks().deleteAllFromRealm();
                        RealmList<FollowupTaskModel> followupTaskModelRealmList = new RealmList<>(bgRealm.copyToRealm(serverTask.getFollowupTasks()).toArray(new FollowupTaskModel[serverTask.getFollowupTasks().size()]));
                        localTask.setFollowupTasks(followupTaskModelRealmList);
                        //--------

                        //mo, 16/5/17, update ft information when updating task info
                        localTask.setShow_followup_icon_multi(serverTask.isShow_followup_icon_multi());
                        localTask.setFollowup_selected_multi(serverTask.isFollowup_selected_multi());

                        //mo, 12/12/16
                        localTask.setTaskNote(serverTask.getTaskNote());
                        localTask.setDisallowReschedule(serverTask.isDisallowReschedule());

                        localTask.setTaskModifiedDate(serverTask.getTaskModifiedDate());

                        //Gus, 28/10/2019
                        localTask.setTaskPosRequired(serverTask.getTaskPosRequired());
                        localTask.setProjectPhotoResolution(serverTask.getProjectPhotoResolution());
                        localTask.setLiveImagesOnly(serverTask.isLiveImagesOnly());

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }


        // (11-4) Update tasks photos data
        JSONArray updateTasksPhotosArray = dataObject.optJSONArray("update_tasks_photos");
        if (updateTasksPhotosArray != null) {
           // CommonFunction.print("Updated tasks photos count: " + updateTasksPhotosArray.length());
            //CommonFunction.printLongString(updateTasksPhotosArray.toString());

            for (int i = 0; i < updateTasksPhotosArray.length(); i++) {
                try {
                    JSONObject object = updateTasksPhotosArray.getJSONObject(i);

                    // Server side task detail model
                    TaskDetailModel serverTask = DataModelManager.taskDetailModelFromJSON(object);

                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverTask.getTaskID());

                    TaskDetailModel localTask = localTaskQuery.findFirst();

                    if (localTask != null) {
                        for (PhotoFolderModel serverPhotoFolderModel : serverTask.getTaskPhotoFolders()) {
                            for (PhotoModel serverPhotoModel : serverPhotoFolderModel.getPhotoFolderContents()) {
                                PhotoFolderModel localPhotoFolderModel = localTask.getTaskPhotoFolders().where().equalTo(PhotoFolderModel.PHOTO_FOLDER_ID_KEY, serverPhotoFolderModel.getPhotoFolderID()).findFirst();

                                if (localPhotoFolderModel != null) {
                                    PhotoModel localPhotoModel = localPhotoFolderModel.getPhotoFolderContents().where().equalTo(PhotoModel.PHOTO_ID_KEY, serverPhotoModel.getPhotoID()).findFirst();

                                    if (localPhotoModel != null) {
                                        //a photo by the id from server exists in app
                                        try {
                                            //mo, 23/10/17, if condition below is updated because sometimes local Photo Model's date modified was null. how it happened, i am not sure.
                                            if (serverPhotoModel.getPhotoID().equalsIgnoreCase(localPhotoModel.getPhotoID())) {

                                                System.out.printf("%s, %s <-> %s%n", localPhotoModel.getPhotoCaption(),
                                                        localPhotoModel.getPhotoLastModifiedDate().toString(),
                                                        serverPhotoModel.getPhotoLastModifiedDate());

                                                //mo, i don't care each photo's date modified. if you come here, it means server had some updated photo for this task >> replace all common photos.
                                                //mo, 15/11/17, i had to compare one by one. Otherwise whole localpaths will be null of this task's photos >> causing white screen issue
                                                if (localPhotoModel.getPhotoLastModifiedDate() == null || serverPhotoModel.getPhotoLastModifiedDate().after(localPhotoModel.getPhotoLastModifiedDate())) {
                                                    // server has latest information of the photo. >> delete all local photo file & info.

                                                    //mo created, 5/10/17
                                                    //----------------------
                                                    //(a) common photos >> override app photo detail with server's one.
                                                    //----------------------
                                                    updateLocalPhotoModelByServerModel(localPhotoModel, serverPhotoModel);

                                                    //(b) app only photos >> handled below outside of this code block
                                                }
                                            }
                                        } catch (Exception e) {
                                            CommonFunction.print(">>>error: " + e);
                                        }
                                    } else {
                                        //mo created this else part, 5/10/17
                                        //----------------------
                                        // (c) server only photos >>  not in app >> copy those photos (that only exist in server side) into this app.
                                        //----------------------
                                        localPhotoModel = new PhotoModel();
                                        updateLocalPhotoModelByServerModel(localPhotoModel, serverPhotoModel);

                                        //add to photofolder
                                        localPhotoFolderModel.getPhotoFolderContents().add(localPhotoModel);
                                    }
                                }
                            }
                        }

                        //mo, 5/10/17, Deleting local photos are commented NOT TO delete un-synced photo
                        // <Rule of thumb>
                        // Never wipe out un-synced photo on app. (this saves un-synced photos from network error)

                        //----------------------
                        //(b)) app only photos >> leave them on app if not synced,  user can delete. (this will save un-synced photos)
                        //(b)) app only photos >> if synced >> this photo was sent to server from this device, but deleted from other mobile device >> can delete
                        //----------------------
                        for (PhotoFolderModel localPhotoFolderModel : localTask.getTaskPhotoFolders()) {
                            for (PhotoModel localPhotoModel : localPhotoFolderModel.getPhotoFolderContents()) {
                                //for this local photo, check if server photo exists >> if not, and synced already >> delete it
                                if (!localPhotoModel.isPhotoItemUpdated()) {
                                    //synced already >> same photo id exists in server?
                                    PhotoModel serverPhotoModel = findServerPhotoModel(localPhotoModel, serverTask);
                                    if (serverPhotoModel == null) {
                                        //delete photo
                                        localPhotoModel.setPhotoIsDeleted(true);
                                    }
                                }
                            }
                        }

                        //Use existing function for iterator. delete local photos that are destined to be deleted from above (b).
                        removeDeletedPhotosFromRealm(bgRealm, true);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }


        //--------------------------------------photo type begin--------------------
        //MARK: mo, update/add/delete phototype
        //mo, we need to update phototypes without touching photo_folder's photos

        //---------------------
        //(1) UPDATE phototypes (11-7)
        JSONArray updatePhotoTypesArray = dataObject.optJSONArray("update_phototypes");
        if (updatePhotoTypesArray != null) {
          //  CommonFunction.print("Updated phototypes count: " + updatePhotoTypesArray.length());

            for (int i = 0; i < updatePhotoTypesArray.length(); i++) {
                try {
                    JSONObject object = updatePhotoTypesArray.getJSONObject(i);

                    // Server side task detail model
                    PhotoTypeListModel serverTask = DataModelManager.photoTypelListModelFromJSON(object);

                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverTask.getTaskID());
                    TaskDetailModel localTask = localTaskQuery.findFirst();

                    if (localTask != null) {
                        for (PhotoTypeModel serverObj : serverTask.getPhotoTypes()) {
                            //mo, for each server phototypes, update local task's photoFolder
                            //error below line becuase phototype_id is int
                            PhotoFolderModel localPhotoFolderModel = localTask.getTaskPhotoFolders().where().equalTo(PhotoFolderModel.PHOTO_FOLDER_ID_KEY, serverObj.getPhototype_id()).findFirst();

                            if (localPhotoFolderModel != null) {
                                try {
                                    //update wheather it has to be mandatory, update phto type name, update number of requuired photos.
                                    localPhotoFolderModel.setPhotoFolderMandatory(serverObj.isManadatory());
                                    localPhotoFolderModel.setPhotoFolderName(serverObj.getPhototype_name());
                                    localPhotoFolderModel.setPhotoFolderMaxAmount(serverObj.getPhototype_picture_amount());

                                } catch (Exception e) {
                                    CommonFunction.print(">>>error: " + e);
                                }
                            }

                        }
                        //all photo types update finished >> update time stamp of phototypes
                        localTask.setTaskPhotoTypesModifiedDate(serverTask.getModified_time_stamp_phototypes());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        //---------------------
        //(2) ADD phototypes (11-8)
        JSONArray addPhotoTypesArray = dataObject.optJSONArray("add_phototypes");
        if (addPhotoTypesArray != null) {
            CommonFunction.print("Added phototypes count: " + addPhotoTypesArray.length());

            // for each add_phototypes obj
            for (int i = 0; i < addPhotoTypesArray.length(); i++) {
                try {
                    JSONObject object = addPhotoTypesArray.getJSONObject(i);

                    // Server side add_phototypes obj model
                    PhotoTypeListModel serverAddPTObj = DataModelManager.photoTypelListModelFromJSON(object);

                    //find a task from local db which has sertverPT's task_id
                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverAddPTObj.getTaskID());
                    TaskDetailModel localTask = localTaskQuery.findFirst();

                    if (localTask != null) {
                        //local task is found >> get the local task's existing photo folder.
                        //(2a) existing photofolder
                        RealmList<PhotoFolderModel> existingList = localTask.getTaskPhotoFolders();

                        //(2b) Convert newly added phototypes to PhotoFolderModel
                        for (PhotoTypeModel serverPT : serverAddPTObj.getPhotoTypes()) {

                            //!important, mo, 9/12/16, check if exlistList already has this new phototypeid by previous "add_task_photos" array.
                            if (!existingListHasPTAlready(existingList, serverPT)) {

                                //mo, for each server phototypes, update local task's photoFolder
                                //error below line becuase phototype_id is int
                                PhotoFolderModel newPhotoFolder = new PhotoFolderModel();

                                try {
                                    newPhotoFolder.setPhotoFolderID(serverPT.getPhototype_id());
                                    newPhotoFolder.setPhotoFolderMandatory(serverPT.isManadatory());
                                    newPhotoFolder.setPhotoFolderName(serverPT.getPhototype_name());
                                    newPhotoFolder.setPhotoFolderMaxAmount(serverPT.getPhototype_picture_amount());
                                    newPhotoFolder.setPhotoFolderContents(new RealmList<>());
                                    newPhotoFolder.setPhotoFolderLastModifiedDate(serverPT.getModified_time_stamp_phototype());

                                    final PhotoFolderModel managedPhotoFolder = bgRealm.copyToRealm(newPhotoFolder);

                                    existingList.add(managedPhotoFolder);

                                } catch (Exception e) {
                                    CommonFunction.print(">>>new photo type error: " + e);
                                }
                            }

                        }

                        //(2d) all photo types update finished >> update time stamp of phototypes
                        localTask.setTaskPhotoTypesModifiedDate(serverAddPTObj.getModified_time_stamp_phototypes());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        //---------------------
        //(3) DELETE phototypes (11-9)
        JSONArray deletePhotoTypesArray = dataObject.optJSONArray("delete_phototypes");
        if (deletePhotoTypesArray != null) {
            CommonFunction.print("Deleted phototypes count: " + deletePhotoTypesArray.length());

            for (int i = 0; i < deletePhotoTypesArray.length(); i++) {
                try {
                    JSONObject object = deletePhotoTypesArray.getJSONObject(i);

                    // Server side task detail model
                    //mo, Check if serverTask is set up properly at this point.
                    PhotoTypeDeleteListModel serverTask = DataModelManager.photoTypelDeleteListModelFromJSON(object);

                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverTask.getTaskID());
                    TaskDetailModel localTask = localTaskQuery.findFirst();

                    if (localTask != null) {
                        //mo, loop through the hashset, and delete correspoinding photo type.
                        for (String phototypeID : serverTask.phototypeIDsToBeDeleted) {
                            //mo, for each server phototypes, find local task's photoFolder and delete it.

                            PhotoFolderModel localPhotoFolder = localTask.getTaskPhotoFolders().where().equalTo(PhotoFolderModel.PHOTO_FOLDER_ID_KEY, phototypeID).findFirst();
                            try {
                                Objects.requireNonNull(localPhotoFolder).deleteFromRealm();
                            } catch (Exception e) {
                                CommonFunction.print(">>>delete photo type error: " + e);
                            }

                            //mo, 29/1/18, when mpt's photo type is deleted from server, it will trigger form.datemodified, and the task's mpt model will be overwritten as well.
                        }


                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        //--------------------------------------photo type end--------------------

        // (11-1) Add new tasks
        JSONArray addTasksArray = dataObject.optJSONArray("add_tasks");
        if (addTasksArray != null) {
          //  CommonFunction.print("Added tasks count: " + addTasksArray.length());

            for (int i = 0; i < addTasksArray.length(); i++) {
                try {
                    JSONObject object = addTasksArray.getJSONObject(i);

                    // Server side task detail model
                    TaskDetailModel serverTask = DataModelManager.taskDetailModelFromJSON(object);

                  //  CommonFunction.print("Signature Folders : Saving : Upload : SIGNATURE : PROB A " + serverTask.getTaskID() + " : " + serverTask.getTaskSignatureFolders().size());

                    bgRealm.copyToRealmOrUpdate(serverTask);

                    //(c)
                    if (!taskIDsToCalculateBudget.contains(serverTask.getTaskID())) {
                        taskIDsToCalculateBudget.add(serverTask.getTaskID());
                    }


                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        //mo, 4/3/19, update_tasks_submission part is moved to the very end here because update_tasks_form was overwriting form answers and completed-multi-question answers never show correctly after More >> Reset Data.
        // (11-5) Update task submission data (form answers updated here)
        // mo, 4/11/16, DateSchedule is updated here first time (by comparing DateModified)

        JSONArray updateTasksSubmissionArray = dataObject.optJSONArray("update_tasks_submission");
        if (updateTasksSubmissionArray != null) {
            CommonFunction.print("Updated tasks submission count: " + updateTasksSubmissionArray.length());
            //CommonFunction.printLongString(updateTasksSubmissionArray.toString());

            for (int i = 0; i < updateTasksSubmissionArray.length(); i++) {
                try {
                    JSONObject object = updateTasksSubmissionArray.getJSONObject(i);

                    // Server side task detail model
                    TaskDetailModel serverTask = DataModelManager.taskDetailModelFromJSON(object);

                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverTask.getTaskID());

                    TaskDetailModel localTask = localTaskQuery.findFirst();

                    if (localTask != null) {
                        //mo, 9/5/19
                        localTask.setCtFormsTotalCnt(serverTask.getCtFormsTotalCnt());
                        localTask.setCtFormsCompletedCnt(serverTask.getCtFormsCompletedCnt());

                        //(a) answers changed >> taskBudgetCalculated needs to be calculated again.
                        if (!taskIDsToCalculateBudget.contains(localTask.getTaskID())) {
                            taskIDsToCalculateBudget.add(localTask.getTaskID());
                        }


                        localTask.setTaskComment(serverTask.getTaskComment());
                        localTask.setTaskDuration(serverTask.getTaskDuration());
                        localTask.setTaskClaimableKMS(serverTask.getTaskClaimableKMS());
                        localTask.setTaskPrintedPages(serverTask.getTaskPrintedPages());
                        localTask.setTaskStatus(serverTask.getTaskStatus());
                        localTask.setTaskSubmissionState(TaskDetailModel.TASK_SUBMISSION_STATE_DEFAULT);

                        localTask.setTaskScheduledDate(serverTask.getTaskScheduledDate());

                        //mo commented, 3/3/17, timestamp on app should never be updated (ios behaves same)

                        localTask.setTaskSubmissionDate(serverTask.getTaskSubmissionDate());

                        localTask.setTaskStartLatitude(0.0);
                        localTask.setTaskStartLongitude(0.0);

                        localTask.setTaskSubmissionLatitude(serverTask.getTaskSubmissionLatitude());
                        localTask.setTaskSubmissionLongitude(serverTask.getTaskSubmissionLongitude());

                        for (FormModel serverFormModel : serverTask.getTaskForms()) {
                            for (FormModel localFormModel : localTask.getTaskForms()) {
                                if (localFormModel.getFormID().equalsIgnoreCase(serverFormModel.getFormID())) {
                                    localFormModel.getFormQuestionAnswers().deleteAllFromRealm();
                                    localFormModel.setFormQuestionAnswers(new RealmList<>(bgRealm.copyToRealm(serverFormModel.getFormQuestionAnswers()).toArray(new QuestionAnswerModel[serverFormModel.getFormQuestionAnswers().size()])));

                                    localFormModel.getFormOriginalQuestionAnswers().deleteAllFromRealm();
                                    localFormModel.setFormOriginalQuestionAnswers(new RealmList<>(bgRealm.copyToRealm(serverFormModel.getFormOriginalQuestionAnswers()).toArray(new QuestionAnswerModel[serverFormModel.getFormOriginalQuestionAnswers().size()])));

                                    //mo, because api doesn't pass isFormEdited or isFormCompleted, both seems to set as false here.
                                    localFormModel.setFormEdited(serverFormModel.isFormEdited());
                                    localFormModel.setFormCompleted(serverFormModel.isFormCompleted());
                                }
                            }
                        }

                        // multi question qp update begin -------------
                        // mo, 20/2/19, answers changed >> multi question's QPs needs to be built again by this answer.
                        for (FormModel serverFormModel : serverTask.getTaskForms()) {
                            // CommonFunction.print("task_id: " + serverTask.getTaskID());

                            for (FormModel localFormModel : localTask.getTaskForms()) {
                                if (localFormModel.getFormID().equalsIgnoreCase(serverFormModel.getFormID())) {
                                    for (QuestionModel questionModel : localFormModel.getFormQuestions()) {
                                        if (questionModel.isQuestionMulti()) {
                                            //(a) build new qp from answer
                                            RealmList<QuestionPartModel> newQuestionPartsBuilt = buildQuestionPartMultiFromAnswers(localFormModel, questionModel);
                                            //(b) write the new qp into realm
                                            updateQuestionPartsMultiToDb(questionModel, newQuestionPartsBuilt);
                                        }
                                    }
                                }
                            }
                        }
                        // multi question qp update end -------------

                        localTask.setTaskTicked(false);

                        // Check whether task is completed or not
                        localTask.setTaskCompleted((localTask.getTaskStatus().equalsIgnoreCase(TaskDetailModel.TASK_SUCCESSFUL_STATUS_VALUE)) ||
                                (localTask.getTaskStatus().equalsIgnoreCase(TaskDetailModel.TASK_UNSUCCESSFUL_STATUS_VALUE)));

                        localTask.setTaskSyncPending(false);
                        localTask.setTaskFailedToSync(false);
                        localTask.setTaskSyncFailureReason("");

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }


        //-----------------Signature Process------------

        // (11-4) Update tasks photos data
        JSONArray updateTasksSignaturesArray = dataObject.optJSONArray("update_tasks_signatures");
        if (updateTasksSignaturesArray != null) {
            //CommonFunction.printLongString(updateTasksPhotosArray.toString());

            for (int i = 0; i < updateTasksSignaturesArray.length(); i++) {
                try {
                    JSONObject object = updateTasksSignaturesArray.getJSONObject(i);

                    // Server side task detail model
                    TaskDetailModel serverTask = DataModelManager.taskDetailModelFromJSON(object);

                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverTask.getTaskID());

                    TaskDetailModel localTask = localTaskQuery.findFirst();

                    if (localTask != null) {
                        for (SignatureFolderModel serverSignatureFolderModel : serverTask.getTaskSignatureFolders()) {
                            for (SignatureModel serverSignatureModel : serverSignatureFolderModel.getSignatureFolderContents()) {
                                SignatureFolderModel localSignatureFolderModel = localTask.getTaskSignatureFolders().where().equalTo(SignatureFolderModel.SIGNATURE_FOLDER_ID_KEY, serverSignatureFolderModel.getSignatureFolderID()).findFirst();

                                if (localSignatureFolderModel != null) {
                                    SignatureModel localSignatureModel = localSignatureFolderModel.getSignatureFolderContents().where().equalTo(SignatureModel.SIGNATURE_ID_KEY, serverSignatureModel.getSignatureId()).findFirst();

                                    if (localSignatureModel != null) {
                                        //a photo by the id from server exists in app
                                        try {
                                            //mo, 23/10/17, if condition below is updated because sometimes local Photo Model's date modified was null. how it happened, i am not sure.
                                            if (serverSignatureModel.getSignatureId().equalsIgnoreCase(localSignatureModel.getSignatureId())) {

                                                System.out.printf("%s, %s <-> %s%n", localSignatureModel.getSignedBy(),
                                                        localSignatureModel.getSignatureLastModifiedDate().toString(),
                                                        serverSignatureModel.getSignatureLastModifiedDate());

                                                //mo, i don't care each photo's date modified. if you come here, it means server had some updated photo for this task >> replace all common photos.
                                                //mo, 15/11/17, i had to compare one by one. Otherwise whole localpaths will be null of this task's photos >> causing white screen issue
                                                if (localSignatureModel.getSignatureLastModifiedDate() == null || serverSignatureModel.getSignatureLastModifiedDate().after(localSignatureModel.getSignatureLastModifiedDate())) {
                                                    // server has latest information of the photo. >> delete all local photo file & info.

                                                    //mo created, 5/10/17
                                                    //----------------------
                                                    //(a) common photos >> override app photo detail with server's one.
                                                    //----------------------
                                                    updateLocalSignatureModelByServerModel(localSignatureModel, serverSignatureModel);

                                                    //(b) app only photos >> handled below outside of this code block
                                                }
                                            }
                                        } catch (Exception e) {
                                            CommonFunction.print(">>>error: " + e);
                                        }
                                    } else {
                                        //mo created this else part, 5/10/17
                                        //----------------------
                                        // (c) server only photos >>  not in app >> copy those photos (that only exist in server side) into this app.
                                        //----------------------
                                        localSignatureModel = new SignatureModel();
                                        updateLocalSignatureModelByServerModel(localSignatureModel, serverSignatureModel);

                                        //add to photofolder
                                        localSignatureFolderModel.getSignatureFolderContents().add(localSignatureModel);
                                    }
                                }
                            }
                        }

                        //mo, 5/10/17, Deleting local photos are commented NOT TO delete un-synced photo
                        // <Rule of thumb>
                        // Never wipe out un-synced photo on app. (this saves un-synced photos from network error)

                        //----------------------
                        //(b)) app only photos >> leave them on app if not synced,  user can delete. (this will save un-synced photos)
                        //(b)) app only photos >> if synced >> this photo was sent to server from this device, but deleted from other mobile device >> can delete
                        //----------------------
                        for (SignatureFolderModel localSignatureFolderModel : localTask.getTaskSignatureFolders()) {
                            for (SignatureModel localSignatureModel : localSignatureFolderModel.getSignatureFolderContents()) {
                                //for this local photo, check if server photo exists >> if not, and synced already >> delete it
                                if (!localSignatureModel.isSignatureItemUpdated()) {
                                    //synced already >> same photo id exists in server?
                                    SignatureModel serverSignatureModel = findServerSignatureModel(localSignatureModel, serverTask);
                                    if (serverSignatureModel == null) {
                                        //delete photo
                                        localSignatureModel.setSignatureIsDeleted(true);
                                    }
                                }
                            }
                        }

                        //Use existing function for iterator. delete local photos that are destined to be deleted from above (b).
                        removeDeletedSignatureFromRealm(bgRealm, true);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }


        //--------------------------------------Signature type begin--------------------
        //MARK: mo, update/add/delete phototype
        //mo, we need to update phototypes without touching photo_folder's photos

        //---------------------
        //(1) UPDATE phototypes (12-7)
        JSONArray updateSignatureTypesArray = dataObject.optJSONArray("update_signaturetypes");
        if (updateSignatureTypesArray != null) {
           // CommonFunction.print("Updated signaturetypes count: " + updateSignatureTypesArray.length());

            for (int i = 0; i < updateSignatureTypesArray.length(); i++) {
                try {
                    JSONObject object = updateSignatureTypesArray.getJSONObject(i);

                    // Server side task detail model
                    SignatureTypeListModel serverTask = DataModelManager.signatureTypelListModelFromJSON(object);

                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverTask.getTaskID());
                    TaskDetailModel localTask = localTaskQuery.findFirst();

                    if (localTask != null) {
                        for (SignatureTypeModel serverObj : serverTask.getSignatureTypes()) {
                            //mo, for each server phototypes, update local task's photoFolder
                            //error below line becuase phototype_id is int
                            SignatureFolderModel localSignatureFolderModel = localTask.getTaskSignatureFolders().where().equalTo(SignatureFolderModel.SIGNATURE_FOLDER_ID_KEY, serverObj.getSignaturetype_id()).findFirst();

                            if (localSignatureFolderModel != null) {
                                try {
                                    //update wheather it has to be mandatory, update phto type name, update number of requuired photos.
                                    localSignatureFolderModel.setSignatureFolderMandatory(serverObj.isManadatory());
                                    localSignatureFolderModel.setSignatureFolderName(serverObj.getSignaturetype_name());
//                                    localSignatureFolderModel.set(serverObj.getPhototype_picture_amount());

                                } catch (Exception e) {
                                    CommonFunction.print(">>>error: " + e);
                                }
                            }

                        }
                        //all photo types update finished >> update time stamp of phototypes
                        localTask.setTaskSignatureTypesModifiedDate(serverTask.getModified_time_stamp_signaturetypes());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        //---------------------
        //(2) ADD phototypes (12-8)
        JSONArray addSignatureTypesArray = dataObject.optJSONArray("add_signaturetypes");
        if (addSignatureTypesArray != null) {
           // CommonFunction.print("Added signaturetypes count: " + addSignatureTypesArray.length());

            // for each add_phototypes obj
            for (int i = 0; i < addSignatureTypesArray.length(); i++) {
                try {
                    JSONObject object = addSignatureTypesArray.getJSONObject(i);

                    // Server side add_phototypes obj model
                    SignatureTypeListModel serverAddPTObj = DataModelManager.signatureTypelListModelFromJSON(object);

                    //find a task from local db which has sertverPT's task_id
                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverAddPTObj.getTaskID());
                    TaskDetailModel localTask = localTaskQuery.findFirst();

                    if (localTask != null) {
                        //local task is found >> get the local task's existing photo folder.
                        //(2a) existing photofolder
                        RealmList<SignatureFolderModel> existingList = localTask.getTaskSignatureFolders();

                        //(2b) Convert newly added phototypes to PhotoFolderModel
                        for (SignatureTypeModel serverPT : serverAddPTObj.getSignatureTypes()) {

                            //!important, mo, 9/12/16, check if exlistList already has this new phototypeid by previous "add_task_photos" array.
                            if (!existingListHasSIGAlready(existingList, serverPT)) {

                                //mo, for each server phototypes, update local task's photoFolder
                                //error below line becuase phototype_id is int
                                SignatureFolderModel newSignatureFolder = new SignatureFolderModel();

                                try {
                                    newSignatureFolder.setSignatureFolderID(serverPT.getSignaturetype_id());
                                    newSignatureFolder.setSignatureFolderMandatory(serverPT.isManadatory());
                                    newSignatureFolder.setSignatureFolderName(serverPT.getSignaturetype_name());
                                    newSignatureFolder.setSignatureFolderContents(new RealmList<>());
                                    newSignatureFolder.setSignatureFolderLastModifiedDate(serverPT.getModified_time_stamp_signaturetype());

                                    final SignatureFolderModel managedSignatureFolder = bgRealm.copyToRealm(newSignatureFolder);

                                    existingList.add(managedSignatureFolder);

                                } catch (Exception e) {
                                    CommonFunction.print(">>>new photo type error: " + e);
                                }
                            }

                        }

                        //(2d) all photo types update finished >> update time stamp of phototypes
                        localTask.setTaskSignatureTypesModifiedDate(serverAddPTObj.getModified_time_stamp_signaturetypes());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        //---------------------
        //(3) DELETE phototypes (12-9)
        JSONArray deleteSignatureTypesArray = dataObject.optJSONArray("delete_signaturetypes");
        if (deleteSignatureTypesArray != null) {

            for (int i = 0; i < deleteSignatureTypesArray.length(); i++) {
                try {
                    JSONObject object = deleteSignatureTypesArray.getJSONObject(i);

                    // Server side task detail model
                    //mo, Check if serverTask is set up properly at this point.
                    SignatureTypeDeleteListModel serverTask = DataModelManager.signatureTypelDeleteListModelFromJSON(object);

                    RealmQuery<TaskDetailModel> localTaskQuery = bgRealm.where(TaskDetailModel.class);
                    localTaskQuery.equalTo(TaskDetailModel.TASK_ID_KEY, serverTask.getTaskID());
                    TaskDetailModel localTask = localTaskQuery.findFirst();

                    if (localTask != null) {
                        //mo, loop through the hashset, and delete correspoinding photo type.
                        for (String signaturetypeID : serverTask.signaturetypeIDsToBeDeleted) {
                            //mo, for each server phototypes, find local task's photoFolder and delete it.

                            SignatureFolderModel localSignatureFolder = localTask.getTaskSignatureFolders().where().equalTo(SignatureFolderModel.SIGNATURE_FOLDER_ID_KEY, signaturetypeID).findFirst();
                            try {
                                Objects.requireNonNull(localSignatureFolder).deleteFromRealm();
                            } catch (Exception e) {
                                CommonFunction.print(">>>delete Signature type error: " + e);
                            }

                            //mo, 29/1/18, when mpt's photo type is deleted from server, it will trigger form.datemodified, and the task's mpt model will be overwritten as well.
                        }


                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        //--------------------------------------Signature type end--------------------

        //----------------------SIGNATURES-------------------------


        // MPT is handled inside question or measurement according to combine_type_id

        // All Joshua's work is finished. Now recalculate budget if necessary
        CommonFunction.print("taskIDs to calculate budget: " + taskIDsToCalculateBudget);

        for (String taskID : taskIDsToCalculateBudget) {
            calculateBudgetCore(bgRealm, taskID);
        }
    } -->