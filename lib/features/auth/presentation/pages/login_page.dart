import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:msal_auth/msal_auth.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/auth/presentation/blocs/auth/auth_cubit.dart';
import 'package:storetrack_app/shared/widgets/loading_widget.dart';
import '../../../../config/routes/app_router.gr.dart';
import '../../../../core/utils/logger.dart';
import '../../../auth/presentation/widgets/app_button.dart';
import '../../../auth/presentation/widgets/app_text_field.dart';
import '../../data/models/auth_request.dart';

@RoutePage()
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  late final TextEditingController _usernameController;
  late final TextEditingController _passwordController;
  final _formKey = GlobalKey<FormState>();
  bool _obscurePassword = true;
  SingleAccountPca? _msalAuth;
  bool _loading = false;
  bool _showPasswordField = false;
  bool _autoValidate = false;
  bool _isEmailValid = false;

  @override
  void initState() {
    super.initState();
    _usernameController = TextEditingController()..addListener(_onEmailChanged);
    _passwordController = TextEditingController();
    _initMsal();
  }

  void _onEmailChanged() {
    final email = _usernameController.text.trim().toLowerCase();
    final hasValidEmailFormat = RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(email);
    final shouldShowPassword = hasValidEmailFormat &&
        !(email.contains('@dkshsmollan.com') || email.contains('@dksh.com'));

    // Validate email on each change
    final isValid = _validateEmail(email) == null;

    setState(() {
      _isEmailValid = isValid;

      if (_showPasswordField != shouldShowPassword) {
        _showPasswordField = shouldShowPassword;
        if (!_showPasswordField) {
          _passwordController.clear();
        }
      }
    });
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (_showPasswordField) {
      if (value == null || value.isEmpty) {
        return 'Password is required';
      }
      if (value.length < 6) {
        return 'Password must be at least 6 characters';
      }
    }
    return null;
  }

  Future<void> _initMsal() async {
    try {
      const clientIdAndroid = "b39f9c3c-adf9-49d4-acee-2323b747f30e";
      const clientIdIos = "b69e600b-8182-4764-8d30-d998b1a7e024";
      const androidRedirectUri = "msauth://com.au.storetrack/callback";
      // const iosRedirectUri = "msauth.com.crossmark.StoreTrack-Crossmark://auth";

      _msalAuth = await SingleAccountPca.create(
        clientId: Platform.isIOS ? clientIdIos : clientIdAndroid,
        androidConfig: AndroidConfig(
          configFilePath: 'assets/jsons/msal_config.json',
          redirectUri: androidRedirectUri,
        ),
        appleConfig: AppleConfig(
          authorityType: AuthorityType.aad,
          broker: Broker.webView,
        ),
      );
      logger('MSAL initialized successfully.');
    } catch (e) {
      logger('Error initializing MSAL: $e');
    }
  }

  @override
  void dispose() {
    _usernameController.removeListener(_onEmailChanged);
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _acquireToken() async {
    if (_msalAuth == null) {
      logger('MSAL not initialized.');
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Login service unavailable.',
        );
      }
      return;
    }

    try {
      final AuthenticationResult authResult = await _msalAuth!.acquireToken(
        scopes: <String>['User.Read'],
        prompt: Prompt.selectAccount,
        loginHint: _usernameController.text,
      );
      logger('Authentication successful:');
      logger('Access Token: ${authResult.accessToken}');
      logger('Account Username: ${authResult.account.username}');
      logger('Expires On: ${authResult.expiresOn}');
      if (mounted) context.read<AuthCubit>().loginAzure();
    } on MsalException catch (e) {
      logger('MSAL Authentication failed: ${e.message}');
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Authentication failed: ${e.message}',
        );
      }
    } catch (e) {
      logger('An unexpected error occurred during authentication: $e');
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Login failed. Try again.',
        );
      }
    }
  }

  Future<void> _handleSubmit() async {
    final isValid = _formKey.currentState!.validate();
    if (!isValid) {
      if (!_autoValidate) {
        setState(() => _autoValidate = true);
      }
      return;
    }

    setState(() => _loading = true);

    final email = _usernameController.text.trim().toLowerCase();

    final isMsal =
        email.contains('@dkshsmollan.com') || email.contains('@dksh.com');
    final isNz = email.contains('@dkshnz.com') ||
        email.contains('@storetrack.com') ||
        email.contains('@crossmark.biz');
    if (!isMsal && !isNz) {
      SnackBarService.error(
        context: context,
        message: 'Email not allowed.',
      );
      setState(() => _loading = false);
      return;
    }

    try {
      if (isMsal) {
        await _acquireToken();
      } else if (isNz) {
        final authRequest = AuthRequest(
          email: email,
          password: _passwordController.text,
        );
        context.read<AuthCubit>().loginNZ(authRequest: authRequest);
      }
    } finally {
      if (mounted) setState(() => _loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        forceMaterialTransparency: true,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.light,
        ),
      ),
      extendBodyBehindAppBar: true,
      body: BlocConsumer<AuthCubit, AuthState>(
        listener: (context, state) {
          if (state is AuthSuccess) {
            context.read<AuthCubit>().login(email: _usernameController.text);
          }
          if (state is LoginSuccess) {
            context.router.replaceAll([const HomeRoute()]);
          }
          if (state is AuthError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          }
        },
        builder: (context, state) {
          if (state is AuthLoading) return const LoadingWidget();
          if (state is AuthError) {
            // return RetryWidget(
            //   color: Colors.white,
            //   onRetry: _handleSubmit,
            // );
          }

          return Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(AppAssets.imgBg),
                fit: BoxFit.fill,
                opacity: 0.2,
              ),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF0077CC),
                  Color(0xFF005799),
                  Color(0xFF003366),
                ],
              ),
            ),
            child: SafeArea(
              child: Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Form(
                    key: _formKey,
                    autovalidateMode: _autoValidate
                        ? AutovalidateMode.onUserInteraction
                        : AutovalidateMode.disabled,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Image.asset(
                          AppAssets.banner,
                          width: MediaQuery.of(context).size.width * 0.8,
                        ),
                        const SizedBox(height: 24),
                        const Text(
                          'Please sign in using your company email address.',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            letterSpacing: 0.2,
                          ),
                        ),
                        const SizedBox(height: 40),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.08),
                                blurRadius: 24,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              AppTextField(
                                label: 'Email',
                                controller: _usernameController,
                                validator: _validateEmail,
                                keyboardType: TextInputType.emailAddress,
                                textInputAction: _showPasswordField
                                    ? TextInputAction.next
                                    : TextInputAction.done,
                              ),
                              if (_showPasswordField) ...[
                                const Divider(),
                                AppTextField(
                                  label: 'Password',
                                  controller: _passwordController,
                                  obscureText: _obscurePassword,
                                  validator: _validatePassword,
                                  textInputAction: TextInputAction.done,
                                  suffixIcon: TextButton(
                                    onPressed: () => setState(() =>
                                        _obscurePassword = !_obscurePassword),
                                    child: Text(
                                      _obscurePassword ? 'Show' : 'Hide',
                                      style: const TextStyle(fontSize: 13),
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        const SizedBox(height: 36),
                        SizedBox(
                          width: double.infinity,
                          child: AppButton(
                            text: _loading ? 'Please wait...' : 'Sign In',
                            color: AppColors.loginRed,
                            onPressed: _loading ? () {} : _handleSubmit,
                          ),
                        ),
                        const SizedBox(height: 16),
                        if (_isEmailValid) ...[
                          SizedBox(
                            width: double.infinity,
                            child: AppButton(
                              text: 'Password Reset',
                              color: AppColors.loginGreen,
                              onPressed: () {
                                // if msal go to web browser route
                                final email = _usernameController.text
                                    .trim()
                                    .toLowerCase();
                                final isAzure =
                                    (email.contains('@dkshsmollan.com') ||
                                        email.contains('@dksh.com'));
                                if (isAzure) {
                                  context.router.push(WebBrowserRoute(
                                      url:
                                          "https://passwordreset.microsoftonline.com/"));
                                } else {
                                  context.router.push(ResetPasswordRoute(
                                      email: _usernameController.text));
                                }
                              },
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
