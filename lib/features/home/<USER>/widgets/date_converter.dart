import 'package:intl/intl.dart';

String convertToDateFormat(String inputDate) {
  try {
    // First try to parse as DD/MM/YYYY format with time
    if (inputDate.contains('/') && inputDate.contains(' ')) {
      // Handle format like "23/06/2025 9:36:00 PM"
      DateTime date = DateFormat('dd/MM/yyyy h:mm:ss a').parse(inputDate);
      return DateFormat('dd/MM/yyyy').format(date);
    }

    // Fallback to ISO format parsing
    DateTime date = DateTime.parse(inputDate);
    return DateFormat('dd/MM/yyyy').format(date);
  } catch (e) {
    // Handle invalid date input
    return 'Invalid date';
  }
}
