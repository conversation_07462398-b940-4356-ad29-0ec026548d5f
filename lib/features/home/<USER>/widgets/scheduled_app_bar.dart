import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/services/location_service.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';

class ScheduledAppBar extends StatelessWidget implements PreferredSizeWidget {
  final bool isCheckboxMode;
  final VoidCallback onCalendarTap;
  final VoidCallback onEditTap;
  final PreferredSizeWidget bottom;

  const ScheduledAppBar({
    super.key,
    required this.isCheckboxMode,
    required this.onCalendarTap,
    required this.onEditTap,
    required this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    return CustomAppBar(
      title: 'Scheduled',
      bottom: bottom,
      actions: [
        GestureDetector(
          onTap: onCalendarTap,
          child: Image.asset(
            AppAssets.appbarCalendar,
            scale: 4,
            color: !isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
          ),
        ),
        const Gap(8),
        GestureDetector(
          onTap: onEditTap,
          child: Image.asset(
            AppAssets.appbarCalendarEdit,
            color: isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
            scale: 4,
          ),
        ),
        const Gap(8),
        GestureDetector(
          onTap: () {
            openRouteMap(context);
            // context.router.push(const JourneyMapRoute());
          },
          child: Image.asset(
            AppAssets.appbarMap,
            scale: 4,
          ),
        ),
        const Gap(16)
      ],
    );
  }

  // Open route map using StoreTrack web service
  static Future<void> openRouteMap(BuildContext context) async {
    try {
      // Get current location
      final locationService = sl<LocationService>();
      final currentLocation = await locationService.getCurrentPosition();

      if (!context.mounted) return;

      if (currentLocation == null) {
        SnackBarService.warning(
          context: context,
          message:
              'Unable to get current location. Please enable location services.',
        );
        return;
      }

      // Get user ID
      final userID = await sl<DataManager>().getUserId() ?? "0";

      // Construct current location string
      final currentLatLng =
          "${currentLocation.latitude},${currentLocation.longitude}";

      // Construct the StoreTrack route URL
      final routeUrl =
          "https://webservice.storetrack.com.au/standalone/route.aspx?pid=$userID&latlong=$currentLatLng";

      // Launch the URL externally
      // final uri = Uri.parse(routeUrl);
      // if (await canLaunchUrl(uri)) {
      //   await launchUrl(uri, mode: LaunchMode.externalApplication);
      // } else {
      //   if (context.mounted) {
      //     SnackBarService.error(
      //       context: context,
      //       message: 'Could not open route map. Please try again.',
      //     );
      //   }
      // }
      context.router.push(WebBrowserRoute(url: routeUrl));
    } catch (e) {
      if (context.mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error opening route map: ${e.toString()}',
        );
      }
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 48);
}
