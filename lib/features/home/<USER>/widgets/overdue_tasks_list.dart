import 'package:flutter/material.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as schedule;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/reorderable_store_list.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';

class OverdueTasksList extends StatelessWidget {
  final List<schedule.TaskDetail> allApiTasks;
  final bool isCheckboxMode;
  final bool areAllItemsSelected;
  final Function(List<TaskDetail>, List<TaskDetail>) onSelectionChanged;
  final TaskDetail Function(schedule.TaskDetail) convertScheduleToDatum;

  const OverdueTasksList({
    super.key,
    required this.allApiTasks,
    required this.isCheckboxMode,
    required this.areAllItemsSelected,
    required this.onSelectionChanged,
    required this.convertScheduleToDatum,
  });

  @override
  Widget build(BuildContext context) {
    // Get today's date at midnight for comparison
    final DateTime today =
        DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);

    // Filter tasks that are scheduled before today
    List<TaskDetail> overdueTasks = allApiTasks
        .where((task) =>
            task.taskStatus == "Confirmed" &&
            task.isOpen == false &&
            task.scheduledTimeStamp != null &&
            task.scheduledTimeStamp!.isBefore(today))
        .toList()
      // Sort by scheduled date and time, then by store name
      ..sort((a, b) => (a.scheduledTimeStamp ?? DateTime.now())
          .compareTo(b.scheduledTimeStamp ?? DateTime.now()))
      ..sort((a, b) => (a.storeName ?? '').compareTo(b.storeName ?? ''));

    // If no overdue tasks, show empty state
    if (overdueTasks.isEmpty) {
      return const EmptyState(message: 'No overdue tasks');
    }

    // Convert schedule.Datum to unscheduled Datum for compatibility with ReorderableStoreList
    List<TaskDetail> convertedTasks =
        overdueTasks.map((task) => convertScheduleToDatum(task)).toList();

    // Return the list of overdue tasks
    return Container(
      constraints: const BoxConstraints(minHeight: 0),
      child: ReorderableStoreList(
        tasks: convertedTasks,
        isCalendarMode: isCheckboxMode,
        showScheduledDate: true, // Show date for overdue tasks
        showTickIndicator: true,
        showAllDisclosureIndicator: false,
        permanentlyDisableAllDisclosureIndicator: false,
        isOpenTask: true,
        onSelectionChanged: onSelectionChanged,
        selectAll: areAllItemsSelected,
      ),
    );
  }
}
