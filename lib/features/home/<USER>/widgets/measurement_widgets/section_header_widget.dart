import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';

class SectionHeaderWidget extends StatelessWidget {
  final String title;
  final int completedCount;
  final int totalCount;
  final bool showProgress;

  const SectionHeaderWidget({
    super.key,
    required this.title,
    this.completedCount = 0,
    this.totalCount = 0,
    this.showProgress = true,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: AppColors.lightGrey2,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              title,
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: AppColors.black,
              ),
            ),
          ),
          if (showProgress)
            Text(
              '$completedCount of $totalCount',
              style: textTheme.bodyMedium?.copyWith(
                color: AppColors.blackTint1,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
        ],
      ),
    );
  }
}
