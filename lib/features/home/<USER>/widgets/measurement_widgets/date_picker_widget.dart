import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_upload_widget.dart'
    show PhotoUploadWidget;

class DatePickerWidget extends StatelessWidget {
  final Measurement measurement;
  final String? value;
  final Function(String?) onChanged;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final VoidCallback? onCameraTap;
  final bool isRequired;
  final String? errorText;
  final List<String> selectedImages;
  final String? photoErrorText;
  final PhotoTagsT? photoTag;

  const DatePickerWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.showCameraIcon = false,
    this.isCameraMandatory = false,
    this.onCameraTap,
    this.isRequired = false,
    this.errorText,
    this.selectedImages = const [],
    this.photoErrorText,
    this.photoTag,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final DateTime initialDate =
        value != null ? DateFormat('dd/MM/yyyy').parse(value!) : DateTime.now();

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  measurement.measurementDescription ?? 'Select Date',
                  style: textTheme.montserratTitleExtraSmall,
                ),
              ),
              if (isRequired)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.priority_high,
                    color: AppColors.loginRed,
                    size: 16,
                  ),
                ),
            ],
          ),
          const Gap(16),
          InkWell(
            onTap: () async {
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: initialDate ?? DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime(2100),
                builder: (context, child) {
                  return Theme(
                    data: Theme.of(context).copyWith(
                      colorScheme: Theme.of(context).colorScheme.copyWith(
                            primary: AppColors.primaryBlue,
                            onPrimary: Colors.white,
                            surface: Colors.white,
                            onSurface: AppColors.black,
                            primaryContainer:
                                AppColors.primaryBlue.withOpacity(0.1),
                            onPrimaryContainer: AppColors.primaryBlue,
                            secondary: AppColors.primaryBlue.withOpacity(0.8),
                            onSecondary: Colors.white,
                            outline: AppColors.blackTint2.withOpacity(0.3),
                          ),
                      datePickerTheme: DatePickerThemeData(
                        backgroundColor: Colors.white,
                        elevation: 24,
                        shadowColor: AppColors.black10.withOpacity(0.3),
                        surfaceTintColor: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        headerBackgroundColor: AppColors.primaryBlue,
                        headerForegroundColor: Colors.white,
                        headerHeadlineStyle: textTheme.headlineSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 24,
                        ),
                        headerHelpStyle: textTheme.bodySmall?.copyWith(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14,
                        ),
                        weekdayStyle: textTheme.bodySmall?.copyWith(
                          color: AppColors.blackTint1,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                        dayStyle: textTheme.bodyMedium?.copyWith(
                          color: AppColors.black,
                          fontSize: 14,
                        ),
                        dayForegroundColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (states.contains(WidgetState.selected)) {
                            return Colors.white;
                          }
                          if (states.contains(WidgetState.disabled)) {
                            return AppColors.blackTint2;
                          }
                          return AppColors.black;
                        }),
                        dayBackgroundColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (states.contains(WidgetState.selected)) {
                            return AppColors.primaryBlue;
                          }
                          if (states.contains(WidgetState.hovered)) {
                            return AppColors.primaryBlue.withOpacity(0.1);
                          }
                          return Colors.transparent;
                        }),
                        dayOverlayColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (states.contains(WidgetState.pressed)) {
                            return AppColors.primaryBlue.withOpacity(0.2);
                          }
                          if (states.contains(WidgetState.focused)) {
                            return AppColors.primaryBlue.withOpacity(0.1);
                          }
                          return Colors.transparent;
                        }),
                        todayBackgroundColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (states.contains(WidgetState.selected)) {
                            return AppColors.primaryBlue;
                          }
                          return Colors.transparent;
                        }),
                        todayForegroundColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (states.contains(WidgetState.selected)) {
                            return Colors.white;
                          }
                          return AppColors.primaryBlue;
                        }),
                        todayBorder: const BorderSide(
                          color: AppColors.primaryBlue,
                          width: 2,
                        ),
                        yearStyle: textTheme.bodyLarge?.copyWith(
                          color: AppColors.black,
                          fontSize: 16,
                        ),
                        yearForegroundColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (states.contains(WidgetState.selected)) {
                            return Colors.white;
                          }
                          return AppColors.black;
                        }),
                        yearBackgroundColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (states.contains(WidgetState.selected)) {
                            return AppColors.primaryBlue;
                          }
                          return Colors.transparent;
                        }),
                        yearOverlayColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (states.contains(WidgetState.pressed)) {
                            return AppColors.primaryBlue.withOpacity(0.2);
                          }
                          return Colors.transparent;
                        }),
                        rangePickerBackgroundColor: Colors.white,
                        rangePickerElevation: 12,
                        rangePickerShadowColor:
                            AppColors.black10.withOpacity(0.2),
                        rangePickerSurfaceTintColor: Colors.transparent,
                        rangePickerShape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        rangePickerHeaderBackgroundColor: AppColors.primaryBlue,
                        rangePickerHeaderForegroundColor: Colors.white,
                        rangeSelectionBackgroundColor:
                            AppColors.primaryBlue.withOpacity(0.1),
                        rangeSelectionOverlayColor: WidgetStateProperty.all(
                          AppColors.primaryBlue.withOpacity(0.1),
                        ),
                        dividerColor: AppColors.blackTint2.withOpacity(0.2),
                        inputDecorationTheme: InputDecorationTheme(
                          filled: true,
                          fillColor: Colors.grey.shade50,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: BorderSide(
                              color: AppColors.blackTint2.withOpacity(0.3),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: const BorderSide(
                              color: AppColors.primaryBlue,
                              width: 2,
                            ),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        confirmButtonStyle: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryBlue,
                          foregroundColor: Colors.white,
                          elevation: 4,
                          shadowColor: AppColors.primaryBlue.withOpacity(0.3),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          textStyle: textTheme.labelLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        cancelButtonStyle: TextButton.styleFrom(
                          foregroundColor: AppColors.blackTint1,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          textStyle: textTheme.labelLarge?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    child: child!,
                  );
                },
              );
              if (picked != null) {
                onChanged(DateFormat('dd/MM/yyyy').format(picked));
              }
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 16.0,
              ),
              decoration: BoxDecoration(
                border: Border.all(
                  color: errorText != null ? Colors.red : AppColors.blackTint2,
                ),
                borderRadius: BorderRadius.circular(10.0),
                color: Colors.white,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    value != null && value!.isNotEmpty
                        ? value!
                        : 'Select a date',
                    style: textTheme.montserratTitleExtraSmall.copyWith(
                      color: value != null && value!.isNotEmpty
                          ? AppColors.black
                          : AppColors.blackTint1,
                    ),
                  ),
                  const Icon(
                    Icons.calendar_today,
                    color: AppColors.primaryBlue,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
          if (showCameraIcon) ...[
            const Gap(16),
            PhotoUploadWidget(
              selectedImages: selectedImages,
              errorText: photoErrorText,
              photoTag: photoTag,
              onCameraPressed: () {
                if (onCameraTap != null) {
                  onCameraTap!();
                }
              },
              onImagesTap: () {
                if (onCameraTap != null) {
                  onCameraTap!();
                }
              },
            ),
          ],
          if (errorText != null)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Text(
                errorText!,
                style: textTheme.montserratTableSmall.copyWith(
                  color: Colors.red,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
