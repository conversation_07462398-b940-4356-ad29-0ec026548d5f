import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/keyboard_type_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class ConditionalTextWidget extends StatefulWidget {
  final Measurement measurement;
  final String? value;
  final Function(String?) onChanged;
  final String? triggerValue; // The value that triggers this text field to show
  final String? currentRadioValue; // Current value of the radio button

  const ConditionalTextWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.triggerValue,
    this.currentRadioValue,
  });

  @override
  State<ConditionalTextWidget> createState() => _ConditionalTextWidgetState();
}

class _ConditionalTextWidgetState extends State<ConditionalTextWidget> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value ?? '');
  }

  @override
  void didUpdateWidget(ConditionalTextWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = widget.value ?? '';
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  bool get shouldShow {
    return widget.triggerValue != null &&
        widget.currentRadioValue == widget.triggerValue;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    if (!shouldShow) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.measurement.measurementDescription ?? 'Please specify why',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: AppColors.black,
            ),
          ),
          if (widget.measurement.required != true)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(
                'Optional',
                style: textTheme.bodySmall?.copyWith(
                  color: AppColors.blackTint1,
                  fontSize: 13,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          const Gap(12),
          TextFormField(
            controller: _controller,
            onChanged: widget.onChanged,
            maxLines: 3,
            keyboardType: widget.measurement.measurementTypeId?.keyboardType,
            decoration: InputDecoration(
              hintText: 'Enter your text here...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.blackTint2),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.blackTint2),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(color: AppColors.primaryBlue, width: 2),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 16.0,
              ),
            ),
            style: textTheme.bodyMedium?.copyWith(
              color: AppColors.black,
              fontSize: 15,
            ),
          ),
          if (widget.measurement.required == true &&
              (widget.value?.isEmpty ?? true))
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                'This field is required',
                style: textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
