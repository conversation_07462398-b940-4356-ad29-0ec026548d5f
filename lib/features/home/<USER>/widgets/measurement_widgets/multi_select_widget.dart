import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_upload_widget.dart'
    show PhotoUploadWidget;

class MultiSelectWidget extends StatelessWidget {
  final Measurement measurement;
  final List<String> value;
  final Function(List<String>) onChanged;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final VoidCallback? onCameraTap;
  final bool isRequired;
  final String? errorText;
  final List<String> selectedImages;
  final String? photoErrorText;
  final PhotoTagsT? photoTag;

  const MultiSelectWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.showCameraIcon = false,
    this.isCameraMandatory = false,
    this.onCameraTap,
    this.isRequired = false,
    this.errorText,
    this.selectedImages = const [],
    this.photoErrorText,
    this.photoTag,
  });

  void _showMultiSelectBottomSheet(BuildContext context) {
    final options = measurement.measurementOptions ?? [];
    final selectedIds = List<String>.from(value);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            // Calculate dynamic height based on content
            const itemHeight = 57.0; // Height per ListTile (56 + 1 for divider)
            const handleBarHeight = 20.0; // Handle bar with margin
            const headerHeight =
                60.0; // Header text with padding (20 * 2 + text height)
            const containerMargin = 32.0; // 16 * 2 for top and bottom margin
            const bottomButtonsHeight = 88.0; // Bottom buttons with padding
            const maxOptionsToShowFully = 7;

            final screenHeight = MediaQuery.of(context).size.height;
            final maxAllowedHeight = screenHeight * 0.7;

            // Calculate height based on number of options
            double finalHeight;
            if (options.length <= maxOptionsToShowFully) {
              // Show exact required height for up to 7 options
              final contentHeight = (options.length * itemHeight) -
                  1; // -1 to remove last divider
              finalHeight = handleBarHeight +
                  headerHeight +
                  containerMargin +
                  contentHeight +
                  bottomButtonsHeight;
            } else {
              // Limit to 70% of screen height for more than 7 options
              finalHeight = maxAllowedHeight;
            }

            return Container(
              height: finalHeight,
              decoration: const BoxDecoration(
                color: AppColors.lightGrey1,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: AppColors.blackTint2,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  // Header
                  Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Text(
                      measurement.measurementDescription ?? 'Select options',
                      style: Theme.of(context)
                          .textTheme
                          .montserratNavigationPrimaryMedium,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  // Options list
                  options.length <= maxOptionsToShowFully
                      ? Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          margin: const EdgeInsets.all(16),
                          height: (options.length * itemHeight) -
                              1, // Exact height for small lists
                          child: ListView.separated(
                            padding: EdgeInsets.zero,
                            physics:
                                const NeverScrollableScrollPhysics(), // Disable scrolling for exact height
                            itemCount: options.length,
                            itemBuilder: (context, index) {
                              final option = options[index];
                              final optionId =
                                  option.measurementOptionId?.toString() ?? '';
                              final isSelected = selectedIds.contains(optionId);

                              return InkWell(
                                onTap: () {
                                  setModalState(() {
                                    if (isSelected) {
                                      selectedIds.remove(optionId);
                                    } else {
                                      selectedIds.add(optionId);
                                    }
                                  });
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 16.0,
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          option.measurementOptionDescription ??
                                              'Unnamed Option',
                                          style: Theme.of(context)
                                              .textTheme
                                              .montserratTitleExtraSmall,
                                        ),
                                      ),
                                      const Gap(16),
                                      Container(
                                        width: 24,
                                        height: 24,
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? AppColors.primaryBlue
                                              : Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(4),
                                          border: Border.all(
                                            color: isSelected
                                                ? AppColors.primaryBlue
                                                : AppColors.blackTint2,
                                            width: 1.5,
                                          ),
                                        ),
                                        child: isSelected
                                            ? const Icon(
                                                Icons.check,
                                                size: 16,
                                                color: Colors.white,
                                              )
                                            : null,
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                            separatorBuilder:
                                (BuildContext context, int index) {
                              return const Divider(
                                  height: 1, color: AppColors.blackTint2);
                            },
                          ),
                        )
                      : Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                            margin: const EdgeInsets.all(16),
                            child: ListView.separated(
                              padding: EdgeInsets.zero,
                              itemCount: options.length,
                              itemBuilder: (context, index) {
                                final option = options[index];
                                final optionId =
                                    option.measurementOptionId?.toString() ??
                                        '';
                                final isSelected =
                                    selectedIds.contains(optionId);

                                return InkWell(
                                  onTap: () {
                                    setModalState(() {
                                      if (isSelected) {
                                        selectedIds.remove(optionId);
                                      } else {
                                        selectedIds.add(optionId);
                                      }
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0,
                                      vertical: 16.0,
                                    ),
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            option.measurementOptionDescription ??
                                                'Unnamed Option',
                                            style: Theme.of(context)
                                                .textTheme
                                                .montserratTitleExtraSmall,
                                          ),
                                        ),
                                        const Gap(16),
                                        Container(
                                          width: 24,
                                          height: 24,
                                          decoration: BoxDecoration(
                                            color: isSelected
                                                ? AppColors.primaryBlue
                                                : Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(4),
                                            border: Border.all(
                                              color: isSelected
                                                  ? AppColors.primaryBlue
                                                  : AppColors.blackTint2,
                                              width: 1.5,
                                            ),
                                          ),
                                          child: isSelected
                                              ? const Icon(
                                                  Icons.check,
                                                  size: 16,
                                                  color: Colors.white,
                                                )
                                              : null,
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                              separatorBuilder:
                                  (BuildContext context, int index) {
                                return const Divider(
                                    height: 1, color: AppColors.blackTint2);
                              },
                            ),
                          ),
                        ),
                  // Bottom buttons
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: AppButton(
                            height: 40,
                            text: 'Cancel',
                            color: Colors.white,
                            textColor: AppColors.black,
                            onPressed: () {
                              Navigator.pop(context);
                            },
                          ),
                        ),
                        const Gap(16),
                        Expanded(
                          child: AppButton(
                            height: 40,
                            text: 'Confirm',
                            color: AppColors.primaryBlue,
                            onPressed: () {
                              onChanged(selectedIds);
                              Navigator.pop(context);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final options = measurement.measurementOptions ?? [];

    // Convert option IDs to descriptions for display
    final selectedDescriptions = value
        .map((id) {
          final option = options.firstWhere(
            (opt) => opt.measurementOptionId?.toString() == id,
            orElse: () => MeasurementOption(),
          );
          return option.measurementOptionDescription ?? 'Unknown';
        })
        .where((desc) => desc != 'Unknown')
        .toList();

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with required indicator
          Row(
            children: [
              Expanded(
                child: Text(
                  measurement.measurementDescription ?? 'Multi-Select',
                  style: textTheme.montserratTitleExtraSmall,
                ),
              ),
              if (isRequired)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.priority_high,
                    color: AppColors.loginRed,
                    size: 16,
                  ),
                ),
            ],
          ),
          const Gap(16),
          InkWell(
            onTap: () => _showMultiSelectBottomSheet(context),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 16.0,
              ),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.blackTint2),
                borderRadius: BorderRadius.circular(10.0),
                color: Colors.white,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      selectedDescriptions.isNotEmpty
                          ? selectedDescriptions.join(', ')
                          : 'Select...',
                      style: textTheme.montserratTitleExtraSmall.copyWith(
                        color: selectedDescriptions.isNotEmpty
                            ? AppColors.black
                            : AppColors.blackTint1,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const Icon(
                    Icons.keyboard_arrow_down,
                    color: AppColors.primaryBlue,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
          if (selectedDescriptions.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                '${selectedDescriptions.length} option(s) selected',
                style: textTheme.montserratTableSmall.copyWith(
                  color: AppColors.blackTint1,
                ),
              ),
            ),
          // Camera section
          if (showCameraIcon) ...[
            const Gap(16),
            PhotoUploadWidget(
              selectedImages: selectedImages,
              errorText: photoErrorText,
              photoTag: photoTag,
              onCameraPressed: () {
                if (onCameraTap != null) {
                  onCameraTap!();
                }
              },
              onImagesTap: () {
                if (onCameraTap != null) {
                  onCameraTap!();
                }
              },
            ),
          ],
          if (errorText != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                errorText!,
                style: textTheme.montserratTableSmall.copyWith(
                  color: Colors.red,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
