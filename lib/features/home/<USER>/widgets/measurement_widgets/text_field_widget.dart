import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/keyboard_type_extensions.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_upload_widget.dart'
    show PhotoUploadWidget;

class TextFieldWidget extends StatefulWidget {
  final Measurement measurement;
  final String value;
  final Function(String) onChanged;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final VoidCallback? onCameraTap;
  final bool isRequired;
  final String? errorText;
  final List<String> selectedImages;
  final String? photoErrorText;
  final PhotoTagsT? photoTag;

  const TextFieldWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.showCameraIcon = false,
    this.isCameraMandatory = false,
    this.onCameraTap,
    this.isRequired = false,
    this.errorText,
    this.selectedImages = const [],
    this.photoErrorText,
    this.photoTag,
  });

  @override
  State<TextFieldWidget> createState() => _TextFieldWidgetState();
}

class _TextFieldWidgetState extends State<TextFieldWidget> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value);
  }

  @override
  void didUpdateWidget(TextFieldWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = widget.value;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with required indicator
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.measurement.measurementDescription ?? 'Enter Text',
                  style: textTheme.montserratTitleExtraSmall,
                ),
              ),
              if (widget.isRequired)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.priority_high,
                    color: AppColors.loginRed,
                    size: 16,
                  ),
                ),
            ],
          ),
          const Gap(16),
          TextFormField(
            controller: _controller,
            onChanged: widget.onChanged,
            keyboardType: widget.measurement.measurementTypeId?.keyboardType,
            decoration: InputDecoration(
              hintText: 'Enter your text here...',
              hintStyle: textTheme.montserratTitleExtraSmall.copyWith(
                color: AppColors.blackTint1,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: BorderSide(
                  color: widget.errorText != null
                      ? Colors.red
                      : AppColors.blackTint2,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: BorderSide(
                  color: widget.errorText != null
                      ? Colors.red
                      : AppColors.blackTint2,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: BorderSide(
                  color: widget.errorText != null
                      ? Colors.red
                      : AppColors.primaryBlue,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 16.0,
              ),
              errorText: widget.errorText,
              errorStyle: textTheme.montserratTableSmall.copyWith(
                color: Colors.red,
              ),
            ),
            style: textTheme.montserratTitleExtraSmall.copyWith(
              color: AppColors.black,
            ),
          ),
          // Camera section
          if (widget.showCameraIcon) ...[
            const Gap(16),
            PhotoUploadWidget(
              selectedImages: widget.selectedImages,
              errorText: widget.photoErrorText,
              photoTag: widget.photoTag,
              onCameraPressed: () {
                if (widget.onCameraTap != null) {
                  widget.onCameraTap!();
                }
              },
              onImagesTap: () {
                if (widget.onCameraTap != null) {
                  widget.onCameraTap!();
                }
              },
            ),
          ],
        ],
      ),
    );
  }
}
