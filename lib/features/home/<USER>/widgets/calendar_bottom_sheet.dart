import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';

class CalendarBottomSheet extends StatefulWidget {
  final Function(DateTime) onDateSelected;
  final DateTime? minDate;
  final DateTime? maxDate;
  final CalendarResponseEntity? calendarResponse;
  final List<DateTime>? taskDates; // Add parameter for dates with tasks

  const CalendarBottomSheet({
    super.key,
    required this.onDateSelected,
    this.minDate,
    this.maxDate,
    this.calendarResponse,
    this.taskDates,
  });

  static Future<DateTime?> show({
    required BuildContext context,
    DateTime? minDate,
    DateTime? maxDate,
    CalendarResponseEntity? calendarResponse,
    List<DateTime>? taskDates,
  }) async {
    return await showModalBottomSheet<DateTime>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(
          decoration: const BoxDecoration(
            color: AppColors.lightGrey2,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: CalendarBottomSheet(
            onDateSelected: (date) {
              Navigator.pop(context, date);
            },
            minDate: minDate,
            maxDate: maxDate,
            calendarResponse: calendarResponse,
            taskDates: taskDates,
          ),
        ),
      ),
    );
  }

  @override
  State<CalendarBottomSheet> createState() => _CalendarBottomSheetState();
}

class _CalendarBottomSheetState extends State<CalendarBottomSheet> {
  late DateTime _focusedDay;
  late DateTime _selectedDay;
  late CalendarFormat _calendarFormat;
  bool _isDateExplicitlySelected = false;

  // Map to store dates with dollar symbol and budget amount
  Map<String, num> _dollarSymbolDates = {};

  // Map to store dates with value dates and budget amount
  Map<String, num> _valueDates = {};

  // Set to store dates with tasks
  Set<String> _taskDates = {};

  @override
  void initState() {
    super.initState();
    // Initialize with current date
    DateTime now = DateTime.now();

    // Get the valid first and last days
    // Make sure firstDay is not after lastDay
    DateTime firstDay = widget.minDate ?? DateTime.utc(2020, 1, 1);
    DateTime lastDay = widget.maxDate ?? DateTime.utc(2030, 12, 31);

    // Ensure firstDay is not after lastDay
    if (firstDay.isAfter(lastDay)) {
      // If minDate is after maxDate, use minDate for both to avoid errors
      firstDay = widget.minDate ?? DateTime.utc(2020, 1, 1);
      lastDay = firstDay;
    }

    // Ensure the initial selected date is within the allowed range
    if (now.isBefore(firstDay)) {
      _selectedDay = firstDay;
      _focusedDay = firstDay;
    } else if (now.isAfter(lastDay)) {
      _selectedDay = lastDay;
      _focusedDay = lastDay;
    } else {
      _selectedDay = now;
      _focusedDay = now;
    }

    _calendarFormat = CalendarFormat.month;

    // Process calendar data if available
    _processDollarSymbolDates();
    _processValueDates();

    // Process task dates if available
    _processTaskDates();
  }

  void _processDollarSymbolDates() {
    // Clear existing data
    _dollarSymbolDates = {};

    // Process calendar data if available
    if (widget.calendarResponse?.data?.calendarInfo != null) {
      for (var info in widget.calendarResponse!.data!.calendarInfo!) {
        if (info.timestamp != null && info.dollarSymbol == true) {
          final dateKey = DateFormat('yyyy-MM-dd').format(info.timestamp!);
          // Store budget amount if available, otherwise store 0
          _dollarSymbolDates[dateKey] = info.budgetAmount ?? 0;
        }
      }
    }
  }

  void _processValueDates() {
    // Clear existing data
    _valueDates = {};

    // Process calendar data if available
    if (widget.calendarResponse?.data?.calendarInfo != null) {
      for (var info in widget.calendarResponse!.data!.calendarInfo!) {
        if (info.timestamp != null &&
            info.budgetAmount != null &&
            info.budgetAmount! > 0) {
          final dateKey = DateFormat('yyyy-MM-dd').format(info.timestamp!);
          // Store budget amount
          _valueDates[dateKey] = info.budgetAmount!;
        }
      }
    }
  }

  void _processTaskDates() {
    // Clear existing data
    _taskDates = {};

    // Process task dates if available
    if (widget.taskDates != null && widget.taskDates!.isNotEmpty) {
      for (var date in widget.taskDates!) {
        final dateKey = DateFormat('yyyy-MM-dd').format(date);
        _taskDates.add(dateKey);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;

    // Get the valid first and last days
    DateTime firstDay = widget.minDate ?? DateTime.utc(2020, 1, 1);
    DateTime lastDay = widget.maxDate ?? DateTime.utc(2030, 12, 31);

    // Ensure firstDay is not after lastDay
    if (firstDay.isAfter(lastDay)) {
      firstDay = widget.minDate ?? DateTime.utc(2020, 1, 1);
      lastDay = firstDay;
    }

    // Ensure focusedDay is not before firstDay
    DateTime focusedDay = _focusedDay;
    if (focusedDay.isBefore(firstDay)) {
      focusedDay = firstDay;
    } else if (focusedDay.isAfter(lastDay)) {
      focusedDay = lastDay;
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar at top
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade400,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const Gap(24),
          // Title
          Text(
            'Choose date',
            style: textTheme.montserratNavigationPrimaryMedium,
          ),
          const Gap(24),
          // Calendar container
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.only(left: 8.0, right: 8.0, bottom: 16),
              child: TableCalendar(
                firstDay: firstDay,
                lastDay: lastDay,
                focusedDay: focusedDay,
                calendarFormat: _calendarFormat,
                startingDayOfWeek: StartingDayOfWeek.sunday,
                headerStyle: HeaderStyle(
                  titleCentered: true,
                  formatButtonVisible: false,
                  titleTextStyle: textTheme.montserratTitleSmall,
                  leftChevronIcon: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.chevron_left),
                  ),
                  rightChevronIcon: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.chevron_right),
                  ),
                  headerPadding: const EdgeInsets.symmetric(vertical: 16),
                ),
                daysOfWeekStyle: DaysOfWeekStyle(
                  weekdayStyle: textTheme.montserratMetricsAxisRegular,
                  weekendStyle: textTheme.montserratMetricsAxisRegular,
                ),
                calendarStyle: CalendarStyle(
                  outsideDaysVisible: false,
                  // We're using custom builders, so these styles won't be used for days with dollar symbols
                  // But we keep them for other calendar elements
                  defaultTextStyle: textTheme.montserratTableSmall,
                  weekendTextStyle: textTheme.montserratTableSmall,
                  outsideTextStyle: textTheme.montserratTableSmall,
                  // We'll use todayBuilder instead, but keep a transparent decoration
                  todayDecoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.transparent,
                  ),
                  todayTextStyle: textTheme.montserratTableSmall.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                  selectedDecoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.black,
                  ),
                  selectedTextStyle: textTheme.montserratTableSmall.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                  disabledTextStyle: textTheme.montserratTableSmall.copyWith(
                    color: Colors.grey.shade400,
                  ),
                  disabledDecoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey.shade100,
                  ),
                  cellMargin: const EdgeInsets.all(4),
                ),
                selectedDayPredicate: (day) {
                  return isSameDay(_selectedDay, day);
                },
                enabledDayPredicate: (day) {
                  // Check if the day is within the allowed range
                  if (widget.minDate != null && day.isBefore(widget.minDate!)) {
                    return false;
                  }
                  if (widget.maxDate != null && day.isAfter(widget.maxDate!)) {
                    return false;
                  }
                  return true;
                },
                onDaySelected: (selectedDay, focusedDay) {
                  // Check if the selected day is within the allowed range
                  bool isValidDate = true;
                  String errorMessage = '';

                  if (widget.minDate != null &&
                      selectedDay.isBefore(widget.minDate!)) {
                    isValidDate = false;
                    errorMessage =
                        'Selected date must be on or after ${DateFormat('MMM dd, yyyy').format(widget.minDate!)} (latest start date)';
                  } else if (widget.maxDate != null &&
                      selectedDay.isAfter(widget.maxDate!)) {
                    isValidDate = false;
                    errorMessage =
                        'Selected date must be on or before ${DateFormat('MMM dd, yyyy').format(widget.maxDate!)} (earliest end date)';
                  }

                  if (!isValidDate) {
                    // Show error message
                    SnackBarService.warning(
                      context: context,
                      message: errorMessage,
                    );
                    return;
                  }

                  setState(() {
                    _selectedDay = selectedDay;
                    _focusedDay = focusedDay;
                    _isDateExplicitlySelected = true;
                  });
                },
                onPageChanged: (focusedDay) {
                  // Ensure the focused day is within the valid range
                  DateTime firstDay =
                      widget.minDate ?? DateTime.utc(2020, 1, 1);
                  DateTime lastDay =
                      widget.maxDate ?? DateTime.utc(2030, 12, 31);

                  if (focusedDay.isBefore(firstDay)) {
                    _focusedDay = firstDay;
                  } else if (focusedDay.isAfter(lastDay)) {
                    _focusedDay = lastDay;
                  } else {
                    _focusedDay = focusedDay;
                  }
                },
                calendarBuilders: CalendarBuilders(
                  // Add todayBuilder to customize the today indicator with a smaller radius
                  todayBuilder: (context, day, focusedDay) {
                    return Container(
                      margin: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.primaryBlue,
                      ),
                      width: 24, // Reduced size for smaller radius
                      height: 24,
                      alignment: Alignment.center,
                      child: Text(
                        day.day.toString(),
                        style: textTheme.montserratTableSmall.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    );
                  },
                  // Add selectedDayBuilder to match the today indicator style but with black color
                  selectedBuilder: (context, day, focusedDay) {
                    return Container(
                      margin: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.black,
                      ),
                      width: 24, // Reduced size for smaller radius
                      height: 24,
                      alignment: Alignment.center,
                      child: Text(
                        day.day.toString(),
                        style: textTheme.montserratTableSmall.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    );
                  },
                  markerBuilder: (context, date, events) {
                    // Format date for lookup
                    final dateKey = DateFormat('yyyy-MM-dd').format(date);

                    // Check if this date has a dollar symbol or value date
                    final bool hasDollarSymbol =
                        _dollarSymbolDates.containsKey(dateKey);
                    final bool hasValueDate = _valueDates.containsKey(dateKey);

                    // Show markers if either dollar symbol or value date exists
                    if (hasDollarSymbol || hasValueDate) {
                      List<Widget> markerWidgets = [];

                      // Add dollar symbol marker if exists
                      if (hasDollarSymbol) {
                        final budgetAmount = _dollarSymbolDates[dateKey]!;
                        markerWidgets.add(
                          // Dollar icon in green
                          Text(
                            '\$',
                            style: textTheme.montserratTableSmall.copyWith(
                              color: AppColors
                                  .loginGreen, // Green color for dollar icon
                              fontWeight: FontWeight.w600,
                              fontSize: 10,
                            ),
                          ),
                        );
                        // Show budget amount if greater than zero
                        if (budgetAmount > 0) {
                          markerWidgets.add(
                            Text(
                              budgetAmount.toStringAsFixed(2),
                              style: textTheme.montserratTableSmall.copyWith(
                                color: AppColors
                                    .black, // Black color for budget amount
                                fontWeight: FontWeight.w500,
                                fontSize: 8,
                              ),
                            ),
                          );
                        }
                      }

                      // Add value marker if exists (and not already showing dollar symbol)
                      if (hasValueDate && !hasDollarSymbol) {
                        final valueAmount = _valueDates[dateKey]!;
                        // markerWidgets.add(
                        //   // V icon in blue for value
                        //   Text(
                        //     'V',
                        //     style: textTheme.montserratTableSmall.copyWith(
                        //       color: AppColors.primaryBlue, // Blue color for value icon
                        //       fontWeight: FontWeight.w600,
                        //       fontSize: 10,
                        //     ),
                        //   ),
                        // );
                        // Show value amount if greater than zero
                        if (valueAmount > 0) {
                          markerWidgets.add(
                            Text(
                              valueAmount.toStringAsFixed(2),
                              style: textTheme.montserratTableSmall.copyWith(
                                color: AppColors
                                    .primaryBlue, // Blue color for value amount
                                fontWeight: FontWeight.w500,
                                fontSize: 8,
                              ),
                            ),
                          );
                        }
                      }

                      return Positioned(
                        bottom: 1,
                        right: 1,
                        child: Container(
                          padding: const EdgeInsets.all(1),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: markerWidgets,
                          ),
                        ),
                      );
                    }

                    return null;
                  },
                  defaultBuilder: (context, day, focusedDay) {
                    // Format date for lookup
                    final dateKey = DateFormat('yyyy-MM-dd').format(day);
                    final bool hasDollarSymbol =
                        _dollarSymbolDates.containsKey(dateKey);
                    final bool hasValueDate = _valueDates.containsKey(dateKey);
                    final bool hasTask = _taskDates.contains(dateKey);

                    // Check if the day is selected
                    final bool isSelected = isSameDay(_selectedDay, day);

                    // Check if the day is today
                    final bool isToday = isSameDay(DateTime.now(), day);

                    // Determine text style based on selection, dollar symbol, and value date
                    TextStyle textStyle = textTheme.montserratTableSmall;

                    if (isSelected) {
                      textStyle = textTheme.montserratTableSmall.copyWith(
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      );
                    } else if (isToday) {
                      textStyle = textTheme.montserratTableSmall.copyWith(
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      );
                    } else if (hasDollarSymbol || hasValueDate) {
                      // Apply dim style for dates with dollar symbol or value dates
                      textStyle = textTheme.montserratTableSmall.copyWith(
                        color: Colors.grey.shade400,
                      );
                    }

                    // Determine decoration based on selection and today
                    BoxDecoration decoration;
                    if (isSelected) {
                      decoration = const BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.black,
                      );
                    } else if (isToday) {
                      decoration = const BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.primaryBlue,
                      );
                    } else if (hasTask) {
                      // Grey background for dates with tasks (always show task indicator)
                      decoration = BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey.shade200,
                      );
                    } else {
                      // Default decoration (no background)
                      decoration = const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.transparent,
                      );
                    }

                    return Container(
                      decoration: decoration,
                      margin: const EdgeInsets.all(4),
                      padding: const EdgeInsets.all(1),
                      width:
                          24, // Match the radius of todayBuilder and selectedBuilder
                      height:
                          24, // Match the radius of todayBuilder and selectedBuilder
                      alignment: Alignment.center,
                      child: Text(
                        day.day.toString(),
                        style: textStyle,
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
          // Buttons
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: AppButton(
                    text: 'Cancel',
                    height: 40,
                    color: Colors.white,
                    textColor: AppColors.black,
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
                if (_isDateExplicitlySelected) ...[
                  const Gap(16),
                  Expanded(
                    child: AppButton(
                      text: 'Confirm',
                      height: 40,
                      color: AppColors.primaryBlue,
                      onPressed: () {
                        // Validate the selected date before confirming
                        bool isValidDate = true;
                        String errorMessage = '';

                        if (widget.minDate != null &&
                            _selectedDay.isBefore(widget.minDate!)) {
                          isValidDate = false;
                          errorMessage =
                              'Selected date must be on or after ${DateFormat('MMM dd, yyyy').format(widget.minDate!)} (latest start date)';
                        } else if (widget.maxDate != null &&
                            _selectedDay.isAfter(widget.maxDate!)) {
                          isValidDate = false;
                          errorMessage =
                              'Selected date must be on or before ${DateFormat('MMM dd, yyyy').format(widget.maxDate!)} (earliest end date)';
                        }

                        if (!isValidDate) {
                          // Show error message
                          SnackBarService.warning(
                            context: context,
                            message: errorMessage,
                          );
                          return;
                        }

                        widget.onDateSelected(_selectedDay);
                      },
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
