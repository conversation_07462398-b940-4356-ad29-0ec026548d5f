import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class PosItemCard extends StatelessWidget {
  final PosItem posItem;
  final VoidCallback? onPhotoTap;

  const PosItemCard({
    super.key,
    required this.posItem,
    this.onPhotoTap,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // Item name column
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Text(
                posItem.itemName ?? 'Unknown Item',
                style: textTheme.montserratTitleExtraSmall.copyWith(
                  color: AppColors.black,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          // Divider
          Container(
            width: 1,
            height: 60,
            color: AppColors.blackTint2,
          ),
          // Number/Amount column
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                posItem.itemAmount?.toString() ?? '0',
                style: textTheme.montserratTitleExtraSmall.copyWith(
                  color: AppColors.black,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          // Divider
          Container(
            width: 1,
            height: 60,
            color: AppColors.blackTint2,
          ),
          // Photo column
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Center(
                child: _buildPhotoSection(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoSection() {
    if (posItem.photoUrl != null && posItem.photoUrl!.isNotEmpty) {
      // Show photo thumbnail if available
      return GestureDetector(
        onTap: onPhotoTap,
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.blackTint2,
              width: 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(7),
            child: Image.network(
              posItem.photoUrl!,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return _buildPhotoPlaceholder();
              },
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return _buildPhotoPlaceholder();
              },
            ),
          ),
        ),
      );
    } else {
      // Show camera icon for taking photo
      return GestureDetector(
        onTap: onPhotoTap,
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.blackTint2,
              width: 1,
            ),
            color: AppColors.lightGrey2,
          ),
          child: const Icon(
            Icons.camera_alt_outlined,
            color: AppColors.blackTint1,
            size: 20,
          ),
        ),
      );
    }
  }

  Widget _buildPhotoPlaceholder() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: AppColors.lightGrey2,
      ),
      child: const Icon(
        Icons.image_outlined,
        color: AppColors.blackTint1,
        size: 20,
      ),
    );
  }
}
