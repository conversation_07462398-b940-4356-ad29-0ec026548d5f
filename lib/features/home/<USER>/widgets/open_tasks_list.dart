import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class OpenTasksList extends StatelessWidget {
  final List<TaskDetail> openTasks;
  final bool isCheckboxMode;
  final bool areAllItemsSelected;
  final Function(List<TaskDetail>, List<TaskDetail>) onSelectionChanged;
  final VoidCallback onSelectAll;

  const OpenTasksList({
    super.key,
    required this.openTasks,
    required this.isCheckboxMode,
    required this.areAllItemsSelected,
    required this.onSelectionChanged,
    required this.onSelectAll,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (isCheckboxMode) _buildSelectionHeader(context),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: openTasks.length,
            itemBuilder: (context, index) {
              final task = openTasks[index];
              final isSelected = isCheckboxMode && areAllItemsSelected;

              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildTaskCard(context, task, isSelected),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSelectionHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.primaryBlue.withValues(alpha: 0.1),
      child: Row(
        children: [
          Checkbox(
            value: areAllItemsSelected,
            onChanged: (value) => onSelectAll(),
            activeColor: AppColors.primaryBlue,
          ),
          Text(
            'Select All',
            style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const Spacer(),
          Text(
            '${openTasks.length} tasks available',
            style:
                Theme.of(context).textTheme.montserratTitleExtraSmall.copyWith(
                      color: AppColors.blackTint1,
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskCard(
      BuildContext context, TaskDetail task, bool isSelected) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected ? AppColors.primaryBlue : AppColors.borderColor,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            if (isCheckboxMode)
              Padding(
                padding: const EdgeInsets.only(right: 12),
                child: Checkbox(
                  value: isSelected,
                  onChanged: (value) {
                    final selectedTasks =
                        value == true ? <TaskDetail>[task] : <TaskDetail>[];
                    onSelectionChanged([task], selectedTasks);
                  },
                  activeColor: AppColors.primaryBlue,
                ),
              ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    task.storeName ?? 'Unknown Store',
                    style: Theme.of(context)
                        .textTheme
                        .montserratTitleSmall
                        .copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: 4),
                  if (task.location != null)
                    Text(
                      task.location!,
                      style: Theme.of(context)
                          .textTheme
                          .montserratParagraphSmall
                          .copyWith(
                            color: AppColors.blackTint1,
                          ),
                    ),
                  const SizedBox(height: 4),
                  if (task.budget != null)
                    Text(
                      'Budget: ${task.budget} minutes',
                      style: Theme.of(context)
                          .textTheme
                          .montserratParagraphSmall
                          .copyWith(
                            color: AppColors.blackTint1,
                          ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
