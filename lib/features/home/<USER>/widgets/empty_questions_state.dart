import 'package:flutter/material.dart';

class EmptyQuestionsState extends StatelessWidget {
  const EmptyQuestionsState({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          'No questions available for this form',
          style: textTheme.bodyLarge,
        ),
      ),
    );
  }
}
