import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/shared/widgets/html_text_widget.dart';
import 'package:webview_flutter/webview_flutter.dart';

class DocumentsView extends StatefulWidget {
  final entities.TaskDetail task;
  final bool? showBrief;
  final bool? showDocuments;

  const DocumentsView({
    super.key,
    required this.task,
    this.showBrief = false,
    this.showDocuments = false,
  });

  @override
  State<DocumentsView> createState() => _DocumentsViewState();
}

class _DocumentsViewState extends State<DocumentsView> {
  Set<num?> expandedDocuments = {};
  Set<num?> expandedSupportingDocuments = {};
  Set<int> expandedForms = {};
  Set<String> expandedDocumentSections = {};

  String? currentDocumentUrl;
  bool showWebView = false;
  WebViewController? webViewController;
  bool isWebViewLoading = true;

  bool showFormQuestions = false;
  entities.Document? currentFormDocument;

  bool isViewingBrief = false;
  bool isOverviewExpanded = false;
  bool isPrioritiesExpanded = false;

  @override
  void initState() {
    super.initState();
    if (widget.showBrief == true) {
      expandedDocumentSections.add('Main');
    }
    if (widget.showDocuments == true) {
      expandedDocumentSections.add('Supporting');
    }
  }

  @override
  Widget build(BuildContext context) {
    return _buildTaskDocumentSection(context);
  }

  Widget _buildTaskDocumentSection(BuildContext context) {
    // The brief section is now only shown when the user explicitly clicks on it
    if (isViewingBrief) {
      return _buildTaskBriefSection(context, widget.task);
    }
    if (showWebView && currentDocumentUrl != null) {
      return _buildDocumentWebView(context);
    }
    if (showFormQuestions) {
      return _buildFormQuestionsView(context, widget.task);
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget.task.documents == null
              ? const Padding(
                  padding: EdgeInsets.all(0),
                  child: EmptyState(message: 'No documents available'),
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                          top: 20, left: 16, right: 16, bottom: 8),
                      child: _buildDocumentCategory(true, context, 'Main', []),
                    ),
                    Divider(
                      color: AppColors.black10,
                      height: 1,
                      thickness: 1,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                          bottom: 12, left: 16, right: 16, top: 20),
                      child: _buildDocumentCategory(
                        false,
                        context,
                        'Supporting',
                        widget.task.documents!.toList(),
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  Widget _buildDocumentWebView(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  setState(() {
                    showWebView = false;
                    currentDocumentUrl = null;
                    webViewController = null;
                  });
                },
              ),
              Text(
                'Document Viewer',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () {
                  webViewController?.reload();
                },
              ),
            ],
          ),
          Container(
            height: MediaQuery.of(context).size.height * 0.6,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(10),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Stack(
                children: [
                  WebViewWidget(
                    controller: webViewController ?? _initWebViewController(),
                  ),
                  if (isWebViewLoading)
                    const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.primaryBlue,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormQuestionsView(
      BuildContext context, entities.TaskDetail task) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  setState(() {
                    showFormQuestions = false;
                    currentFormDocument = null;
                  });
                },
              ),
              const SizedBox(width: 48),
            ],
          ),
          Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.6,
            ),
            child: SingleChildScrollView(
              child: _buildFormQuestionsContent(context, task),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormQuestionsContent(
      BuildContext context, entities.TaskDetail task) {
    final textTheme = Theme.of(context).textTheme;

    if (task.forms == null || task.forms!.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          'No form questions available for this task.',
          style: textTheme.bodyMedium?.copyWith(
            color: AppColors.black.withValues(alpha: 0.6),
          ),
        ),
      );
    }

    List<Widget> formWidgets = [];
    for (var form in task.forms!) {
      if (form.questions != null && form.questions!.isNotEmpty) {
        formWidgets.add(_buildFormSection(context, form));
        formWidgets.add(const SizedBox(height: 16));
      }
    }

    if (formWidgets.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          'No questions found in the forms.',
          style: textTheme.bodyMedium?.copyWith(
            color: AppColors.black.withValues(alpha: 0.6),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: formWidgets,
    );
  }

  Widget _buildFormSection(BuildContext context, entities.Form form) {
    final textTheme = Theme.of(context).textTheme;
    final formId = (form.formId ?? form.hashCode).toInt();
    final isExpanded = expandedForms.contains(formId);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              if (isExpanded) {
                expandedForms.remove(formId);
              } else {
                expandedForms.add(formId);
              }
            });
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: isExpanded
                  ? const BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                    )
                  : BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    form.formName ?? 'Unnamed Form',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_right,
                  color: AppColors.black,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        if (isExpanded) ...[
          ...form.questions!.asMap().entries.map((entry) {
            final index = entry.key;
            final question = entry.value;
            final isLastQuestion = index == form.questions!.length - 1;
            return _buildQuestionAnswerItem(
                context, form, question, isLastQuestion);
          }),
        ],
      ],
    );
  }

  Widget _buildQuestionAnswerItem(
      BuildContext context, entities.Form form, entities.Question question,
      [bool isLastQuestion = false]) {
    final textTheme = Theme.of(context).textTheme;

    entities.QuestionAnswer? answer;
    if (form.questionAnswers != null) {
      try {
        answer = form.questionAnswers!.firstWhere(
          (qa) => qa.questionId == question.questionId,
        );
      } catch (e) {
        answer = null;
      }
    }

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: isLastQuestion
            ? const BorderRadius.only(
                bottomLeft: Radius.circular(10),
                bottomRight: Radius.circular(10),
              )
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HtmlTextWidget(
            text: question.questionBrief ?? 'No question description',
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getAnswerText(answer),
            style: textTheme.bodyMedium?.copyWith(
              color: AppColors.black.withValues(alpha: 0.8),
            ),
          ),
          if (question.questionBrief?.isNotEmpty == true) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: AppColors.lightGrey2.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
              child: HtmlTextWidget(
                text: 'Brief: ${question.questionBrief}',
                style: textTheme.bodySmall?.copyWith(
                  color: AppColors.black.withValues(alpha: 0.7),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _getAnswerText(entities.QuestionAnswer? answer) {
    if (answer == null) {
      return 'No answer ';
    }
    if (answer.measurementTextResult?.isNotEmpty == true) {
      return answer.measurementTextResult!;
    }
    if (answer.measurementOptionId != null) {
      return 'Option ${answer.measurementOptionId}';
    }
    if (answer.measurementOptionIds?.isNotEmpty == true) {
      return 'Options: ${answer.measurementOptionIds}';
    }
    return 'Answer provided (no text available)';
  }

  WebViewController _initWebViewController() {
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              isWebViewLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              isWebViewLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              isWebViewLoading = false;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(currentDocumentUrl!));

    return webViewController!;
  }

  Widget _buildDocumentCategory(
    bool isMainSection,
    BuildContext context,
    String title,
    List<entities.Document> documents,
  ) {
    final textTheme = Theme.of(context).textTheme;
    final isExpanded = expandedDocumentSections.contains(title);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              if (isExpanded) {
                expandedDocumentSections.remove(title);
              } else {
                expandedDocumentSections.add(title);
              }
            });
          },
          child: Container(
            color: Colors.transparent,
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 0.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(title, style: textTheme.montserratTitleSmall),
                Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_right,
                  color: AppColors.black,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 12),
        if (isExpanded) ...[
          if (isMainSection)
            _buildBriefItem(context)
          else
            documents.isEmpty
                ? EmptyState(
                    message: 'No ${title.toLowerCase()} documents available',
                    margin: const EdgeInsets.symmetric(vertical: 8),
                  )
                : Column(
                    children: documents.map((document) {
                      return _buildDocumentNameItem(context, document);
                    }).toList(),
                  ),
        ],
      ],
    );
  }

  Widget _buildBriefItem(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(10),
      ),
      child: ListTile(
        leading: Padding(
          padding: const EdgeInsets.only(left: 14.0),
          child: Image.asset(
            AppAssets.supportDocument,
            scale: 4,
            color: Colors.black,
          ),
        ),
        title: Text(
          'Brief',
          style: textTheme.montserratTitleExtraSmall.copyWith(
            color: Colors.black,
          ),
        ),
        onTap: () {
          setState(() {
            isViewingBrief = true;
          });
        },
      ),
    );
  }

  Widget _buildTaskBriefSection(
      BuildContext context, entities.TaskDetail task) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    setState(() {
                      isViewingBrief = false;
                    });
                  },
                ),
                Expanded(
                  child: Text(
                    'Brief',
                    style: textTheme.titleMedium
                        ?.copyWith(fontWeight: FontWeight.w600),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 48),
              ],
            ),
          ),
          const Divider(height: 1),
          ListTile(
            title: Text(
              'Overview',
              style: textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
            ),
            trailing: Icon(
              isOverviewExpanded
                  ? Icons.keyboard_arrow_up
                  : Icons.keyboard_arrow_right,
              color: AppColors.black,
              size: 20,
            ),
            onTap: () {
              setState(() {
                isOverviewExpanded = !isOverviewExpanded;
              });
            },
          ),
          if (isOverviewExpanded)
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: _buildOverviewTabContent(context),
            ),
          ListTile(
            title: Text(
              'Brief',
              style: textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
            ),
            trailing: Icon(
              isPrioritiesExpanded
                  ? Icons.keyboard_arrow_up
                  : Icons.keyboard_arrow_right,
              color: AppColors.black,
              size: 20,
            ),
            onTap: () {
              setState(() {
                isPrioritiesExpanded = !isPrioritiesExpanded;
              });
            },
          ),
          if (isPrioritiesExpanded)
            Container(
              padding: const EdgeInsets.all(16.0),
              child: _buildPrioritiesTabContent(context, task),
            ),
        ],
      ),
    );
  }

  Widget _buildOverviewTabContent(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Exciting Task Alert!',
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.black,
                ),
              ),
              const Gap(12),
              Text(
                "The summary of the task brief goes here. \n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl nec ultricies tincidunt, nunc nisl tincidunt nunc, nec tincidunt nisl nisl nec nisl. \n\nNullam euismod, nisl nec ultricies tincidunt, nunc nisl tincidunt nunc, nec tincidunt nisl nisl nec nisl.",
                style: textTheme.bodySmall?.copyWith(
                  color: AppColors.black.withValues(alpha: 0.8),
                  height: 1.4,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPrioritiesTabContent(
      BuildContext context, entities.TaskDetail task) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (task.forms != null && task.forms!.isNotEmpty)
          ...task.forms!.asMap().entries.map((entry) {
            final index = entry.key;
            final form = entry.value;

            return _buildPriorityFormItem(context, index + 1, form, task);
          })
        else
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Text(
              'No priority forms available',
              style: textTheme.bodySmall?.copyWith(
                color: AppColors.black.withValues(alpha: 0.6),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPriorityFormItem(
    BuildContext context,
    int priorityNumber,
    entities.Form form,
    entities.TaskDetail task,
  ) {
    final textTheme = Theme.of(context).textTheme;
    final visibleQuestions = form.questions
        ?.where((q) => q.questionBrief?.isNotEmpty == true)
        .toList();
    return Row(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.black20),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      priorityNumber.toString(),
                      style: textTheme.bodySmall?.copyWith(
                        color: AppColors.primaryBlue,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                const Gap(12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      form.formName ?? 'Unnamed Form',
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.black,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Row(
              children: [
                if (!(form.questions == null || form.questions!.isEmpty))
                  Padding(
                    padding: const EdgeInsets.only(left: 10.0),
                    child: Container(
                      width: 2,
                      height: 60,
                      color: AppColors.black10,
                      margin: const EdgeInsets.only(top: 0),
                    ),
                  ),
                priorityNumber == (task.forms?.length ?? 0)
                    ? const Gap(36)
                    : const Gap(26),
                ...form.questions!.asMap().entries.map(
                  (entry) {
                    if (entry.value.questionBrief?.isNotEmpty == true) {
                      final index = entry.key;
                      final question = entry.value;
                      final isLastQuestion =
                          index == form.questions!.length - 1;
                      return Padding(
                        padding: EdgeInsets.only(
                            top: priorityNumber == (task.forms?.length ?? 0)
                                ? 12.0
                                : 0),
                        child: _buildPriorityQuestionItem(
                            context, form, question, isLastQuestion),
                      );
                    } else {
                      return Container();
                    }
                  },
                ),
                (visibleQuestions == null || visibleQuestions.isEmpty)
                    ? Padding(
                        padding: EdgeInsets.only(
                            top: priorityNumber == (task.forms?.length ?? 0)
                                ? 12.0
                                : 0),
                        child: Text(
                          'N/A',
                          style: textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            color: AppColors.black,
                          ),
                        ),
                      )
                    : Container(),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPriorityQuestionItem(BuildContext context, entities.Form form,
      entities.Question question, bool isLastQuestion) {
    final textTheme = Theme.of(context).textTheme;

    entities.QuestionAnswer? answer;
    if (form.questionAnswers != null) {
      try {
        answer = form.questionAnswers!.firstWhere(
          (qa) => qa.questionId == question.questionId,
        );
      } catch (e) {
        answer = null;
      }
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: isLastQuestion
            ? const BorderRadius.only(
                bottomLeft: Radius.circular(10),
                bottomRight: Radius.circular(10),
              )
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (question.questionBrief?.isNotEmpty == true)
            Padding(
              padding: const EdgeInsets.all(2.0),
              child: SizedBox(
                width: MediaQuery.of(context).size.width - 100,
                child: HtmlTextWidget(
                  text: question.questionBrief ?? 'No question brief',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.black,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDocumentNameItem(
      BuildContext context, entities.Document document) {
    final textTheme = Theme.of(context).textTheme;
    final isExpanded =
        expandedSupportingDocuments.contains(document.documentId);

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          ListTile(
            leading: Container(
              width: 40,
              height: 40,
              alignment: Alignment.centerLeft,
              child: document.documentIconLink != null
                  ? Image.network(
                      document.documentIconLink!,
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                      color: Colors.black,
                    )
                  : Image.asset(
                      AppAssets.taskReport,
                      width: 40,
                      height: 40,
                      scale: 3,
                      color: Colors.black,
                    ),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 2.0),
            title: Text(
              document.documentName ?? 'Unnamed Document',
              style: textTheme.montserratTitleExtraSmall.copyWith(
                color: Colors.black,
              ),
            ),
            trailing: Icon(
              isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
              color: AppColors.black,
              size: 20,
            ),
            onTap: () {
              setState(() {
                if (isExpanded) {
                  expandedSupportingDocuments.remove(document.documentId);
                } else {
                  expandedSupportingDocuments.add(document.documentId);
                }
              });
            },
          ),
          if (isExpanded) ...[
            if (document.files == null || document.files!.isEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'No files available for this document',
                  style: textTheme.bodySmall?.copyWith(
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                ),
              )
            else
              ...document.files!.map((file) {
                return _buildDocumentFileItem(context, file, document);
              }),
            const Gap(12),
          ],
        ],
      ),
    );
  }

  Widget _buildDocumentFileItem(BuildContext context, entities.FileElement file,
      entities.Document document) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: ListTile(
        dense: true,
        leading: Icon(
          _getFileIcon(file.documentFileLink),
          color: AppColors.black,
          size: 20,
        ),
        title: Text(
          (file.documentFileLink ?? 'Unnamed File').trim().split('\n').last,
          style: textTheme.bodySmall?.copyWith(
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        onTap: () {
          if (file.documentFileLink != null &&
              file.documentFileLink!.isNotEmpty) {
            context.router.push(WebBrowserRoute(url: file.documentFileLink!));
          } else {
            SnackBarService.error(
              context: context,
              message: 'File URL not available',
            );
          }
        },
      ),
    );
  }

  IconData _getFileIcon(String? fileUrl) {
    if (fileUrl == null) return Icons.description;
    final url = fileUrl.toLowerCase();
    if (url.contains('.pdf')) {
      return Icons.picture_as_pdf;
    } else if (url.contains('.jpg') ||
        url.contains('.jpeg') ||
        url.contains('.png') ||
        url.contains('.gif')) {
      return Icons.image;
    } else if (url.contains('youtube.com') || url.contains('youtu.be')) {
      return Icons.play_circle;
    } else if (url.contains('.txt') ||
        url.contains('.doc') ||
        url.contains('.docx')) {
      return Icons.description;
    } else {
      return Icons.insert_drive_file;
    }
  }
}
