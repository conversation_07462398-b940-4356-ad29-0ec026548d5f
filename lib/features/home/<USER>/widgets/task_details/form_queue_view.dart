import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:auto_route/auto_route.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_state.dart';

class FormQueueView extends StatefulWidget {
  final entities.TaskDetail task;

  const FormQueueView({super.key, required this.task});

  @override
  State<FormQueueView> createState() => _FormQueueViewState();
}

class _FormQueueViewState extends State<FormQueueView> {
  // Key to force rebuild of ListView when refresh is triggered
  Key _listViewKey = UniqueKey();

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final List progressItems = widget.task.forms ?? [];

    return BlocListener<FormRefreshCubit, FormRefreshState>(
      listener: (context, state) {
        if (state is RefreshForm) {
          // Force rebuild of the ListView by changing its key
          setState(() {
            _listViewKey = UniqueKey();
          });
        }
      },
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16.0, right: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Form queue',
                  style: textTheme.montserratTitleSmall,
                  textAlign: TextAlign.center,
                ),
                const Spacer(),
                const Icon(
                  Icons.check_box_outlined,
                  size: 16,
                ),
                const Gap(4),
                Text('Forms', style: textTheme.montserratTitleExtraSmall),
                const Gap(16),
              ],
            ),
          ),
          const Gap(16),
          widget.task.forms?.isEmpty ?? true
              ? const EmptyState(message: 'No forms available')
              : SizedBox(
                  height: 100,
                  child: ListView.builder(
                    key: _listViewKey,
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    itemCount: progressItems.length,
                    itemBuilder: (context, index) {
                      final form = progressItems[index];
                      return _FormProgressCard(
                        form: form,
                        taskId: widget.task.taskId,
                        textTheme: textTheme,
                        key: ValueKey(
                            '${widget.task.taskId}_${form.formId}_${_listViewKey.toString()}'),
                      );
                    },
                  ),
                ),
        ],
      ),
    );
  }
}

class _FormProgressCard extends StatefulWidget {
  final entities.Form form;
  final num? taskId;
  final TextTheme textTheme;

  const _FormProgressCard({
    required this.form,
    required this.taskId,
    required this.textTheme,
    super.key,
  });

  @override
  State<_FormProgressCard> createState() => _FormProgressCardState();
}

class _FormProgressCardState extends State<_FormProgressCard> {
  FormProgress? _progress;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFormProgress();
  }

  @override
  void didUpdateWidget(_FormProgressCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Reload progress if key properties changed
    if (oldWidget.taskId != widget.taskId ||
        oldWidget.form.formId != widget.form.formId) {
      _loadFormProgress();
    }
  }

  Future<void> _loadFormProgress() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.taskId != null && widget.form.formId != null) {
        // Try to get progress using a simpler approach
        final progress = await _calculateSimpleFormProgress();
        if (mounted) {
          setState(() {
            _progress = progress;
            _isLoading = false;
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _progress = FormProgress();
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      // Fallback to simple progress based on form properties if available
      try {
        final formKTotal = widget.form.kTotal ?? 0;
        final formKCompleted = widget.form.kCompleted ?? 0;
        if (mounted) {
          setState(() {
            _progress = FormProgress(
              totalVisible: formKTotal,
              totalCompleted: formKCompleted,
            );
            _isLoading = false;
          });
        }
      } catch (fallbackError) {
        if (mounted) {
          setState(() {
            _progress = FormProgress();
            _isLoading = false;
          });
        }
      }
    }
  }

  /// Calculate simple form progress by counting questions and their completion
  Future<FormProgress> _calculateSimpleFormProgress() async {
    try {
      // First try using the database kTotal/kCompleted values
      final realm = RealmDatabase.instance.realm;
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel != null) {
        final formModel = taskModel.forms
            .where((form) => form.formId == widget.form.formId?.toInt())
            .firstOrNull;

        if (formModel != null) {
          final kTotal = formModel.kTotal;
          final kCompleted = formModel.kCompleted;

          if (kTotal != null && kCompleted != null) {
            return FormProgress(
              totalVisible: kTotal,
              totalCompleted: kCompleted,
            );
          }
        }
      }

      // Fallback to counting questions in the form
      final questions = widget.form.questions ?? [];
      final totalQuestions = questions.length;

      // For now, we'll return 0 completed since we don't have a simple way to calculate completion
      // This can be enhanced later with specific completion logic
      return FormProgress(
        totalVisible: totalQuestions,
        totalCompleted: 0,
      );
    } catch (e) {
      // Return empty progress on any error
      return FormProgress();
    }
  }

  /// Handle form card tap - navigate to QuestionPage
  void _handleFormTap(BuildContext context) {
    if (widget.form.formId != null) {
      context.router.push(QuestionRoute(
        formId: widget.form.formId!,
        taskId: widget.taskId,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<FormRefreshCubit, FormRefreshState>(
      listener: (context, state) {
        if (state is RefreshForm) {
          // Reload progress when refresh is triggered
          _loadFormProgress();
        }
      },
      child: _buildCardContent(),
    );
  }

  Widget _buildCardContent() {
    final totalVisible = _progress?.totalVisible ?? 0;
    final totalCompleted = _progress?.totalCompleted ?? 0;
    final progressValue =
        totalVisible > 0 ? totalCompleted / totalVisible : 0.0;

    bool isVisionForm = widget.form.isVisionForm ?? false;

    return InkWell(
      onTap: () => _handleFormTap(context),
      borderRadius: BorderRadius.circular(10),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        decoration: BoxDecoration(
          color:
              progressValue == 1 ? AppColors.completedGreenDark : Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              widget.form.formName ?? '',
              style: widget.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            if (!isVisionForm) const Gap(12),
            if (!isVisionForm)
              Row(
                children: [
                  Expanded(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: _isLoading
                          ? Container(
                              height: 8,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: const LinearProgressIndicator(
                                backgroundColor: Colors.transparent,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.primaryBlue),
                              ),
                            )
                          : LinearProgressIndicator(
                              value: progressValue,
                              backgroundColor: Colors.grey.shade200,
                              valueColor: const AlwaysStoppedAnimation<Color>(
                                  AppColors.primaryBlue),
                              minHeight: 8,
                            ),
                    ),
                  ),
                  const Gap(24),
                  Text(
                    _isLoading ? '...' : '$totalCompleted of $totalVisible',
                    style: widget.textTheme.montserratTableSmall.copyWith(
                      color: AppColors.black.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
