import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/utils/date_time_utils.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/notification_card.dart';

class AlertView extends StatefulWidget {
  final entities.TaskDetail task;

  const AlertView({super.key, required this.task});

  @override
  State<AlertView> createState() => _AlertViewState();
}

class _AlertViewState extends State<AlertView> {
  final Map<String, List<entities.Taskalert>> groupedTaskAlerts = {};

  @override
  void initState() {
    super.initState();
    _groupTaskAlertsByDate(widget.task);
  }

  @override
  void didUpdateWidget(covariant AlertView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.task != oldWidget.task) {
      _groupTaskAlertsByDate(widget.task);
    }
  }

  void _groupTaskAlertsByDate(entities.TaskDetail task) {
    groupedTaskAlerts.clear();
    for (var alert in task.taskalerts ?? []) {
      final index = task.taskalerts!.indexOf(alert);
      final daysAgo = index % 3;
      final fakeDate = DateTime.now().subtract(Duration(days: daysAgo));
      final formattedDate = DateFormat('EEE dd MMM').format(fakeDate);

      if (!groupedTaskAlerts.containsKey(formattedDate)) {
        groupedTaskAlerts[formattedDate] = [];
      }
      groupedTaskAlerts[formattedDate]!.add(alert);
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.all(0),
      child: (widget.task.taskalerts == null || widget.task.taskalerts!.isEmpty)
          ? const EmptyState(message: 'No task alerts available')
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...groupedTaskAlerts.entries.map((entry) {
                  final date = entry.key;
                  final alertsForThisDate = entry.value;
                  final isToday =
                      date == DateFormat('EEE dd MMM').format(DateTime.now());

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
                        child: Text(
                          date,
                          style: textTheme.titleSmall?.copyWith(
                            color: isToday
                                ? AppColors.primaryBlue
                                : AppColors.black,
                          ),
                        ),
                      ),
                      ...alertsForThisDate.map((alert) {
                        NotificationType notificationType =
                            NotificationType.message;
                        if (alert.subject
                                    ?.toLowerCase()
                                    .contains('urgent') ==
                                true ||
                            alert.message?.toLowerCase().contains('urgent') ==
                                true ||
                            alert.message?.toLowerCase().contains('asap') ==
                                true) {
                          notificationType = NotificationType.urgent;
                        }

                        final index = alertsForThisDate.indexOf(alert);
                        final hoursAgo = 1 + (index * 2);
                        final fakeDate =
                            DateTime.now().subtract(Duration(hours: hoursAgo));
                        final timeAgo = getTimeAgo(fakeDate);

                        return NotificationCard(
                          type: notificationType,
                          message: alert.message ?? 'No message',
                          company: widget.task.client ?? 'Unknown',
                          task: alert.subject ?? 'No subject',
                          location: widget.task.location ?? 'No location',
                          timeAgo: timeAgo,
                          duration: widget.task.budget != null
                              ? '${widget.task.budget}m'
                              : '0m',
                        );
                      }),
                      if (entry.key != groupedTaskAlerts.keys.last)
                        Padding(
                          padding:
                              const EdgeInsets.only(top: 8.0, bottom: 16.0),
                          child: Divider(
                            height: 1,
                            thickness: 1,
                            color: AppColors.appBarBorderBlack,
                          ),
                        ),
                    ],
                  );
                }),
              ],
            ),
    );
  }
}
