import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/pos_status_utils.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_state.dart';

class PosReceivedView extends StatefulWidget {
  final entities.TaskDetail task;

  const PosReceivedView({super.key, required this.task});

  @override
  State<PosReceivedView> createState() => _PosReceivedViewState();
}

class _PosReceivedViewState extends State<PosReceivedView> {
  PosStatusResult _posStatus = PosStatusResult.empty();

  @override
  void initState() {
    super.initState();
    _loadPosData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh POS data when returning from other pages
    _loadPosData();
  }

  void _loadPosData() {
    setState(() {
      _posStatus = PosStatusUtils.calculatePosStatus(widget.task);
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return BlocListener<FormRefreshCubit, FormRefreshState>(
      listener: (context, state) {
        if (state is RefreshForm) {
          // Refresh POS data when refresh is triggered
          _loadPosData();
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: _posStatus.isCompleted
              ? AppColors.completedGreenDark
              : Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('POS Received', style: textTheme.montserratTitleXxsmall),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  AppAssets.dashboardPos,
                  width: 28,
                ),
                const Gap(8),
                Text(
                  _posStatus.posReceivedCount.toString(),
                  style: textTheme.titleSmall?.copyWith(
                    fontSize: 40,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Center(
                  child: SizedBox(
                    height: 30,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          ' of ${_posStatus.totalPosItems}',
                          style: textTheme.bodySmall?.copyWith(
                            color: AppColors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Text(
              'Delivered',
              style: textTheme.montserratTableSmall.copyWith(
                  color: AppColors.primaryBlue, fontStyle: FontStyle.italic),
            ),
          ],
        ),
      ),
    );
  }
}
