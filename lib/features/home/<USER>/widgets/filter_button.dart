import 'package:flutter/material.dart';

class FilterButton extends StatelessWidget {
  final Widget? icon;

  const FilterButton({super.key, required this.icon});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 48,
      height: 44,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: icon,
    );
  }
}
