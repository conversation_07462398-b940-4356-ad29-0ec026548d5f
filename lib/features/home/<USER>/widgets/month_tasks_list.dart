import 'package:flutter/material.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as schedule;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/reorderable_store_list.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/date_task_header.dart';

class MonthTasksList extends StatelessWidget {
  final List<schedule.TaskDetail> allApiTasks;
  final DateTime currentMonth;
  final bool isCheckboxMode;
  final bool areAllItemsSelected;
  final Function(List<TaskDetail>, List<TaskDetail>) onSelectionChanged;
  final TaskDetail Function(schedule.TaskDetail) convertScheduleToDatum;
  final String Function(List<schedule.TaskDetail>) calculateTotalHours;

  const MonthTasksList({
    super.key,
    required this.allApiTasks,
    required this.currentMonth,
    required this.isCheckboxMode,
    required this.areAllItemsSelected,
    required this.onSelectionChanged,
    required this.convertScheduleToDatum,
    required this.calculateTotalHours,
  });

  @override
  Widget build(BuildContext context) {
    // Get tasks for the entire month
    Map<DateTime, List<schedule.TaskDetail>> tasksByDate = _getTasksForMonth();

    // Check if there are any tasks for the entire month
    bool hasAnyTasks = false;
    tasksByDate.forEach((date, tasks) {
      if (tasks.isNotEmpty) {
        hasAnyTasks = true;
      }
    });

    // If no tasks for the entire month, show a message
    if (!hasAnyTasks) {
      return const EmptyState(message: 'No tasks for this month');
    }

    // Create a list of widgets for each date with its tasks
    List<Widget> dateTaskWidgets = [];

    // Sort the dates
    List<DateTime> sortedDates = tasksByDate.keys.toList()
      ..sort((a, b) => a.compareTo(b));

    // Build widgets for each date
    for (DateTime date in sortedDates) {
      List<schedule.TaskDetail> tasksForDate = tasksByDate[date]!;

      // Add date header
      dateTaskWidgets.add(
        DateTaskHeader(
          date: date,
          tasksForDate: tasksForDate,
          calculateTotalHours: calculateTotalHours,
        ),
      );

      // Add divider
      tasksForDate.isEmpty
          ? dateTaskWidgets.add(const Divider(height: 1))
          : const SizedBox();

      // If there are tasks for this date, show them
      if (tasksForDate.isNotEmpty) {
        // Convert to Datum for compatibility
        List<TaskDetail> convertedTasks =
            tasksForDate.map((task) => convertScheduleToDatum(task)).toList();

        // Add tasks for this date
        dateTaskWidgets.add(
          Container(
            constraints: const BoxConstraints(minHeight: 0),
            child: ReorderableStoreList(
              tasks: convertedTasks,
              isCalendarMode: isCheckboxMode,
              showScheduledDate: false,
              showTickIndicator: true,
              showAllDisclosureIndicator: false,
              permanentlyDisableAllDisclosureIndicator: false,
              isOpenTask: true,
              onSelectionChanged: onSelectionChanged,
              selectAll: areAllItemsSelected,
            ),
          ),
        );

        // Add divider after tasks
        dateTaskWidgets.add(const Divider(height: 1));
      }
    }

    // Return a Column with all date sections
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: dateTaskWidgets,
    );
  }

  // Get tasks for the entire month grouped by date, including days with no tasks
  Map<DateTime, List<schedule.TaskDetail>> _getTasksForMonth() {
    Map<DateTime, List<schedule.TaskDetail>> tasksByDate = {};

    // Get the last day of the month
    final DateTime lastDayOfMonth =
        DateTime(currentMonth.year, currentMonth.month + 1, 0);

    // Initialize the map with all days in the month (empty lists)
    for (int day = 1; day <= lastDayOfMonth.day; day++) {
      final normalizedDate =
          DateTime(currentMonth.year, currentMonth.month, day);
      tasksByDate[normalizedDate] = [];
    }

    // Process all API tasks
    for (var task in allApiTasks) {
      // Check if the task is confirmed and not open
      if (task.taskStatus != "Confirmed" ||
          task.isOpen == true ||
          task.scheduledTimeStamp == null) {
        continue;
      }

      // Check if the task's date is in the current month
      final taskDate = DateTime(task.scheduledTimeStamp!.year,
          task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);

      if (taskDate.year == currentMonth.year &&
          taskDate.month == currentMonth.month) {
        // Create a normalized date (without time) for grouping
        final normalizedDate =
            DateTime(taskDate.year, taskDate.month, taskDate.day);

        // Check if this date is in our map (it should be, but just to be safe)
        if (tasksByDate.containsKey(normalizedDate)) {
          // Add to list
          tasksByDate[normalizedDate]!.add(task);
        }
      }
    }

    // Sort tasks within each date by store name and scheduled time
    tasksByDate.forEach((date, tasks) {
      tasks.sort((a, b) => (a.storeName ?? '').compareTo(b.storeName ?? ''));
      tasks.sort((a, b) => (a.scheduledTimeStamp ?? DateTime.now())
          .compareTo(b.scheduledTimeStamp ?? DateTime.now()));
    });

    return tasksByDate;
  }
}
