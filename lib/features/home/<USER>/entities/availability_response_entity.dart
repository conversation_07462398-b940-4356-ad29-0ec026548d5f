class AvailabilityResponseEntity {
  final List<DayAvailabilityEntity> days;

  AvailabilityResponseEntity({
    required this.days,
  });
}

class DayAvailabilityEntity {
  final int dayNumber;
  final int dayOrder;
  final String dayDescription;
  final List<DaySpanEntity> daySpans;

  DayAvailabilityEntity({
    required this.dayNumber,
    required this.dayOrder,
    required this.dayDescription,
    required this.daySpans,
  });
}

class DaySpanEntity {
  final int dayEntryNumber;
  final String startHour;
  final String endHour;

  DaySpanEntity({
    required this.dayEntryNumber,
    required this.startHour,
    required this.endHour,
  });
}
