import 'dart:convert';

class UploadPhotoRequestEntity {
  String? token;
  int? userId;
  int? taskId;
  int? folderId;
  String? pictureBlob;
  int? photoId;
  String? photoCaption;
  String? deviceuid;
  bool? cannotUploadMandatory;
  int? formId;
  int? questionId;
  int? measurementId;
  int? questionpartId;
  String? questionPartMultiId;
  int? measurementPhototypeId;
  DateTime? photoDate;

  UploadPhotoRequestEntity({
    this.token,
    this.userId,
    this.taskId,
    this.folderId,
    this.pictureBlob,
    this.photoId,
    this.photoCaption,
    this.deviceuid,
    this.cannotUploadMandatory,
    this.formId,
    this.questionId,
    this.measurementId,
    this.questionpartId,
    this.questionPartMultiId,
    this.measurementPhototypeId,
    this.photoDate,
  });

  factory UploadPhotoRequestEntity.fromRawJson(String str) =>
      UploadPhotoRequestEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UploadPhotoRequestEntity.fromJson(Map<String, dynamic> json) =>
      UploadPhotoRequestEntity(
        token: json["token"],
        userId: json["user_id"],
        taskId: json["task_id"],
        folderId: json["folder_id"],
        pictureBlob: json["picture_blob"],
        photoId: json["photo_id"],
        photoCaption: json["photo_caption"],
        deviceuid: json["deviceuid"],
        cannotUploadMandatory: json["cannot_upload_mandatory"],
        formId: json["form_id"],
        questionId: json["question_id"],
        measurementId: json["measurement_id"],
        questionpartId: json["questionpart_id"],
        questionPartMultiId: json["question_part_multi_id"],
        measurementPhototypeId: json["measurement_phototype_id"],
        photoDate: json["photo_date"] == null
            ? null
            : DateTime.parse(json["photo_date"]),
      );

  Map<String, dynamic> toJson() => {
        "token": token,
        "user_id": userId,
        "task_id": taskId,
        "folder_id": folderId,
        "picture_blob": pictureBlob,
        "photo_id": photoId,
        "photo_caption": photoCaption,
        "deviceuid": deviceuid,
        "cannot_upload_mandatory": cannotUploadMandatory,
        "form_id": formId,
        "question_id": questionId,
        "measurement_id": measurementId,
        "questionpart_id": questionpartId,
        "question_part_multi_id": questionPartMultiId,
        "measurement_phototype_id": measurementPhototypeId,
        "photo_date": photoDate?.toIso8601String(),
      };
}
