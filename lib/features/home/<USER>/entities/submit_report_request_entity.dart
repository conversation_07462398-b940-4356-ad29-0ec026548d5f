import 'dart:convert';

import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class SubmitReportRequestEntity {
  int? submissionState;
  String? taskStatus;
  int? startTaskLongitude;
  int? minutes;
  int? taskLatitude;
  List<FollowupTask>? followupTasks;
  String? appversion;
  String? taskId;
  int? claimableKms;
  int? taskLongitude;
  int? budgetCalculated;
  String? token;
  List<dynamic>? resumePauseItems;
  int? timerMinutes;
  String? deviceUid;
  int? pages;
  DateTime? submissionTimeStamp;
  String? userId;
  DateTime? taskCommencementTimeStamp;
  DateTime? taskStoppedTimeStamp;
  int? startTaskLatitude;
  String? comment;
  List<Form>? forms;
  DateTime? scheduledTimeStamp;

  SubmitReportRequestEntity({
    this.submissionState,
    this.taskStatus,
    this.startTaskLongitude,
    this.minutes,
    this.taskLatitude,
    this.followupTasks,
    this.appversion,
    this.taskId,
    this.claimableKms,
    this.taskLongitude,
    this.budgetCalculated,
    this.token,
    this.resumePauseItems,
    this.timerMinutes,
    this.deviceUid,
    this.pages,
    this.submissionTimeStamp,
    this.userId,
    this.taskCommencementTimeStamp,
    this.taskStoppedTimeStamp,
    this.startTaskLatitude,
    this.comment,
    this.forms,
    this.scheduledTimeStamp,
  });

  factory SubmitReportRequestEntity.fromRawJson(String str) =>
      SubmitReportRequestEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SubmitReportRequestEntity.fromJson(Map<String, dynamic> json) =>
      SubmitReportRequestEntity(
        submissionState: json["submission_state"],
        taskStatus: json["task_status"],
        startTaskLongitude: json["start_task_longitude"],
        minutes: json["minutes"],
        taskLatitude: json["task_latitude"],
        followupTasks: json["followup_tasks"] == null
            ? []
            : List<FollowupTask>.from(
                json["followup_tasks"]!.map((x) => FollowupTask.fromJson(x))),
        appversion: json["appversion"],
        taskId: json["task_id"],
        claimableKms: json["claimable_kms"],
        taskLongitude: json["task_longitude"],
        budgetCalculated: json["budget_calculated"],
        token: json["token"],
        resumePauseItems: json["resume_pause_items"] == null
            ? []
            : List<dynamic>.from(json["resume_pause_items"]!.map((x) => x)),
        timerMinutes: json["timer_minutes"],
        deviceUid: json["device_uid"],
        pages: json["pages"],
        submissionTimeStamp: json["submission_time_stamp"] == null
            ? null
            : DateTime.parse(json["submission_time_stamp"]),
        userId: json["user_id"],
        taskCommencementTimeStamp: json["task_commencement_time_stamp"] == null
            ? null
            : DateTime.parse(json["task_commencement_time_stamp"]),
        taskStoppedTimeStamp: json["task_stopped_time_stamp"] == null
            ? null
            : DateTime.parse(json["task_stopped_time_stamp"]),
        startTaskLatitude: json["start_task_latitude"],
        comment: json["comment"],
        forms: json["forms"] == null
            ? []
            : List<Form>.from(json["forms"]!.map((x) => Form.fromJson(x))),
        scheduledTimeStamp: json["scheduled_time_stamp"] == null
            ? null
            : DateTime.parse(json["scheduled_time_stamp"]),
      );

  Map<String, dynamic> toJson() => {
        "submission_state": submissionState,
        "task_status": taskStatus,
        "start_task_longitude": startTaskLongitude,
        "minutes": minutes,
        "task_latitude": taskLatitude,
        "followup_tasks": followupTasks == null
            ? []
            : List<dynamic>.from(followupTasks!.map((x) => x.toJson())),
        "appversion": appversion,
        "task_id": taskId,
        "claimable_kms": claimableKms,
        "task_longitude": taskLongitude,
        "budget_calculated": budgetCalculated,
        "token": token,
        "resume_pause_items": resumePauseItems == null
            ? []
            : List<dynamic>.from(resumePauseItems!.map((x) => x)),
        "timer_minutes": timerMinutes,
        "device_uid": deviceUid,
        "pages": pages,
        "submission_time_stamp": submissionTimeStamp?.toIso8601String(),
        "user_id": userId,
        "task_commencement_time_stamp":
            taskCommencementTimeStamp?.toIso8601String(),
        "task_stopped_time_stamp": taskStoppedTimeStamp?.toIso8601String(),
        "start_task_latitude": startTaskLatitude,
        "comment": comment,
        "forms": forms == null ? [] : List<dynamic>.from(forms!.map((x) => x)),
        "scheduled_time_stamp": scheduledTimeStamp?.toIso8601String(),
      };
}

class FollowupTask {
  int? followupNumber;
  String? scheduleNote;
  int? followupItemId;
  String? taskId;
  int? followupTypeId;
  DateTime? visitDate;
  int? budget;

  FollowupTask({
    this.followupNumber,
    this.scheduleNote,
    this.followupItemId,
    this.taskId,
    this.followupTypeId,
    this.visitDate,
    this.budget,
  });

  factory FollowupTask.fromRawJson(String str) =>
      FollowupTask.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FollowupTask.fromJson(Map<String, dynamic> json) => FollowupTask(
        followupNumber: json["followup_number"],
        scheduleNote: json["schedule_note"],
        followupItemId: json["followup_item_id"],
        taskId: json["task_id"],
        followupTypeId: json["followup_type_id"],
        visitDate: json["visit_date"] == null
            ? null
            : DateTime.parse(json["visit_date"]),
        budget: json["budget"],
      );

  Map<String, dynamic> toJson() => {
        "followup_number": followupNumber,
        "schedule_note": scheduleNote,
        "followup_item_id": followupItemId,
        "task_id": taskId,
        "followup_type_id": followupTypeId,
        "visit_date": visitDate?.toIso8601String(),
        "budget": budget,
      };
}
