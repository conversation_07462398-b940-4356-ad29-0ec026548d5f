import 'dart:convert';

class UploadPhotoResponseEntity {
  Data? data;

  UploadPhotoResponseEntity({
    this.data,
  });

  factory UploadPhotoResponseEntity.fromRawJson(String str) =>
      UploadPhotoResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UploadPhotoResponseEntity.fromJson(Map<String, dynamic> json) =>
      UploadPhotoResponseEntity(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class Data {
  int? taskId;
  int? folderId;
  int? photoId;
  String? photoUrl;
  String? photoCaption;
  bool? cannotUploadMandatory;
  DateTime? modifiedTimeStampPhoto;
  int? formId;
  int? questionId;
  int? measurementId;

  Data({
    this.taskId,
    this.folderId,
    this.photoId,
    this.photoUrl,
    this.photoCaption,
    this.cannotUploadMandatory,
    this.modifiedTimeStampPhoto,
    this.formId,
    this.questionId,
    this.measurementId,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        taskId: json["task_id"],
        folderId: json["folder_id"],
        photoId: json["photo_id"],
        photoUrl: json["photo_url"],
        photoCaption: json["photo_caption"],
        cannotUploadMandatory: json["cannot_upload_mandatory"],
        modifiedTimeStampPhoto: json["modified_time_stamp_photo"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_photo"]),
        formId: json["form_id"],
        questionId: json["question_id"],
        measurementId: json["measurement_id"],
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "folder_id": folderId,
        "photo_id": photoId,
        "photo_url": photoUrl,
        "photo_caption": photoCaption,
        "cannot_upload_mandatory": cannotUploadMandatory,
        "modified_time_stamp_photo": modifiedTimeStampPhoto?.toIso8601String(),
        "form_id": formId,
        "question_id": questionId,
        "measurement_id": measurementId,
      };
}
