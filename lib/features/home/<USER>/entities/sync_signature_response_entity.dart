import 'dart:convert';

class SyncSignatureResponseEntity {
  Data? data;

  SyncSignatureResponseEntity({
    this.data,
  });

  factory SyncSignatureResponseEntity.fromRawJson(String str) =>
      SyncSignatureResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SyncSignatureResponseEntity.fromJson(Map<String, dynamic> json) =>
      SyncSignatureResponseEntity(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class Data {
  int? updatedDeleted;

  Data({
    this.updatedDeleted,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        updatedDeleted: json["updated_deleted"],
      );

  Map<String, dynamic> toJson() => {
        "updated_deleted": updatedDeleted,
      };
}
