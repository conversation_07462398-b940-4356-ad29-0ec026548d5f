class StoreCommentEntity {
  final String? storeCommentId;
  final String? userName;
  final String? userProfileImageUrl;
  final String? comment;
  final String? operatorId;
  final String? dateCreated;

  StoreCommentEntity({
    this.storeCommentId,
    this.userName,
    this.userProfileImageUrl,
    this.comment,
    this.operatorId,
    this.dateCreated,
  });
}

class StoreCommentsResponseEntity {
  final List<StoreCommentEntity> comments;
  final String? message;
  final bool success;

  StoreCommentsResponseEntity({
    required this.comments,
    this.message,
    required this.success,
  });
}

class StoreCommentRequestEntity {
  final String taskId;
  final String storeCommentId; // "0" for new comments, actual ID for edits
  final String token;
  final String comment;
  final String userId;

  StoreCommentRequestEntity({
    required this.taskId,
    required this.storeCommentId,
    required this.token,
    required this.comment,
    required this.userId,
  });
}
