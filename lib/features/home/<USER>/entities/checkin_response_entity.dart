import 'package:equatable/equatable.dart';

class CheckinResponseEntity extends Equatable {
  final bool success;
  final String? message;

  const CheckinResponseEntity({
    required this.success,
    this.message,
  });

  factory CheckinResponseEntity.fromJson(Map<String, dynamic> json) {
    return CheckinResponseEntity(
      success: json['success'] ?? false,
      message: json['message'],
    );
  }

  @override
  List<Object?> get props => [success, message];
}
