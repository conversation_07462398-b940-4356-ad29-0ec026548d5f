class SignatureTypeDeleteListEntity {
  final String? taskId;
  final List<String>? signaturetypeIdsToBeDeleted;

  SignatureTypeDeleteListEntity({
    this.taskId,
    this.signaturetypeIdsToBeDeleted,
  });

  factory SignatureTypeDeleteListEntity.fromJson(Map<String, dynamic> json) {
    return SignatureTypeDeleteListEntity(
      taskId: json['task_id']?.toString(),
      signaturetypeIdsToBeDeleted: json['signaturetype_ids_to_be_deleted'] != null
          ? (json['signaturetype_ids_to_be_deleted'] as List)
              .map((e) => e.toString())
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'task_id': taskId,
      'signaturetype_ids_to_be_deleted': signaturetypeIdsToBeDeleted,
    };
  }
}