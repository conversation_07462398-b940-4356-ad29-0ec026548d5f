import 'dart:convert';

class PosResponseEntity {
  Data? data;

  PosResponseEntity({
    this.data,
  });

  factory PosResponseEntity.fromRawJson(String str) =>
      PosResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PosResponseEntity.fromJson(Map<String, dynamic> json) =>
      PosResponseEntity(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class Data {
  List<Pos>? pos;

  Data({
    this.pos,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        pos: json["pos"] == null
            ? []
            : List<Pos>.from(json["pos"]!.map((x) => Pos.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "pos":
            pos == null ? [] : List<dynamic>.from(pos!.map((x) => x.toJson())),
      };
}

class Pos {
  int? taskId;
  String? storeName;
  String? clientName;
  String? cycle;
  DateTime? rangeStart;
  DateTime? rangeEnd;
  DateTime? scheduledDate;
  String? received;

  Pos({
    this.taskId,
    this.storeName,
    this.clientName,
    this.cycle,
    this.rangeStart,
    this.rangeEnd,
    this.scheduledDate,
    this.received,
  });

  factory Pos.fromRawJson(String str) => Pos.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Pos.fromJson(Map<String, dynamic> json) => Pos(
        taskId: json["task_id"],
        storeName: json["store_name"],
        clientName: json["client_name"],
        cycle: json["cycle"],
        rangeStart: json["range_start"] == null
            ? null
            : DateTime.parse(json["range_start"]),
        rangeEnd: json["range_end"] == null
            ? null
            : DateTime.parse(json["range_end"]),
        scheduledDate: json["scheduled_date"] == null
            ? null
            : DateTime.parse(json["scheduled_date"]),
        received: json["received"],
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "store_name": storeName,
        "client_name": clientName,
        "cycle": cycle,
        "range_start": rangeStart?.toIso8601String(),
        "range_end": rangeEnd?.toIso8601String(),
        "scheduled_date": scheduledDate?.toIso8601String(),
        "received": received,
      };
}
