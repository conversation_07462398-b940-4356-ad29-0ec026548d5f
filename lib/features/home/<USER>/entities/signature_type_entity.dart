class SignatureType {
  final String? signaturetypeId;
  final String? signaturetypeName;
  final bool? mandatory;
  final DateTime? modifiedTimeStampSignaturetype;

  SignatureType({
    this.signaturetypeId,
    this.signaturetypeName,
    this.mandatory,
    this.modifiedTimeStampSignaturetype,
  });

  factory SignatureType.fromJson(Map<String, dynamic> json) {
    return SignatureType(
      signaturetypeId: json['signaturetype_id']?.toString(),
      signaturetypeName: json['signaturetype_name']?.toString(),
      mandatory: json['mandatory'] == true,
      modifiedTimeStampSignaturetype: json['modified_time_stamp_signaturetype'] != null
          ? DateTime.parse(json['modified_time_stamp_signaturetype'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'signaturetype_id': signaturetypeId,
      'signaturetype_name': signaturetypeName,
      'mandatory': mandatory,
      'modified_time_stamp_signaturetype': modifiedTimeStampSignaturetype?.toIso8601String(),
    };
  }
}