class SyncPicInfoRequestEntity {
  int? userId;
  String? token;
  List<Task>? tasks;
  String? deviceuid;

  SyncPicInfoRequestEntity({
    this.userId,
    this.token,
    this.tasks,
    this.deviceuid,
  });

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "token": token,
        "tasks": tasks?.map((x) => x.toJson()).toList(),
        "deviceuid": deviceuid,
      };
}

class Task {
  int? taskId;
  DateTime? modifiedTimeStampPhotos;
  bool? uploadPhotosSuccess;
  List<PhotoFolder>? photoFolder;

  Task({
    this.taskId,
    this.modifiedTimeStampPhotos,
    this.uploadPhotosSuccess,
    this.photoFolder,
  });

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "modified_time_stamp_photos":
            modifiedTimeStampPhotos?.toIso8601String(),
        "upload_photos_success": uploadPhotosSuccess,
        "photo_folder": photoFolder?.map((x) => x.toJson()).toList(),
      };
}

class PhotoFolder {
  int? folderId;
  String? deletePhotosIds;

  PhotoFolder({
    this.folderId,
    this.deletePhotosIds,
  });

  Map<String, dynamic> toJson() => {
        "folder_id": folderId,
        "delete_photos_ids": deletePhotosIds,
      };
}
