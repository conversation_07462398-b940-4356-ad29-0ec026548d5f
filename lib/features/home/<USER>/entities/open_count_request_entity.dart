class OpenCountRequestEntity {
  final double latitude;
  final double longitude;
  final String userId;
  final String token;

  OpenCountRequestEntity({
    required this.latitude,
    required this.longitude,
    required this.userId,
    required this.token,
  });

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'user_id': userId,
      'token': token,
    };
  }
}
