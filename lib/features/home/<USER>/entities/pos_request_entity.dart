import 'dart:convert';

class PosRequestEntity {
  int? taskId;
  String? storeName;
  String? clientName;
  String? cycle;
  DateTime? rangeStart;
  DateTime? rangeEnd;
  DateTime? scheduledDate;
  String? received; // Changed to String to match API spec

  PosRequestEntity({
    this.taskId,
    this.storeName,
    this.clientName,
    this.cycle,
    this.rangeStart,
    this.rangeEnd,
    this.scheduledDate,
    this.received,
  });

  factory PosRequestEntity.fromRawJson(String str) =>
      PosRequestEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PosRequestEntity.fromJson(Map<String, dynamic> json) =>
      PosRequestEntity(
        taskId: json["task_id"],
        storeName: json["store_name"],
        clientName: json["client_name"],
        cycle: json["cycle"],
        rangeStart: json["range_start"] == null
            ? null
            : DateTime.parse(json["range_start"]),
        rangeEnd: json["range_end"] == null
            ? null
            : DateTime.parse(json["range_end"]),
        scheduledDate: json["scheduled_date"] == null
            ? null
            : DateTime.parse(json["scheduled_date"]),
        received: json["received"],
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "store_name": storeName,
        "client_name": clientName,
        "cycle": cycle,
        "range_start": rangeStart?.toIso8601String(),
        "range_end": rangeEnd?.toIso8601String(),
        "scheduled_date": scheduledDate?.toIso8601String(),
        "received": received,
      };
}

// Wrapper entity for the complete API request
class UpdatePosRequestEntity {
  String? token;
  int? userId;
  List<PosRequestEntity>? pos;
  String? deviceuid;

  UpdatePosRequestEntity({
    this.token,
    this.userId,
    this.pos,
    this.deviceuid,
  });

  factory UpdatePosRequestEntity.fromRawJson(String str) =>
      UpdatePosRequestEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UpdatePosRequestEntity.fromJson(Map<String, dynamic> json) =>
      UpdatePosRequestEntity(
        token: json["token"],
        userId: json["user_id"],
        pos: json["pos"] == null
            ? []
            : List<PosRequestEntity>.from(
                json["pos"]!.map((x) => PosRequestEntity.fromJson(x))),
        deviceuid: json["deviceuid"],
      );

  Map<String, dynamic> toJson() => {
        "token": token,
        "user_id": userId,
        "pos":
            pos == null ? [] : List<dynamic>.from(pos!.map((x) => x.toJson())),
        "deviceuid": deviceuid,
      };
}
