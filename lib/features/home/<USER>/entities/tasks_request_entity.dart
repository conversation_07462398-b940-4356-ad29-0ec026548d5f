// features/unschedule/data/models/unschedule_task_request.dart
import 'dart:convert';

import 'package:equatable/equatable.dart';

class PreviousTasksRequestEntity extends Equatable {
  final int userId;
  final String token;
  final String deviceUid;
  final String appversion;
  final int taskId;
  final int specificTaskId;
  final int debug;

  const PreviousTasksRequestEntity({
    required this.userId,
    required this.token,
    required this.deviceUid,
    required this.appversion,
    required this.taskId,
    required this.specificTaskId,
    this.debug = 0,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'token': token,
      'device_uid': deviceUid,
      'appversion': appversion,
      'task_id': taskId,
      'specific_task_id': specificTaskId,
      'debug': debug,
    };
  }

  @override
  List<Object?> get props =>
      [userId, token, deviceUid, appversion, taskId, specificTaskId, debug];
}

class TasksRequestEntity extends Equatable {
  final String deviceUid;
  final String userId;
  final String appversion;
  final List<Task> tasks; // List of task IDs to unschedule
  final String token;

  const TasksRequestEntity({
    required this.deviceUid,
    required this.userId,
    required this.appversion,
    required this.tasks,
    required this.token,
  });

  /// Converts the object to a JSON map for the API request body.
  Map<String, dynamic> toJson() {
    return {
      'device_uid': deviceUid,
      'user_id': userId,
      'appversion': appversion,
      'tasks': tasks, // Dart list automatically becomes JSON array
      'token': token,
    };
  }

  @override
  List<Object?> get props => [deviceUid, userId, appversion, tasks, token];
}

class Task {
  DateTime? modifiedTimeStampPhotos;
  DateTime? modifiedTimeStampPhototypes;
  DateTime? modifiedTimeStampForms;
  DateTime? submissionTimeStamp;
  bool? forceImportFollowupTask;
  List<String>? phototypeIds;
  DateTime? modifiedTimeStampSignaturetypes;
  DateTime? modifiedTimeStampDocuments;
  String? taskId;
  DateTime? modifiedTimeStampTask;
  DateTime? modifiedTimeStampSignatures;
  DateTime? scheduledTimeStamp;

  Task({
    this.modifiedTimeStampPhotos,
    this.modifiedTimeStampPhototypes,
    this.modifiedTimeStampForms,
    this.submissionTimeStamp,
    this.forceImportFollowupTask,
    this.phototypeIds,
    this.modifiedTimeStampSignaturetypes,
    this.modifiedTimeStampDocuments,
    this.taskId,
    this.modifiedTimeStampTask,
    this.modifiedTimeStampSignatures,
    this.scheduledTimeStamp,
  });

  factory Task.fromRawJson(String str) => Task.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Task.fromJson(Map<String, dynamic> json) => Task(
        modifiedTimeStampPhotos: json["modified_time_stamp_photos"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_photos"]),
        modifiedTimeStampPhototypes:
            json["modified_time_stamp_phototypes"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_phototypes"]),
        modifiedTimeStampForms: json["modified_time_stamp_forms"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_forms"]),
        submissionTimeStamp: json["submission_time_stamp"] == null
            ? null
            : DateTime.parse(json["submission_time_stamp"]),
        forceImportFollowupTask: json["force_import_followup_task"],
        phototypeIds: json["phototype_ids"] == null
            ? []
            : List<String>.from(json["phototype_ids"]!.map((x) => x)),
        modifiedTimeStampSignaturetypes:
            json["modified_time_stamp_signaturetypes"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_signaturetypes"]),
        modifiedTimeStampDocuments:
            json["modified_time_stamp_documents"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_documents"]),
        taskId: json["task_id"],
        modifiedTimeStampTask: json["modified_time_stamp_task"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_task"]),
        modifiedTimeStampSignatures:
            json["modified_time_stamp_signatures"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_signatures"]),
        scheduledTimeStamp: json["scheduled_time_stamp"] == null
            ? null
            : DateTime.parse(json["scheduled_time_stamp"]),
      );

  Map<String, dynamic> toJson() => {
        "modified_time_stamp_photos":
            modifiedTimeStampPhotos?.toIso8601String(),
        "modified_time_stamp_phototypes":
            modifiedTimeStampPhototypes?.toIso8601String(),
        "modified_time_stamp_forms": modifiedTimeStampForms?.toIso8601String(),
        "submission_time_stamp": submissionTimeStamp?.toIso8601String(),
        "force_import_followup_task": forceImportFollowupTask,
        "phototype_ids": phototypeIds == null
            ? []
            : List<dynamic>.from(phototypeIds!.map((x) => x)),
        "modified_time_stamp_signaturetypes":
            modifiedTimeStampSignaturetypes?.toIso8601String(),
        "modified_time_stamp_documents":
            modifiedTimeStampDocuments?.toIso8601String(),
        "task_id": taskId,
        "modified_time_stamp_task": modifiedTimeStampTask?.toIso8601String(),
        "modified_time_stamp_signatures":
            modifiedTimeStampSignatures?.toIso8601String(),
        "scheduled_time_stamp": scheduledTimeStamp?.toIso8601String(),
      };
}
