class SyncSignInfoRequestEntity {
  int? userId;
  String? token;
  List<Task>? tasks;
  String? deviceuid;

  SyncSignInfoRequestEntity({
    this.userId,
    this.token,
    this.tasks,
    this.deviceuid,
  });

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "token": token,
        "tasks": tasks?.map((x) => x.toJson()).toList(),
        "deviceuid": deviceuid,
      };
}

class Task {
  int? taskId;
  bool? uploadSignatureSuccess;
  DateTime? modifiedTimeStampSignatures;
  List<SignatureFolder>? signatureFolder;

  Task({
    this.taskId,
    this.uploadSignatureSuccess,
    this.modifiedTimeStampSignatures,
    this.signatureFolder,
  });

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "upload_signature_success": uploadSignatureSuccess,
        "modified_time_stamp_signatures":
            modifiedTimeStampSignatures?.toIso8601String(),
        "signature_folder": signatureFolder?.map((x) => x.toJson()).toList(),
      };
}

class SignatureFolder {
  int? folderId;
  String? deleteSignaturesIds;

  SignatureFolder({
    this.folderId,
    this.deleteSignaturesIds,
  });

  Map<String, dynamic> toJson() => {
        "folder_id": folderId,
        "delete_signatures_ids": deleteSignaturesIds,
      };
}
