class OpenTaskResponseEntity {
  DataModel data;

  OpenTaskResponseEntity({required this.data});

  // Factory method to create an OpenTaskResponseEntity from JSON
  factory OpenTaskResponseEntity.fromJson(Map<String, dynamic> json) {
    return OpenTaskResponseEntity(
      data: DataModel.fromJson(json['data']),
    );
  }

  // Method to convert OpenTaskResponseEntity to JSON
  Map<String, dynamic> toJson() {
    return {
      'data': data.toJson(),
    };
  }
}

class DataModel {
  int countTaken;
  int countAvailable;
  int radiusLimit;
  bool otShow;
  int countAutoschedule;

  DataModel({
    required this.countTaken,
    required this.countAvailable,
    required this.radiusLimit,
    required this.otShow,
    required this.countAutoschedule,
  });

  // Factory method to create a DataModel from JSON
  factory DataModel.fromJson(Map<String, dynamic> json) {
    return DataModel(
      countTaken: json['count_taken'] as int,
      countAvailable: json['count_available'] as int,
      radiusLimit: json['radius_limit'] as int,
      otShow: json['ot_show'] as bool,
      countAutoschedule: json['count_autoschedule'] as int,
    );
  }

  // Method to convert DataModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'count_taken': countTaken,
      'count_available': countAvailable,
      'radius_limit': radiusLimit,
      'ot_show': otShow,
      'count_autoschedule': countAutoschedule,
    };
  }
}
