import 'signature_type_entity.dart';

class SignatureTypeListEntity {
  final String? taskId;
  final List<SignatureType>? signatureTypes;
  final DateTime? modifiedTimeStampSignaturetypes;

  SignatureTypeListEntity({
    this.taskId,
    this.signatureTypes,
    this.modifiedTimeStampSignaturetypes,
  });

  factory SignatureTypeListEntity.fromJson(Map<String, dynamic> json) {
    return SignatureTypeListEntity(
      taskId: json['task_id']?.toString(),
      signatureTypes: json['signature_types'] != null
          ? (json['signature_types'] as List)
              .map((e) => SignatureType.fromJson(e))
              .toList()
          : null,
      modifiedTimeStampSignaturetypes: json['modified_time_stamp_signaturetypes'] != null
          ? DateTime.parse(json['modified_time_stamp_signaturetypes'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'task_id': taskId,
      'signature_types': signatureTypes?.map((e) => e.to<PERSON>son()).toList(),
      'modified_time_stamp_signaturetypes': modifiedTimeStampSignaturetypes?.toIso8601String(),
    };
  }
}