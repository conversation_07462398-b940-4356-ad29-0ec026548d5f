import 'dart:convert';

class SyncPicResponseEntity {
  Data? data;

  SyncPicResponseEntity({
    this.data,
  });

  factory SyncPicResponseEntity.fromRawJson(String str) =>
      SyncPicResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SyncPicResponseEntity.fromJson(Map<String, dynamic> json) =>
      SyncPicResponseEntity(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class Data {
  int? updatedDeleted;

  Data({
    this.updatedDeleted,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        updatedDeleted: json["updated_deleted"],
      );

  Map<String, dynamic> toJson() => {
        "updated_deleted": updatedDeleted,
      };
}
