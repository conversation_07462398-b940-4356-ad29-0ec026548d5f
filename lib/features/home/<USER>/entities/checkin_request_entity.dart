import 'package:equatable/equatable.dart';

class CheckinRequestEntity extends Equatable {
  final String userId;
  final String token;
  final int checkinType; // 0 = Start Day, 1 = End Day
  final int checkinKm; // Odometer reading

  const CheckinRequestEntity({
    required this.userId,
    required this.token,
    required this.checkinType,
    required this.checkinKm,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'token': token,
      'checkin_type': checkinType,
      'checkin_km': checkinKm,
    };
  }

  @override
  List<Object?> get props => [userId, token, checkinType, checkinKm];
}
