import 'package:equatable/equatable.dart';

class VacancyEntity extends Equatable {
  final int id;
  final String jobTitle;
  final String jobLocation;
  final String jobDescription;
  final String? companyName;
  final String? salaryRange;
  final String? employmentType;
  final DateTime? postedDate;
  final bool canApply;
  final bool canRefer;

  const VacancyEntity({
    required this.id,
    required this.jobTitle,
    required this.jobLocation,
    required this.jobDescription,
    this.companyName,
    this.salaryRange,
    this.employmentType,
    this.postedDate,
    this.canApply = true,
    this.canRefer = true,
  });

  @override
  List<Object?> get props => [
        id,
        jobTitle,
        jobLocation,
        jobDescription,
        companyName,
        salaryRange,
        employmentType,
        postedDate,
        canApply,
        canRefer,
      ];
}
