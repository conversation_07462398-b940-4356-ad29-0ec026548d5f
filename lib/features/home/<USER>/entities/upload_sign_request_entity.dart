import 'dart:convert';

class UploadSignRequestEntity {
  String? token;
  int? taskId;
  int? userId;
  int? folderId;
  String? signatureBlob;
  int? signatureId;
  String? signedBy;
  String? deviceuid;
  int? formId;
  int? questionId;
  bool? cannotUploadMandatory;

  UploadSignRequestEntity({
    this.token,
    this.taskId,
    this.userId,
    this.folderId,
    this.signatureBlob,
    this.signatureId,
    this.signedBy,
    this.deviceuid,
    this.formId,
    this.questionId,
    this.cannotUploadMandatory,
  });

  factory UploadSignRequestEntity.fromRawJson(String str) =>
      UploadSignRequestEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UploadSignRequestEntity.fromJson(Map<String, dynamic> json) =>
      UploadSignRequestEntity(
        token: json["token"],
        taskId: json["task_id"],
        userId: json["user_id"],
        folderId: json["folder_id"],
        signatureBlob: json["signature_blob"],
        signatureId: json["signature_id"],
        signedBy: json["signed_by"],
        deviceuid: json["deviceuid"],
        formId: json["form_id"],
        questionId: json["question_id"],
        cannotUploadMandatory: json["cannot_upload_mandatory"],
      );

  Map<String, dynamic> toJson() => {
        "token": token,
        "task_id": taskId,
        "user_id": userId,
        "folder_id": folderId,
        "signature_blob": signatureBlob,
        "signature_id": signatureId,
        "signed_by": signedBy,
        "deviceuid": deviceuid,
        "form_id": formId,
        "question_id": questionId,
        "cannot_upload_mandatory": cannotUploadMandatory,
      };
}
