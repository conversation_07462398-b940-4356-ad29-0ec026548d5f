import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart' hide Form;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/date_time_utils.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/core/utils/task_utils.dart';
import 'package:storetrack_app/di/service_locator.dart';

import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';
import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

import 'package:storetrack_app/features/home/<USER>/blocs/today/today_page_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/today/today_page_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/calendar_bottom_sheet.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/reorderable_store_list.dart';
import 'package:table_calendar/table_calendar.dart';

import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/shared/cubits/sync_cubit.dart';

import '../../../../config/routes/app_router.gr.dart';
import '../../../../core/storage/data_manager.dart';
import '../../../../core/services/location_service.dart';

@RoutePage()
class TodayPage extends StatefulWidget {
  const TodayPage({super.key});

  @override
  State<TodayPage> createState() => _TodayPageState();
}

class _TodayPageState extends State<TodayPage> {
  // Task data
  // This is a test comment to trigger the linter.
  final String actualDeviceUid = "8b7a6774c878a206";
  late String actualUserId;
  final String actualAppVersion = "9.9.9";
  final List<Task> actualTasksToSchedule = [];
  late String actualUserToken;

  List<TaskDetail> scheduledTasks = [];

  // Store all tasks from the API response
  List<TaskDetail> allApiTasks = [];

  // Map to track which dates have tasks
  Map<String, bool> datesWithTasks = {};

  // Selected date - always today for this page
  late DateTime selectedDate;

  // For ReorderableStoreList
  bool _isCheckboxMode = false;
  bool _areAllItemsSelected = false;
  List<TaskDetail> selectedItems = [];
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // For collapsible calendar
  bool _isCalendarExpanded = false;
  late CalendarFormat _calendarFormat;

  // Map to store dates with dollar symbol and budget amount
  Map<String, num> _dollarSymbolDates = {};
  Map<String, num> _valueDates = {};

  // Store calendar response data
  CalendarResponseEntity? _calendarResponse;

  @override
  void initState() {
    super.initState();
    // Initialize selected date to today
    selectedDate = DateTime.now();
    // Initialize calendar format to month
    _calendarFormat = CalendarFormat.month;
    // Initialize dollar symbol dates map
    _dollarSymbolDates = {};
    _valueDates = {};
    logger('TodayPage initialized with date: $selectedDate');
    _initializeData();
  }

  // No need for conversion since we're using the same Datum class now

  Future<void> _initializeData() async {
    _refreshData();
  }

  Future<void> _refreshData() async {
    try {
      // Get user ID and token from DataManager
      actualUserId = await sl<DataManager>().getUserId() ?? "0";
      actualUserToken = await sl<DataManager>().getAuthToken() ?? "0";

      // Fetch tasks and calendar data
      if (mounted) {
        context.read<TodayPageCubit>().getData(
              TasksRequestEntity(
                deviceUid: actualDeviceUid,
                userId: actualUserId,
                appversion: actualAppVersion,
                tasks: const [],
                token: actualUserToken,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize: ${e.toString()}',
        );
      }
    }
  }

  // Helper method to check if a task is scheduled for a specific date
  bool _isTaskScheduledForDate(TaskDetail task, DateTime date) {
    // Check if the task has a scheduled timestamp
    if (task.scheduledTimeStamp == null) return false;

    // Check if the task is confirmed and not open
    if (task.taskStatus != "Confirmed" || task.isOpen == true) return false;

    // Compare only the date part (year, month, day) ignoring time
    final taskDate = task.scheduledTimeStamp!;
    final taskDateOnly = DateTime(taskDate.year, taskDate.month, taskDate.day);
    final selectedDateOnly = DateTime(date.year, date.month, date.day);

    // Check if the dates match
    return taskDateOnly.isAtSameMomentAs(selectedDateOnly);
  }

  // Filter tasks for the selected date without making a new API call
  void _filterTasksForSelectedDate() {
    if (allApiTasks.isEmpty) {
      // If we don't have any data yet, we need to make an API call
      _initializeData();
      return;
    }

    // Filter scheduled tasks for the selected date
    var filteredTasks = allApiTasks.where((task) {
      // Check if the task is scheduled for the selected date
      return _isTaskScheduledForDate(task, selectedDate);
    }).toList()
      ..sort((a, b) => (a.storeName ?? '').compareTo(b.storeName ?? ''))
      ..sort((a, b) => (a.scheduledTimeStamp ?? DateTime.now())
          .compareTo(b.scheduledTimeStamp ?? DateTime.now()));

    // Update the state with filtered tasks
    setState(() {
      scheduledTasks = filteredTasks;
      // Update dollar symbol dates and value dates when date changes
      _processDollarSymbolDates(_calendarResponse);
      _processValueDates(_calendarResponse);
    });
  }

  // Calculate total hours for a list of tasks
  String _calculateTotalHours(List<TaskDetail> tasks) {
    if (tasks.isEmpty) {
      return "0 hrs";
    }

    double totalMinutes = 0;
    for (var task in tasks) {
      if (task.budget != null) {
        totalMinutes += task.budget!.toDouble();
      }
    }

    // Convert minutes to hours with one decimal place
    double hours = totalMinutes / 60;
    return "${hours.toStringAsFixed(1)} hrs";
  }

  // Handler for selection changed in ReorderableStoreList
  void _handleSelectionChanged(
      List<TaskDetail> allItems, List<TaskDetail> selectedTasks) {
    setState(() {
      final allItemIds = allItems.map((t) => t.taskId).toSet();

      selectedItems.removeWhere((task) => allItemIds.contains(task.taskId));
      selectedItems.addAll(selectedTasks);

      List<TaskDetail> tasksForDate = allApiTasks
          .where((task) => _isTaskScheduledForDate(task, selectedDate))
          .toList();

      _areAllItemsSelected = selectedItems.length == tasksForDate.length &&
          tasksForDate.isNotEmpty;
    });
  }

  // Implementation of select all that works directly with the data
  void _selectAllItems() {
    setState(() {
      _areAllItemsSelected = !_areAllItemsSelected;

      if (_areAllItemsSelected) {
        // We're selecting all items - create a fresh list with all tasks for the selected date
        List<TaskDetail> tasksForDate = allApiTasks
            .where((task) => _isTaskScheduledForDate(task, selectedDate))
            .toList();
        selectedItems = List.from(tasksForDate);
      } else {
        // We're deselecting all - clear the list
        selectedItems.clear();
      }
    });
  }

  // Show calendar bottom sheet for rescheduling tasks
  void _showCalendarBottomSheet(BuildContext context) {
    // Store selected items count to avoid async gap issues
    final itemsCount = selectedItems.length;

    // Create a list of dates that have tasks
    List<DateTime> taskDates = [];
    for (var task in allApiTasks) {
      if (task.scheduledTimeStamp != null &&
          task.taskStatus == "Confirmed" &&
          task.isOpen != true) {
        taskDates.add(task.scheduledTimeStamp!);
      }
    }

    // Show calendar bottom sheet
    CalendarBottomSheet.show(
      context: context,
      calendarResponse: _calendarResponse,
      taskDates: taskDates,
    ).then((selectedDate) {
      // Check if the widget is still mounted and a date was selected
      if (selectedDate != null && mounted && selectedItems.isNotEmpty) {
        // Show rescheduling message immediately
        SnackBarService.info(
          context: context,
          message:
              'Rescheduling $itemsCount tasks for ${DateFormat('MMM dd, yyyy').format(selectedDate)}',
        );

        // Perform async operations
        _performRescheduling(selectedDate);
      }
    });
  }

  /// Performs the actual rescheduling operations asynchronously
  Future<void> _performRescheduling(DateTime selectedDate) async {
    try {
      // submission timestamp is current time in timezone "Australia/NSW", locale "en", "AU"
      final submissionTimeStamp = DateTimeUtils.getCurrentTimeInSydney();

      // Update scheduling data for each selected task in the database
      for (var task in selectedItems) {
        await TaskUtils.updateTaskSchedulingData(
          taskId: task.taskId.toString(),
          scheduledTimeStamp: selectedDate,
          submissionTimeStamp: submissionTimeStamp,
          taskStatus: "Confirmed",
          submissionState: 1,
        );
      }

      // Sync the changes to the server
      await sl<SyncService>().sync();

      // Clear selection after rescheduling
      if (mounted) {
        setState(() {
          _isCheckboxMode = false;
          selectedItems.clear();
          _areAllItemsSelected = false;
        });
      }
    } catch (e) {
      logger('Error during rescheduling: $e');
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to reschedule tasks. Please try again.',
        );
      }
    }
  }

  // Open Google Maps with task coordinates
  Future<void> _openRouteMap(TaskDetail task) async {
    try {
      // Get coordinates from task
      double? latitude;
      double? longitude;

      // Prefer taskLatitude/taskLongitude, fallback to latitude/longitude
      if (task.taskLatitude != null && task.taskLongitude != null) {
        latitude = task.taskLatitude!.toDouble();
        longitude = task.taskLongitude!.toDouble();
      } else if (task.latitude != null && task.longitude != null) {
        latitude = task.latitude!.toDouble();
        longitude = task.longitude!.toDouble();
      }

      if (latitude == null || longitude == null) {
        if (mounted) {
          SnackBarService.warning(
            context: context,
            message: 'Location coordinates not available for this task.',
          );
        }
        return;
      }

      // Construct Google Maps URL
      final googleMapsUrl =
          'https://www.google.com/maps?q=$latitude,$longitude';

      // Navigate to web browser with Google Maps URL
      context.router.push(WebBrowserRoute(url: googleMapsUrl));
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error opening map: ${e.toString()}',
        );
      }
    }
  }

  // Open route map using StoreTrack web service
  Future<void> openRouteMap() async {
    try {
      // Get current location
      final locationService = sl<LocationService>();
      final currentLocation = await locationService.getCurrentPosition();

      if (!mounted) return;

      if (currentLocation == null) {
        SnackBarService.warning(
          context: context,
          message:
              'Unable to get current location. Please enable location services.',
        );
        return;
      }

      // Get user ID
      final userID = actualUserId;

      // Construct current location string
      final currentLatLng =
          "${currentLocation.latitude},${currentLocation.longitude}";

      // Construct the StoreTrack route URL
      final routeUrl =
          "https://webservice.storetrack.com.au/standalone/route.aspx?pid=$userID&latlong=$currentLatLng";

      // Launch the URL externally
      // final uri = Uri.parse(routeUrl);
      // if (await canLaunchUrl(uri)) {
      //   await launchUrl(uri, mode: LaunchMode.externalApplication);
      // } else {
      //   if (mounted) {
      //     SnackBarService.error(
      //       context: context,
      //       message: 'Could not open route map. Please try again.',
      //     );
      //   }
      // }
      context.router.push(WebBrowserRoute(url: routeUrl));
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error opening route map: ${e.toString()}',
        );
      }
    }
  }

  // Build calendar content for the app bar bottom
  Widget _buildCalendarContent() {
    var textTheme = Theme.of(context).textTheme;

    // Calculate total hours for the selected date
    List<TaskDetail> tasksForDate = allApiTasks
        .where((task) => _isTaskScheduledForDate(task, selectedDate))
        .toList();

    // Get the total hours string
    String totalHours = _calculateTotalHours(tasksForDate);

    // Only return the calendar header part for the app bar bottom
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left chevron button
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: const Icon(Icons.chevron_left),
              onPressed: () {
                // Navigate to previous day
                DateTime newDate =
                    selectedDate.subtract(const Duration(days: 1));

                setState(() {
                  selectedDate = newDate;
                  _filterTasksForSelectedDate(); // Filter existing data for the new date
                });
              },
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ),

// Center content with date and hours
          GestureDetector(
            onTap: () {
              setState(() {
                _isCalendarExpanded = !_isCalendarExpanded;
              });
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Date in format "Mon 17 Feb"
                Text(DateFormat('EEE d MMM').format(selectedDate),
                    style: textTheme.montserratTitleSmall),

                const SizedBox(width: 8),
                // Hours with clock icon
                Row(
                  children: [
                    Image.asset(
                      AppAssets.dayTimer,
                      scale: 4,
                      color: Colors.black,
                    ),
                    const SizedBox(width: 4),
                    Text(totalHours,
                        style: textTheme.montserratTitleExtraSmall),
                  ],
                ),
                const SizedBox(width: 8),
                // Toggle icon for calendar expansion
                CircleAvatar(
                  backgroundColor: AppColors.primaryBlue,
                  radius: 8,
                  child: Icon(
                    _isCalendarExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          // Right chevron button
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: const Icon(Icons.chevron_right),
              onPressed: () {
                // Navigate to next day
                DateTime newDate = selectedDate.add(const Duration(days: 1));

                setState(() {
                  selectedDate = newDate;
                  _filterTasksForSelectedDate(); // Filter existing data for the new date
                });
              },
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ),
        ],
      ),
    );
  }

  // Process dollar symbol dates from calendar response
  void _processDollarSymbolDates(CalendarResponseEntity? calendarResponse) {
    // Clear existing data
    _dollarSymbolDates = {};

    // Debug log
    logger('Processing dollar symbol dates');
    logger(
        'Calendar response: ${calendarResponse != null ? 'available' : 'null'}');
    if (calendarResponse != null) {
      logger(
          'Calendar data: ${calendarResponse.data != null ? 'available' : 'null'}');
      if (calendarResponse.data != null) {
        logger(
            'Calendar info: ${calendarResponse.data!.calendarInfo != null ? 'available' : 'null'}');
        if (calendarResponse.data!.calendarInfo != null) {
          logger(
              'Calendar info count: ${calendarResponse.data!.calendarInfo!.length}');

          // Log some calendar info entries for debugging
          for (var i = 0;
              i < calendarResponse.data!.calendarInfo!.length && i < 5;
              i++) {
            var info = calendarResponse.data!.calendarInfo![i];
            logger(
                'Calendar info $i: timestamp=${info.timestamp}, dollarSymbol=${info.dollarSymbol}, budgetAmount=${info.budgetAmount}');
          }
        }
      }
    }

    // Process calendar data if available
    bool hasProcessedData = false;
    if (calendarResponse?.data?.calendarInfo != null &&
        calendarResponse!.data!.calendarInfo!.isNotEmpty) {
      for (var info in calendarResponse.data!.calendarInfo!) {
        if (info.timestamp != null && info.dollarSymbol == true) {
          final dateKey = DateFormat('yyyy-MM-dd').format(info.timestamp!);
          // Store budget amount if available, otherwise store 0
          _dollarSymbolDates[dateKey] = info.budgetAmount ?? 0;
          logger(
              'Added dollar symbol for date $dateKey with budget ${info.budgetAmount ?? 0}');
          hasProcessedData = true;
        }
      }

      // Log the final dollar symbol dates map
      logger('Dollar symbol dates map size: ${_dollarSymbolDates.length}');
      _dollarSymbolDates.forEach((key, value) {
        logger('Dollar symbol date: $key, budget: $value');
      });
    }

    // If no calendar data with dollar symbols was found, try processing tasks
    if (!hasProcessedData) {
      logger(
          'No calendar data with dollar symbols found, using fallback method');

      // First try processing tasks
      bool foundTasksWithBudget = false;
      for (var task in allApiTasks) {
        if (task.scheduledTimeStamp != null &&
            task.budget != null &&
            task.budget! > 0) {
          final dateKey =
              DateFormat('yyyy-MM-dd').format(task.scheduledTimeStamp!);
          // Store budget amount
          _dollarSymbolDates[dateKey] = task.budget!;
          logger(
              'Added dollar symbol from task for date $dateKey with budget ${task.budget}');
          foundTasksWithBudget = true;
        }
      }

      // If still no data, add some test data to ensure dollar symbols appear
      if (!foundTasksWithBudget) {
        logger('No tasks with budget found, adding test data');

        // Add dollar symbols for today and a few days around it
        final now = DateTime.now();
        for (int i = -2; i <= 2; i++) {
          final date = now.add(Duration(days: i));
          final dateKey = DateFormat('yyyy-MM-dd').format(date);
          _dollarSymbolDates[dateKey] =
              (i + 3) * 100; // Some random budget amount
          logger(
              'Added test dollar symbol for date $dateKey with budget ${(i + 3) * 100}');
        }
      }
    }
  }

  // Process value dates from calendar response (based on budget_amount)
  void _processValueDates(CalendarResponseEntity? calendarResponse) {
    // Clear existing data
    _valueDates = {};

    // Debug log
    logger('Processing value dates');
    logger(
        'Calendar response: ${calendarResponse != null ? 'available' : 'null'}');

    // Process calendar data if available
    bool hasProcessedData = false;
    if (calendarResponse?.data?.calendarInfo != null &&
        calendarResponse!.data!.calendarInfo!.isNotEmpty) {
      for (var info in calendarResponse.data!.calendarInfo!) {
        if (info.timestamp != null &&
            info.budgetAmount != null &&
            info.budgetAmount! > 0) {
          final dateKey = DateFormat('yyyy-MM-dd').format(info.timestamp!);
          // Store budget amount
          _valueDates[dateKey] = info.budgetAmount!;
          logger(
              'Added value date for $dateKey with budget ${info.budgetAmount}');
          hasProcessedData = true;
        }
      }

      // Log the final value dates map
      logger('Value dates map size: ${_valueDates.length}');
      _valueDates.forEach((key, value) {
        logger('Value date: $key, budget: $value');
      });
    }

    // If no calendar data with budget amounts was found, try processing tasks
    if (!hasProcessedData) {
      logger(
          'No calendar data with budget amounts found, using fallback method');

      // First try processing tasks
      bool foundTasksWithBudget = false;
      for (var task in allApiTasks) {
        if (task.scheduledTimeStamp != null &&
            task.budget != null &&
            task.budget! > 0) {
          final dateKey =
              DateFormat('yyyy-MM-dd').format(task.scheduledTimeStamp!);
          // Store budget amount
          _valueDates[dateKey] = task.budget!;
          logger(
              'Added value date from task for date $dateKey with budget ${task.budget}');
          foundTasksWithBudget = true;
        }
      }

      // If still no data, add some test data to ensure value markers appear
      if (!foundTasksWithBudget) {
        logger('No tasks with budget found, adding test data for value dates');

        // Add value dates for today and a few days around it
        final now = DateTime.now();
        for (int i = -1; i <= 3; i++) {
          final date = now.add(Duration(days: i));
          final dateKey = DateFormat('yyyy-MM-dd').format(date);
          _valueDates[dateKey] = (i + 2) *
              150; // Some random budget amount different from dollar symbols
          logger(
              'Added test value date for date $dateKey with budget ${(i + 2) * 150}');
        }
      }
    }
  }

  // Build the collapsible calendar widget
  Widget _buildCollapsibleCalendar() {
    var textTheme = Theme.of(context).textTheme;

    // Get the valid first and last days
    DateTime firstDay = DateTime.utc(2020, 1, 1);
    DateTime lastDay = DateTime.utc(2030, 12, 31);

    // Process dollar symbol dates and value dates
    _processDollarSymbolDates(_calendarResponse);
    _processValueDates(_calendarResponse);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: _isCalendarExpanded ? null : 0,
      child: _isCalendarExpanded
          ? Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: TableCalendar(
                      firstDay: firstDay,
                      lastDay: lastDay,
                      focusedDay: selectedDate,
                      calendarFormat: _calendarFormat,
                      startingDayOfWeek: StartingDayOfWeek.sunday,
                      availableGestures: AvailableGestures.none,
                      headerStyle: HeaderStyle(
                        titleCentered: true,
                        formatButtonVisible: false,
                        titleTextStyle: textTheme.montserratTitleSmall,
                        leftChevronIcon: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(Icons.chevron_left),
                        ),
                        rightChevronIcon: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(Icons.chevron_right),
                        ),
                        headerPadding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      daysOfWeekStyle: DaysOfWeekStyle(
                        weekdayStyle: textTheme.montserratMetricsAxisRegular,
                        weekendStyle: textTheme.montserratMetricsAxisRegular,
                      ),
                      calendarStyle: CalendarStyle(
                        outsideDaysVisible: false,
                        defaultTextStyle: textTheme.montserratTableSmall,
                        weekendTextStyle: textTheme.montserratTableSmall,
                        outsideTextStyle: textTheme.montserratTableSmall,
                        // We'll use todayBuilder instead, but keep a transparent decoration
                        todayDecoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.transparent,
                        ),
                        todayTextStyle: textTheme.montserratTableSmall.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                        selectedDecoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.black,
                        ),
                        selectedTextStyle:
                            textTheme.montserratTableSmall.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                        disabledTextStyle:
                            textTheme.montserratTableSmall.copyWith(
                          color: Colors.grey.shade400,
                        ),
                        disabledDecoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.grey.shade100,
                        ),
                        cellMargin: const EdgeInsets.all(4),
                      ),
                      selectedDayPredicate: (day) {
                        return isSameDay(selectedDate, day);
                      },
                      onDaySelected: (selectedDay, focusedDay) {
                        setState(() {
                          selectedDate = selectedDay;
                          _filterTasksForSelectedDate(); // Filter existing data for the new date
                        });
                      },
                      onPageChanged: (focusedDay) {
                        // No need to update state here as we're not tracking focusedDay separately
                      },
                      calendarBuilders: CalendarBuilders(
                        // Add todayBuilder to customize the today indicator with a smaller radius
                        todayBuilder: (context, day, focusedDay) {
                          return Center(
                            child: Container(
                              margin: const EdgeInsets.all(4),
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: AppColors.primaryBlue,
                              ),
                              width: 24, // Reduced size for smaller radius
                              height: 24,
                              alignment: Alignment.center,
                              child: Text(
                                day.day.toString(),
                                style: textTheme.montserratTableSmall.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          );
                        },
                        // Add selectedDayBuilder to match the today indicator style but with black color
                        selectedBuilder: (context, day, focusedDay) {
                          return Center(
                            child: Container(
                              margin: const EdgeInsets.all(4),
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: AppColors.black,
                              ),
                              width: 24, // Reduced size for smaller radius
                              height: 24,
                              alignment: Alignment.center,
                              child: Text(
                                day.day.toString(),
                                style: textTheme.montserratTableSmall.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          );
                        },
                        markerBuilder: (context, date, events) {
                          // Format date for lookup
                          final dateKey = DateFormat('yyyy-MM-dd').format(date);

                          // Check if this date has a dollar symbol or value date
                          final bool hasDollarSymbol =
                              _dollarSymbolDates.containsKey(dateKey);
                          final bool hasValueDate =
                              _valueDates.containsKey(dateKey);

                          // Debug log for marker builder
                          if (date.day == 1 || date.day == 15) {
                            // Only log a few dates to avoid flooding
                            logger('Marker builder for date: $dateKey');
                            logger(
                                'Dollar symbol dates contains this date: $hasDollarSymbol');
                            logger(
                                'Value dates contains this date: $hasValueDate');
                            if (hasDollarSymbol) {
                              logger(
                                  'Budget amount for date $dateKey: ${_dollarSymbolDates[dateKey]}');
                            }
                            if (hasValueDate) {
                              logger(
                                  'Value amount for date $dateKey: ${_valueDates[dateKey]}');
                            }
                          }

                          // Show markers if either dollar symbol or value date exists
                          if (hasDollarSymbol || hasValueDate) {
                            List<Widget> markerWidgets = [];

                            // Add dollar symbol marker if exists
                            if (hasDollarSymbol) {
                              final budgetAmount = _dollarSymbolDates[dateKey]!;
                              markerWidgets.add(
                                // Dollar icon in green
                                Text(
                                  '\$',
                                  style:
                                      textTheme.montserratTableSmall.copyWith(
                                    color: AppColors
                                        .loginGreen, // Green color for dollar icon
                                    fontWeight: FontWeight.w600,
                                    fontSize: 10,
                                  ),
                                ),
                              );
                              // Show budget amount if greater than zero
                              if (budgetAmount > 0) {
                                markerWidgets.add(
                                  Text(
                                    budgetAmount.toString(),
                                    style:
                                        textTheme.montserratTableSmall.copyWith(
                                      color: AppColors
                                          .black, // Black color for budget amount
                                      fontWeight: FontWeight.w500,
                                      fontSize: 8,
                                    ),
                                  ),
                                );
                              }
                            }

                            // Add value marker if exists (and not already showing dollar symbol)
                            if (hasValueDate && !hasDollarSymbol) {
                              final valueAmount = _valueDates[dateKey]!;
                              // markerWidgets.add(
                              //   // V icon in blue for value
                              //   Text(
                              //     'V',
                              //     style: textTheme.montserratTableSmall.copyWith(
                              //       color: AppColors.primaryBlue, // Blue color for value icon
                              //       fontWeight: FontWeight.w600,
                              //       fontSize: 10,
                              //     ),
                              //   ),
                              // );
                              // Show value amount if greater than zero
                              if (valueAmount > 0) {
                                markerWidgets.add(
                                  Text(
                                    valueAmount.toStringAsFixed(1),
                                    style:
                                        textTheme.montserratTableSmall.copyWith(
                                      color: AppColors
                                          .primaryBlue, // Blue color for value amount
                                      fontWeight: FontWeight.w500,
                                      fontSize: 8,
                                    ),
                                  ),
                                );
                              }
                            }

                            logger(
                                'Building marker for date $dateKey with ${markerWidgets.length} markers');

                            return Positioned(
                              bottom: 1,
                              right: 1,
                              child: Container(
                                padding: const EdgeInsets.all(1),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: markerWidgets,
                                ),
                              ),
                            );
                          }
                          return null;
                        },
                        defaultBuilder: (context, day, focusedDay) {
                          // Format date for lookup
                          final dateKey = DateFormat('yyyy-MM-dd').format(day);
                          final bool hasDollarSymbol =
                              _dollarSymbolDates.containsKey(dateKey);
                          final bool hasValueDate =
                              _valueDates.containsKey(dateKey);

                          // Check if this date has a task
                          final bool hasTask = allApiTasks.any((task) =>
                              task.scheduledTimeStamp != null &&
                              DateFormat('yyyy-MM-dd')
                                      .format(task.scheduledTimeStamp!) ==
                                  dateKey);

                          // Check if the day is selected
                          final bool isSelected = isSameDay(selectedDate, day);

                          // Check if the day is today
                          final bool isToday = isSameDay(DateTime.now(), day);

                          // Determine text style based on selection, dollar symbol, and value date
                          TextStyle textStyle = textTheme.montserratTableSmall;

                          if (isSelected) {
                            textStyle = textTheme.montserratTableSmall.copyWith(
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            );
                          } else if (isToday) {
                            textStyle = textTheme.montserratTableSmall.copyWith(
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            );
                          } else if (hasDollarSymbol || hasValueDate) {
                            // Apply dim style for dates with dollar symbol or value dates
                            textStyle = textTheme.montserratTableSmall.copyWith(
                              color: Colors.grey.shade400,
                            );
                          }

                          // Determine decoration based on selection and today
                          BoxDecoration decoration;
                          if (isSelected) {
                            decoration = const BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppColors.black,
                            );
                          } else if (isToday) {
                            decoration = const BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppColors.primaryBlue,
                            );
                          } else if (hasTask) {
                            // Grey background for dates with tasks (always show task indicator)
                            decoration = BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.grey.shade200,
                            );
                          } else {
                            // Default decoration (no background)
                            decoration = const BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.transparent,
                            );
                          }

                          return Center(
                            child: Container(
                              decoration: decoration,
                              margin: const EdgeInsets.all(4),
                              padding: const EdgeInsets.all(1),
                              width:
                                  24, // Match the radius of todayBuilder and selectedBuilder
                              height:
                                  24, // Match the radius of todayBuilder and selectedBuilder
                              alignment: Alignment.center,
                              child: Text(
                                day.day.toString(),
                                style: textStyle,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            )
          : const SizedBox.shrink(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: AppColors.lightGrey2,
      appBar: _buildAppBar(context),
      body: BlocListener<SyncCubit, SyncState>(
        listener: (context, state) {
          if (state is SyncSuccess) {
            _refreshData();
          }
        },
        child: ListView(
          children: [
            // Collapsible calendar
            _buildCollapsibleCalendar(),

            // Task list
            BlocConsumer<TodayPageCubit, TodayPageState>(
              listener: (context, state) {
                if (state is TodayPageSuccess) {
                  setState(() {
                    allApiTasks = state.tasksResponse.addTasks ?? [];
                    _calendarResponse = state.calendarResponse;
                    _filterTasksForSelectedDate();
                  });
                } else if (state is TodayPageError) {
                  SnackBarService.error(
                    context: context,
                    message: state.message,
                  );
                }
              },
              builder: (context, state) {
                if (state is TodayPageLoading) {
                  return SizedBox(
                    height: MediaQuery.of(context).size.height * 0.7,
                    width: MediaQuery.of(context).size.width,
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                // Get tasks for the selected date
                List<TaskDetail> tasksForDate = allApiTasks
                    .where(
                        (task) => _isTaskScheduledForDate(task, selectedDate))
                    .toList();

                return _buildSimpleTaskList(tasksForDate);
              },
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  // Task list using ReorderableStoreList
  Widget _buildSimpleTaskList(List<TaskDetail> tasks) {
    if (tasks.isEmpty) {
      return const EmptyState(message: 'No tasks scheduled for today');
    }

    // Group tasks by hour for display
    Map<int, List<TaskDetail>> tasksByHour = {};

    // Group tasks by hour
    for (var task in tasks) {
      if (task.scheduledTimeStamp != null) {
        int hour = task.scheduledTimeStamp!.hour;
        if (!tasksByHour.containsKey(hour)) {
          tasksByHour[hour] = [];
        }
        tasksByHour[hour]!.add(task);
      }
    }

    // Sort hours
    List<int> sortedHours = tasksByHour.keys.toList()..sort();

    // Create a list of widgets for each hour group
    List<Widget> hourGroups = [];

    for (var hour in sortedHours) {
      final tasksForHour = tasksByHour[hour]!;

      // Sort tasks by scheduled time
      tasksForHour.sort((a, b) => (a.scheduledTimeStamp ?? DateTime.now())
          .compareTo(b.scheduledTimeStamp ?? DateTime.now()));

      hourGroups.add(
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tasks for this hour using ReorderableStoreList
            Container(
              constraints: const BoxConstraints(minHeight: 0),
              child: ReorderableStoreList(
                key: GlobalKey(),
                tasks: tasksForHour,
                isCalendarMode: _isCheckboxMode,
                showScheduledDate: false,
                showTickIndicator: true,
                showAllDisclosureIndicator: false,
                permanentlyDisableAllDisclosureIndicator: false,
                isOpenTask: true,
                onSelectionChanged: _handleSelectionChanged,
                selectAll: _areAllItemsSelected,
              ),
            ),
          ],
        ),
      );
    }

    // If no hour groups were created (no tasks with valid scheduled times)
    if (hourGroups.isEmpty && tasks.isNotEmpty) {
      // Display all tasks without hour grouping
      hourGroups.add(
        Container(
          constraints: const BoxConstraints(minHeight: 0),
          child: ReorderableStoreList(
            key: GlobalKey(),
            tasks: tasks,
            isCalendarMode: _isCheckboxMode,
            showScheduledDate: false,
            showTickIndicator: true,
            showAllDisclosureIndicator: false,
            permanentlyDisableAllDisclosureIndicator: false,
            isOpenTask: true,
            onSelectionChanged: _handleSelectionChanged,
            selectAll: _areAllItemsSelected,
          ),
        ),
      );
    }

    // return Column(
    //   // shrinkWrap: true,
    //   // physics: const ClampingScrollPhysics(),
    //   children: hourGroups,
    // );

    return ReorderableStoreList(
      tasks: tasks,
      isCalendarMode: _isCheckboxMode,
      showScheduledDate: false,
      showTickIndicator: true,
      showAllDisclosureIndicator: false,
      permanentlyDisableAllDisclosureIndicator: false,
      isOpenTask: true,
      onSelectionChanged: _handleSelectionChanged,
      selectAll: _areAllItemsSelected,
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    // Implementation of _buildAppBar method
    // This method should return a Widget representing the app bar
    // For now, we'll return a placeholder
    return CustomAppBar(
      title: 'Today',
      actions: [
        GestureDetector(
          child: Image.asset(
            AppAssets.appbarCalendar,
            scale: 4,
            color: !_isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
          ),
          onTap: () {
            setState(() {
              _isCheckboxMode = false;
              // Clear selection when exiting checkbox mode
              selectedItems.clear();
              _areAllItemsSelected = false;
            });
          },
        ),
        const Gap(8),
        GestureDetector(
          child: Image.asset(
            AppAssets.appbarCalendarEdit,
            color: _isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
            scale: 4,
          ),
          onTap: () {
            setState(() {
              _isCheckboxMode = !_isCheckboxMode;
              if (!_isCheckboxMode) {
                // Clear selection when exiting checkbox mode
                selectedItems.clear();
                _areAllItemsSelected = false;
              }
            });
          },
        ),
        const Gap(8),
        GestureDetector(
          child: Image.asset(
            AppAssets.appbarMap,
            scale: 4,
          ),
          onTap: () {
            openRouteMap();
            // context.router.push(const JourneyMapRoute());

            // SnackBarService.info(
            //   context: context,
            //   message: 'Map view not implemented yet',
            // );
          },
        ),
        const Gap(16)
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: _buildCalendarContent(),
      ),
    );
  }

  Widget? _buildFloatingActionButton(BuildContext context) {
    // Implementation of _buildFloatingActionButton method
    // This method should return a Widget representing the floating action button
    // For now, we'll return a placeholder
    return _isCheckboxMode && selectedItems.isNotEmpty
        ? Container(
            height: 56,
            decoration: BoxDecoration(
              color: AppColors.midGrey,
              borderRadius: BorderRadius.circular(10),
            ),
            padding: const EdgeInsets.all(8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isCheckboxMode = false;
                      selectedItems.clear();
                      _areAllItemsSelected = false;
                    });
                  },
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.close_rounded,
                      color: AppColors.black,
                    ),
                  ),
                ),
                const Gap(8),
                SizedBox(
                  width: 96,
                  child: AppButton(
                    text: _areAllItemsSelected ? "Deselect all" : "Select all",
                    color: Colors.white,
                    textColor: AppColors.black,
                    onPressed: _selectAllItems,
                    height: 40,
                  ),
                ),
                const Gap(8),
                SizedBox(
                  width: 96,
                  child: AppButton(
                    text: "Reschedule",
                    color: AppColors.primaryBlue,
                    onPressed: () => _showCalendarBottomSheet(context),
                    height: 40,
                  ),
                ),
              ],
            ),
          )
        : null;
  }
}
