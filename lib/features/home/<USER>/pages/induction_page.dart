import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/di/service_locator.dart';
import '../blocs/induction/induction_cubit.dart';
import '../../data/models/induction_response.dart';

@RoutePage()
class InductionPage extends StatefulWidget {
  const InductionPage({super.key});

  @override
  State<InductionPage> createState() => _InductionPageState();
}

class _InductionPageState extends State<InductionPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late TabController _tabController;
  String _selectedButton = 'retailer'; // 'retailer' or 'crossmark'

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _tabController = TabController(length: 2, vsync: this);
    _animationController.forward();
  }

  Future<void> _refreshInductionData() async {
    try {
      if (mounted) {
        await context.read<InductionCubit>().fetchInduction();
      }
      return;
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to refresh induction: ${e.toString()}',
        );
      }
      rethrow;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => sl<InductionCubit>()..fetchInduction(),
      child: BlocConsumer<InductionCubit, InductionState>(
        listener: (context, state) {
          if (state is InductionError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          } else if (state is InductionSaved) {
            SnackBarService.success(
              context: context,
              message: 'Induction changes saved successfully',
            );
          }
        },
        builder: (context, state) {
          return Scaffold(
            backgroundColor: AppColors.lightGrey2.withValues(alpha: 0.9),
            appBar: CustomAppBar(
              title: 'Inductions',
              onBackPressed: () => context.router.back(),
              actions: state is InductionLoaded
                  ? [
                      IconButton(
                        onPressed: state is InductionSaving
                            ? null
                            : () {
                                context
                                    .read<InductionCubit>()
                                    .saveInductionChanges();
                              },
                        icon: state is InductionSaving
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : const Icon(Icons.check),
                      ),
                    ]
                  : null,
            ),
            body: FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                children: [
                  // Toggle Buttons
                  Container(
                    color: Colors.white,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Container(
                      height: 40,
                      decoration: BoxDecoration(
                        color: const Color(0xFFF5F5F5),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color(0xFFE0E0E0),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          // Retailer button
                          _buildToggleButton(
                            context,
                            'Retailer',
                            'retailer',
                            const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              bottomLeft: Radius.circular(8),
                            ),
                          ),
                          // Crossmark button
                          _buildToggleButton(
                            context,
                            'Crossmark',
                            'crossmark',
                            const BorderRadius.only(
                              topRight: Radius.circular(8),
                              bottomRight: Radius.circular(8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Content View
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _refreshInductionData,
                      color: AppColors.primaryBlue,
                      backgroundColor: Colors.white,
                      child: _buildSelectedTabContent(state),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTabContent(InductionState state, String type) {
    if (state is InductionLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryBlue,
        ),
      );
    } else if (state is InductionError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.blackTint1,
              ),
              const SizedBox(height: 16),
              Text(
                'Error',
                style: Theme.of(context).textTheme.montserratTitleSmall,
              ),
              const SizedBox(height: 8),
              Text(
                state.message,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.montserratParagraphSmall,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _refreshInductionData,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    } else if (state is InductionLoaded) {
      final allInductions = state.response.data?.inductions ?? [];
      final filteredInductions = _filterInductionsByType(allInductions, type);
      return _buildInductionList(filteredInductions, type);
    }

    return const Center(
      child: CircularProgressIndicator(
        color: AppColors.primaryBlue,
      ),
    );
  }

  List<InductionItem> _filterInductionsByType(
      List<InductionItem> inductions, String type) {
    return inductions.where((induction) {
      final category = induction.inductionCategory?.toLowerCase() ?? '';
      return category.contains(type.toLowerCase());
    }).toList();
  }

  Widget _buildInductionList(List<InductionItem> inductions, String type) {
    if (inductions.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.school_outlined,
                size: 64,
                color: AppColors.blackTint1,
              ),
              const SizedBox(height: 16),
              Text(
                'No ${type.capitalize()} Inductions',
                style: Theme.of(context).textTheme.montserratTitleSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'There are currently no ${type.toLowerCase()} inductions available.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.montserratParagraphSmall,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: inductions.length,
      itemBuilder: (context, index) {
        final induction = inductions[index];
        return _buildInductionItem(induction);
      },
    );
  }

  Widget _buildInductionItem(InductionItem induction) {
    return BlocBuilder<InductionCubit, InductionState>(
      builder: (context, state) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListTile(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            title: Text(
              induction.inductionName ?? 'Untitled Induction',
              style: Theme.of(context).textTheme.montserratSemiBold.copyWith(
                    fontSize: 16,
                    color: AppColors.black,
                  ),
            ),
            subtitle: induction.inductionCategory != null &&
                    induction.inductionCategory!.isNotEmpty
                ? Padding(
                    padding: const EdgeInsets.only(top: 5),
                    child: Text(
                      induction.inductionCategory!,
                      style: Theme.of(context)
                          .textTheme
                          .montserratParagraphSmall
                          .copyWith(
                            fontSize: 14,
                            color: AppColors.blackTint1,
                          ),
                    ),
                  )
                : null,
            trailing: Icon(
              Icons.check_circle,
              color: induction.has == true
                  ? AppColors.primaryBlue
                  : AppColors.black20,
            ),
            onTap: () {
              if (induction.inductionId != null) {
                context
                    .read<InductionCubit>()
                    .toggleInductionSelection(induction.inductionId!);
              }
            },
          ),
        );
      },
    );
  }

  Widget _buildToggleButton(
    BuildContext context,
    String text,
    String buttonKey,
    BorderRadius borderRadius,
  ) {
    final bool isSelected = _selectedButton == buttonKey;

    return Expanded(
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedButton = buttonKey;
            // Update TabController to keep it in sync
            if (buttonKey == 'retailer') {
              _tabController.animateTo(0);
            } else {
              _tabController.animateTo(1);
            }
          });
        },
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor:
              isSelected ? AppColors.primaryBlue : const Color(0xFFF5F5F5),
          foregroundColor: isSelected ? Colors.white : AppColors.blackTint1,
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
            side: const BorderSide(
              color: Colors.grey,
              width: 0,
            ),
          ),
          padding: EdgeInsets.zero,
        ),
        child: Text(
          text,
          style: Theme.of(context)
              .textTheme
              .montserratNavigationPrimaryMedium
              .copyWith(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
        ),
      ),
    );
  }

  Widget _buildSelectedTabContent(InductionState state) {
    String type = _selectedButton == 'retailer' ? 'retailers' : 'crossmark';
    return _buildTabContent(state, type);
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
