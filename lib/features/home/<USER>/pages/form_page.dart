// import 'package:auto_route/auto_route.dart';
// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:storetrack_app/config/routes/app_router.gr.dart';
// import 'package:storetrack_app/config/themes/app_colors.dart';
// import 'package:storetrack_app/core/utils/snackbar_service.dart';
// import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
//     as entities;
// import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
// import 'package:storetrack_app/features/home/<USER>/widgets/form_card.dart';
// import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_cubit.dart';
// import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_state.dart';
// import 'package:storetrack_app/features/home/<USER>/blocs/form_page/form_page_cubit.dart';
// import 'package:storetrack_app/features/home/<USER>/blocs/form_page/form_page_state.dart';
// import 'package:storetrack_app/di/service_locator.dart';

// /// A page that displays a list of forms for a specific task
// ///
// /// This page provides:
// /// - Dynamic progress tracking for each form
// /// - Navigation to QuestionPage or WebBrowser for vision forms
// /// - Real-time progress updates after returning from form pages
// /// - Comprehensive error handling and retry functionality
// /// - Proper state management with caching for performance
// @RoutePage()
// class FormPage extends StatefulWidget {
//   final int taskId;

//   const FormPage({
//     super.key,
//     required this.taskId,
//   });

//   @override
//   State<FormPage> createState() => _FormPageState();
// }

// class _FormPageState extends State<FormPage> {
//   @override
//   Widget build(BuildContext context) {
//     // Provide the FormPageCubit here since the router creates the page directly
//     return BlocProvider<FormPageCubit>(
//       create: (context) {
//         final cubit = sl<FormPageCubit>();
//         // Clear any cached progress data to ensure fresh data
//         cubit.clearProgressCache();
//         cubit.loadTask(widget.taskId);
//         return cubit;
//       },
//       child: _FormPageContent(taskId: widget.taskId),
//     );
//   }
// }

// class _FormPageContent extends StatefulWidget {
//   final int taskId;

//   const _FormPageContent({
//     required this.taskId,
//   });

//   @override
//   State<_FormPageContent> createState() => _FormPageContentState();
// }

// class _FormPageContentState extends State<_FormPageContent>
//     with WidgetsBindingObserver {
//   bool _hasInitialized = false;

//   @override
//   void initState() {
//     super.initState();
//     WidgetsBinding.instance.addObserver(this);
//     _hasInitialized = true;
//   }

//   @override
//   void dispose() {
//     WidgetsBinding.instance.removeObserver(this);
//     super.dispose();
//   }

//   @override
//   void didChangeAppLifecycleState(AppLifecycleState state) {
//     super.didChangeAppLifecycleState(state);
//     // Refresh progress when app becomes active (user returns to app)
//     if (state == AppLifecycleState.resumed && mounted) {
//       context.read<FormPageCubit>().refreshAllProgress(widget.taskId);
//     }
//   }

//   @override
//   void didChangeDependencies() {
//     super.didChangeDependencies();
//     // Only force refresh if we've already initialized (i.e., returning from another page)
//     if (_hasInitialized) {
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         if (mounted) {
//           context.read<FormPageCubit>().forceRefresh(widget.taskId);
//         }
//       });
//     }
//   }

//   /// Handle form navigation and refresh progress after returning
//   ///
//   /// Supports both regular forms (QuestionPage) and vision forms (WebBrowser)
//   /// Automatically refreshes progress data when returning from any form
//   Future<void> _navigateToQuestion(entities.Form form) async {
//     // Validate form data before navigation
//     if (form.formId == null) {
//       if (mounted) {
//         SnackBarService.error(
//           context: context,
//           message: 'Invalid form data. Cannot open form.',
//         );
//       }
//       return;
//     }

//     try {
//       if (form.isVisionForm == true) {
//         final visionUrl = form.visionFormUrl;
//         if (visionUrl == null || visionUrl.isEmpty) {
//           if (mounted) {
//             SnackBarService.error(
//               context: context,
//               message: 'Vision form URL not available.',
//             );
//           }
//           return;
//         }

//         await context.router.push(WebBrowserRoute(url: visionUrl));
//       } else {
//         // Navigate to QuestionPage
//         await context.router.push(
//           QuestionRoute(
//             formId: form.formId!,
//             taskId: widget.taskId,
//           ),
//         );
//       }

//       // Refresh progress after returning from QuestionPage or WebBrowser
//       if (mounted) {
//         // Add a small delay to ensure any database updates from the form page are processed
//         await Future.delayed(const Duration(milliseconds: 200));
//         context.read<FormPageCubit>().refreshAllProgress(widget.taskId);
//       }
//     } catch (e) {
//       if (mounted) {
//         SnackBarService.error(
//           context: context,
//           message: 'Failed to open form: ${e.toString()}',
//         );
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     return BlocListener<FormRefreshCubit, FormRefreshState>(
//       listener: (context, refreshState) {
//         if (refreshState is RefreshForm) {
//           // Refresh progress data when FormRefreshCubit emits refresh event
//           // Add a small delay to ensure any database updates are processed first
//           WidgetsBinding.instance.addPostFrameCallback((_) async {
//             await Future.delayed(const Duration(milliseconds: 100));
//             if (mounted) {
//               context.read<FormPageCubit>().refreshAllProgress(widget.taskId);
//             }
//           });
//         }
//       },
//       child: PopScope(
//         onPopInvokedWithResult: (didPop, result) {
//           if (didPop) {
//             // Trigger refresh for form-related widgets when user navigates back
//             // Add a small delay to ensure any database updates are processed first
//             WidgetsBinding.instance.addPostFrameCallback((_) async {
//               await Future.delayed(const Duration(milliseconds: 100));
//               if (mounted) {
//                 context.read<FormRefreshCubit>().refresh();
//               }
//             });
//           }
//         },
//         child: Scaffold(
//           backgroundColor: AppColors.lightGrey2,
//           appBar: const CustomAppBar(
//             title: 'Forms',
//           ),
//           body: BlocBuilder<FormPageCubit, FormPageState>(
//             builder: (context, state) {
//               return _buildBody(context, textTheme, state);
//             },
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildBody(
//       BuildContext context, TextTheme textTheme, FormPageState state) {
//     if (state is FormPageLoading) {
//       return const Center(
//         child: CircularProgressIndicator(
//           color: AppColors.primaryBlue,
//         ),
//       );
//     }

//     if (state is FormPageError) {
//       return Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(
//               Icons.error_outline,
//               size: 64,
//               color: Colors.red.shade400,
//             ),
//             const Gap(16),
//             Text(
//               'Error loading forms',
//               style: textTheme.titleMedium,
//             ),
//             const Gap(8),
//             Text(
//               state.message,
//               style: textTheme.bodyMedium,
//               textAlign: TextAlign.center,
//             ),
//             const Gap(16),
//             ElevatedButton(
//               onPressed: () =>
//                   context.read<FormPageCubit>().loadTask(widget.taskId),
//               child: const Text('Retry'),
//             ),
//           ],
//         ),
//       );
//     }

//     // Handle loaded states (both basic loaded and progress loaded)
//     entities.TaskDetail? task;
//     Map<int, Map<String, dynamic>>? progressData;

//     if (state is FormPageLoaded) {
//       task = state.task;
//     } else if (state is FormPageProgressLoading) {
//       task = state.task;
//     } else if (state is FormPageProgressLoaded) {
//       task = state.task;
//       progressData = state.progressData;
//     }

//     if (task == null) {
//       return const Center(
//         child: EmptyState(message: 'No task data available'),
//       );
//     }

//     final formItems = task.forms;

//     return SingleChildScrollView(
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           const Gap(8),
//           formItems == null || formItems.isEmpty
//               ? const Center(
//                   child: EmptyState(
//                     message: 'No forms available for this task',
//                   ),
//                 )
//               : _buildFormsList(formItems, state, progressData),
//           const Gap(8),
//         ],
//       ),
//     );
//   }

//   /// Build the forms list widget
//   Widget _buildFormsList(
//     List<entities.Form> formItems,
//     FormPageState state,
//     Map<int, Map<String, dynamic>>? progressData,
//   ) {
//     return ListView.separated(
//       shrinkWrap: true,
//       physics: const NeverScrollableScrollPhysics(),
//       padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
//       itemCount: formItems.length,
//       itemBuilder: (context, index) {
//         final form = formItems[index];
//         return _buildFormItem(form, state, progressData);
//       },
//       separatorBuilder: (BuildContext context, int index) {
//         return const Gap(8);
//       },
//     );
//   }

//   /// Build individual form item widget
//   Widget _buildFormItem(
//     entities.Form form,
//     FormPageState state,
//     Map<int, Map<String, dynamic>>? progressData,
//   ) {
//     final formId = form.formId?.toInt();
//     final isMandatory = form.isMandatory ?? false;

//     // Get cached progress data or show loading/default values
//     Map<String, dynamic>? cachedProgress;
//     if (formId != null && progressData != null) {
//       cachedProgress = progressData[formId];
//     }

//     double progress = 0.0;
//     String progressText = 'Loading...';

//     if (cachedProgress != null) {
//       progress = cachedProgress['progress'] as double;
//       progressText = cachedProgress['progressText'] as String;
//     } else if (state is FormPageProgressLoading) {
//       progressText = 'Updating...';
//     }

//     return GestureDetector(
//       onTap: () => _navigateToQuestion(form),
//       child: FormCard(
//         title: form.formName ?? 'Unnamed Form',
//         progress: progress,
//         progressText: progressText,
//         width: 1.0, // Full width for list view
//         isMandatory: isMandatory,
//         isVisionForm: form.isVisionForm ?? false,
//       ),
//     );
//   }
// }

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/form_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_page/form_page_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_page/form_page_state.dart';
import 'package:storetrack_app/di/service_locator.dart';

/// A page that displays a list of forms for a specific task
///
/// This page provides:
/// - Dynamic progress tracking for each form
/// - Navigation to QuestionPage or WebBrowser for vision forms
/// - Real-time progress updates after returning from form pages
/// - Comprehensive error handling and retry functionality
/// - Proper state management with caching for performance
@RoutePage()
class FormPage extends StatefulWidget {
  final int taskId;

  const FormPage({
    super.key,
    required this.taskId,
  });

  @override
  State<FormPage> createState() => _FormPageState();
}

class _FormPageState extends State<FormPage> {
  @override
  Widget build(BuildContext context) {
    // Provide the FormPageCubit here.
    // The actual data loading is moved to `_FormPageContent.initState`
    // to prevent blocking the initial build of this page.
    return BlocProvider<FormPageCubit>(
      create: (context) => sl<FormPageCubit>(),
      child: _FormPageContent(taskId: widget.taskId),
    );
  }
}

class _FormPageContent extends StatefulWidget {
  final int taskId;

  const _FormPageContent({
    required this.taskId,
  });

  @override
  State<_FormPageContent> createState() => _FormPageContentState();
}

class _FormPageContentState extends State<_FormPageContent>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // FIX: Load data after the first frame to prevent jank on page open.
    // By using `addPostFrameCallback`, we ensure the initial page transition
    // is smooth (showing a loading indicator), and data fetching starts immediately after.
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final cubit = context.read<FormPageCubit>();
        // Clear any cached progress data to ensure fresh data.
        cubit.clearProgressCache();
        cubit.loadTask(widget.taskId);
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // Refresh progress when app becomes active (user returns to app)
    if (state == AppLifecycleState.resumed && mounted) {
      context.read<FormPageCubit>().refreshAllProgress(widget.taskId);
    }
  }

  // FIX: Removed `didChangeDependencies`. Its logic was complex and a likely
  // source of jank from unnecessary rebuilds when popping back to this page.
  // The refresh-on-return is now handled cleanly by the `await` in `_navigateToQuestion`.

  /// Handle form navigation and refresh progress after returning
  ///
  /// Supports both regular forms (QuestionPage) and vision forms (WebBrowser)
  /// Automatically refreshes progress data when returning from any form
  Future<void> _navigateToQuestion(entities.Form form) async {
    if (form.formId == null) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Invalid form data. Cannot open form.',
        );
      }
      return;
    }

    try {
      if (form.isVisionForm == true) {
        final visionUrl = form.visionFormUrl;
        if (visionUrl == null || visionUrl.isEmpty) {
          if (mounted) {
            SnackBarService.error(
              context: context,
              message: 'Vision form URL not available.',
            );
          }
          return;
        }

        await context.router.push(WebBrowserRoute(url: visionUrl));
      } else {
        await context.router.push(
          QuestionRoute(
            formId: form.formId!,
            taskId: widget.taskId,
          ),
        );
      }

      // FIX: Refresh progress immediately after returning. The `Future.delayed`
      // was removed as it added unnecessary latency. Awaiting the push operation
      // is sufficient to ensure this code runs at the correct time.
      if (mounted) {
        context.read<FormPageCubit>().refreshAllProgress(widget.taskId);
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to open form: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    // FIX: The FormRefreshCubit instance is captured here to be used safely
    // in the PopScope callback, avoiding context issues after the widget is popped.
    final formRefreshCubit = context.read<FormRefreshCubit>();

    // FIX: Removed the BlocListener<FormRefreshCubit, ...>. This page should
    // manage its own state updates (on-load, on-return) rather than listening for
    // a broad refresh event, which can cause redundant and janky rebuilds.
    return PopScope(
      onPopInvoked: (didPop) {
        if (didPop) {
          // FIX: Notify the previous page to refresh after this page has been popped.
          // Using `addPostFrameCallback` ensures the pop animation completes smoothly
          // before the previous page's state is updated, preventing jank.
          // The previous implementation was likely broken due to an incorrect `mounted` check.
          WidgetsBinding.instance.addPostFrameCallback((_) {
            formRefreshCubit.refresh();
          });
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.lightGrey2,
        appBar: const CustomAppBar(
          title: 'Forms',
        ),
        body: BlocBuilder<FormPageCubit, FormPageState>(
          builder: (context, state) {
            return _buildBody(context, textTheme, state);
          },
        ),
      ),
    );
  }

  Widget _buildBody(
      BuildContext context, TextTheme textTheme, FormPageState state) {
    if (state is FormPageLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryBlue,
        ),
      );
    }

    if (state is FormPageError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const Gap(16),
            Text(
              'Error loading forms',
              style: textTheme.titleMedium,
            ),
            const Gap(8),
            Text(
              state.message,
              style: textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const Gap(16),
            ElevatedButton(
              onPressed: () =>
                  context.read<FormPageCubit>().loadTask(widget.taskId),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    entities.TaskDetail? task;
    Map<int, Map<String, dynamic>>? progressData;

    if (state is FormPageLoaded) {
      task = state.task;
    } else if (state is FormPageProgressLoading) {
      task = state.task;
    } else if (state is FormPageProgressLoaded) {
      task = state.task;
      progressData = state.progressData;
    }

    if (task == null) {
      return const Center(
        child: EmptyState(message: 'No task data available'),
      );
    }

    final formItems = task.forms;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Gap(8),
          formItems == null || formItems.isEmpty
              ? const Center(
                  child: EmptyState(
                    message: 'No forms available for this task',
                  ),
                )
              : _buildFormsList(formItems, state, progressData),
          const Gap(8),
        ],
      ),
    );
  }

  Widget _buildFormsList(
    List<entities.Form> formItems,
    FormPageState state,
    Map<int, Map<String, dynamic>>? progressData,
  ) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      itemCount: formItems.length,
      itemBuilder: (context, index) {
        final form = formItems[index];
        return _buildFormItem(form, state, progressData);
      },
      separatorBuilder: (BuildContext context, int index) {
        return const Gap(8);
      },
    );
  }

  Widget _buildFormItem(
    entities.Form form,
    FormPageState state,
    Map<int, Map<String, dynamic>>? progressData,
  ) {
    final formId = form.formId?.toInt();
    final isMandatory = form.isMandatory ?? false;

    Map<String, dynamic>? cachedProgress;
    if (formId != null && progressData != null) {
      cachedProgress = progressData[formId];
    }

    double progress = 0.0;
    String progressText = 'Loading...';

    if (cachedProgress != null) {
      progress = cachedProgress['progress'] as double;
      progressText = cachedProgress['progressText'] as String;
    } else if (state is FormPageProgressLoading) {
      progressText = 'Updating...';
    }

    return GestureDetector(
      onTap: () => _navigateToQuestion(form),
      child: FormCard(
        title: form.formName ?? 'Unnamed Form',
        progress: progress,
        progressText: progressText,
        width: 1.0,
        isMandatory: isMandatory,
        isVisionForm: form.isVisionForm ?? false,
      ),
    );
  }
}
