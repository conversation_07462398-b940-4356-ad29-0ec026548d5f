import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:url_launcher/url_launcher.dart';
import '../blocs/useful_links/useful_links_cubit.dart';
import '../../data/models/useful_links_response.dart';

@RoutePage()
class UsefulLinksPage extends StatefulWidget {
  const UsefulLinksPage({super.key});

  @override
  State<UsefulLinksPage> createState() => _UsefulLinksPageState();
}

class _UsefulLinksPageState extends State<UsefulLinksPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _loadUsefulLinks();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _animationController.forward();
  }

  Future<void> _loadUsefulLinks() async {
    try {
      final dataManager = sl<DataManager>();
      final userId = await dataManager.getUserId() ?? '0';
      final authToken = await dataManager.getAuthToken() ?? '0';

      if (mounted) {
        context.read<UsefulLinksCubit>().fetchUsefulLinks(
              token: authToken,
              userId: userId,
            );
      }
    } catch (e) {
      // Handle error loading links
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        // Fallback to WebBrowser if external launch fails
        if (mounted) {
          context.router.push(WebBrowserRoute(url: url));
        }
      }
    } catch (e) {
      // Fallback to WebBrowser
      if (mounted) {
        context.router.push(WebBrowserRoute(url: url));
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const CustomAppBar(
        title: 'Useful Links',
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: BlocBuilder<UsefulLinksCubit, UsefulLinksState>(
          builder: (context, state) {
            if (state is UsefulLinksLoading) {
              return const Center(
                child: CircularProgressIndicator(
                  color: AppColors.primaryBlue,
                ),
              );
            } else if (state is UsefulLinksError) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: AppColors.blackTint1,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error',
                        style: Theme.of(context).textTheme.montserratTitleSmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        state.message,
                        textAlign: TextAlign.center,
                        style: Theme.of(context)
                            .textTheme
                            .montserratParagraphSmall,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _loadUsefulLinks,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryBlue,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              );
            } else if (state is UsefulLinksLoaded) {
              return _buildUsefulLinksList(
                  state.response.data?.usefulLinks ?? []);
            }

            return const Center(
              child: Text('Welcome to Useful Links'),
            );
          },
        ),
      ),
    );
  }

  Widget _buildUsefulLinksList(List<UsefulLink> links) {
    if (links.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.link_off,
                size: 64,
                color: AppColors.blackTint1,
              ),
              const SizedBox(height: 16),
              Text(
                'No Useful Links',
                style: Theme.of(context).textTheme.montserratTitleSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'There are currently no useful links available.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.montserratParagraphSmall,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 0),
      itemCount: links.length,
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        color: AppColors.lightGrey2,
      ),
      itemBuilder: (context, index) {
        final link = links[index];
        return _buildLinkItem(link);
      },
    );
  }

  Widget _buildLinkItem(UsefulLink link) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _launchUrl(link.linkUrl ?? ''),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          child: Row(
            children: [
              // Icon or placeholder

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      link.linkTitle ?? 'Untitled Link',
                      style: Theme.of(context).textTheme.montserratTitleSmall,
                    ),
                  ],
                ),
              ),

              // Arrow
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.blackTint1,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
