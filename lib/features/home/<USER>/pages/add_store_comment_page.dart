import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/models/store_comment_model.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/store_comments/store_comments_cubit.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class AddStoreCommentPage extends StatefulWidget {
  final String taskId;
  final StoreCommentData? comment;
  final bool isEditMode;

  const AddStoreCommentPage({
    super.key,
    required this.taskId,
    this.comment,
    this.isEditMode = false,
  });

  @override
  State<AddStoreCommentPage> createState() => _AddStoreCommentPageState();
}

class _AddStoreCommentPageState extends State<AddStoreCommentPage> {
  final _formKey = GlobalKey<FormState>();
  final _commentController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.isEditMode && widget.comment != null) {
      _commentController.text = widget.comment!.comment ?? '';
    }
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _saveComment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final cubit = sl<StoreCommentsCubit>();
      await cubit.saveStoreComment(
        taskId: widget.taskId,
        comment: _commentController.text.trim(),
        storeCommentId:
            widget.isEditMode ? widget.comment?.storeCommentId : null,
      );

      if (mounted) {
        SnackBarService.success(
          context: context,
          message: widget.isEditMode
              ? 'Comment updated successfully'
              : 'Comment added successfully',
        );
        context.router.maybePop(true);
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to save comment: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: widget.isEditMode ? 'Edit Store Comment' : 'Add Store Comment',
        backgroundColor: Colors.white,
        titleColor: AppColors.black,
        actions: [
          if (!_isLoading)
            IconButton(
              onPressed: _saveComment,
              icon: const Icon(
                Icons.check,
                color: AppColors.primaryBlue,
              ),
            ),
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: AppColors.primaryBlue,
                  strokeWidth: 2,
                ),
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Comment',
                      style: textTheme.montserratFormsField.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.black,
                      ),
                    ),
                    const Gap(12),
                    TextFormField(
                      controller: _commentController,
                      maxLines: 5,
                      decoration: InputDecoration(
                        hintText: 'Enter your comment here...',
                        hintStyle: textTheme.montserratFormsField.copyWith(
                          color: AppColors.blackTint1.withValues(alpha: 0.6),
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              const BorderSide(color: AppColors.midGrey),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              const BorderSide(color: AppColors.midGrey),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              const BorderSide(color: AppColors.primaryBlue),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: Colors.red),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.all(16),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a comment';
                        }
                        if (value.trim().length < 3) {
                          return 'Comment must be at least 3 characters long';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
              const Gap(24),
              if (_isLoading)
                const Center(
                  child: Column(
                    children: [
                      CircularProgressIndicator(
                        color: AppColors.primaryBlue,
                      ),
                      Gap(16),
                      Text(
                        'Saving comment...',
                        style: TextStyle(
                          color: AppColors.blackTint1,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
