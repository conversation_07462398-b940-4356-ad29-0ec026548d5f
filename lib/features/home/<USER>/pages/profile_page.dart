import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/profile/profile_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/models/profile_response.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/core/database/realm_database.dart';

import '../../../../config/routes/app_router.gr.dart';

@RoutePage()
class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  String userName = "";
  String userID = "";
  String userEmail = "";
  String _profileUrl = "";
  ProfileResponse? _profileData;

  Future<void> _initializeData() async {
    final dataManager = sl<DataManager>();
    final token = await dataManager.getAuthToken();
    final userId = await dataManager.getUserId();

    if (token != null && userId != null) {
      // Load profile data using ProfileCubit
      if (mounted) {
        context.read<ProfileCubit>().loadProfile(
              token: token,
              userId: userId,
            );
      }
    }
  }

  Future<void> _handleLogout() async {
    final confirmed = await ConfirmDialog.show(
      context: context,
      title: 'Confirm logout',
      message: 'Are you sure you want to logout?',
      confirmText: 'Logout',
      cancelText: 'Cancel',
      onConfirm: () async {
        await sl<DataManager>().clearAll();
        sl<RealmDatabase>().clearAllData();
        if (mounted) {
          context.router.replaceAll([const LoginRoute()]);
        }
      },
    );
  }

  @override
  void initState() {
    super.initState();
    print('loginResponse.data: initState');

    _initializeData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2.withOpacity(.9),
      appBar: CustomAppBar(
        title: 'Profile',
        onBackPressed: () {},
      ),
      body: BlocListener<ProfileCubit, ProfileState>(
        listener: (context, state) {
          if (state is ProfileLoaded) {
            print(
                'state.profileResponse.data?.profileImageUrl: ${state.profileResponse.data?.profileImageUrl}');
            setState(() {
              _profileData = state.profileResponse;
              _profileUrl = state.profileResponse.data?.profileImageUrl ??
                  'https://randomuser.me/api/portraits/men/42.jpg';
              final data = state.profileResponse.data;
              userName =
                  "${data?.firstName ?? 'John'} ${data?.lastName ?? 'Doe'}";
              userID = "ID: ${data?.contractorId?.toString() ?? '75444'}";
              userEmail = data?.email ?? '<EMAIL>';
            });
          } else if (state is ProfileError) {
            // Handle error if needed, but keep fallback values
            print('Profile loading error: ${state.message}');
          }
        },
        child: BlocBuilder<ProfileCubit, ProfileState>(
          builder: (context, state) {
            return Column(
              children: [
                _buildUserInfoSection(
                    isLoading:
                        state is ProfileLoading || state is ProfileInitial),
                Expanded(
                  child: _buildGridSection(),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildUserInfoSection({bool isLoading = false}) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: isLoading ? _buildLoadingUserInfo() : _buildUserInfo(),
    );
  }

  Widget _buildLoadingUserInfo() {
    return Row(
      children: [
        // Loading avatar
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey.shade300,
          ),
          // child: const CircularProgressIndicator(
          //   color: AppColors.primaryBlue,
          //   strokeWidth: 3,
          // ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Loading name
              Container(
                width: double.infinity,
                height: 20,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(height: 12),
              // Loading ID
              Container(
                width: 120,
                height: 28,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              const SizedBox(height: 12),
              // Loading email
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.email_outlined,
                    size: 16,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUserInfo() {
    return Row(
      children: [
        Hero(
          tag: 'profile-avatar',
          child: Container(
              width: 100,
              height: 100,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  // BoxShadow(
                  //   color: avatarColor.withOpacity(0.3),
                  //   blurRadius: 12,
                  //   spreadRadius: 2,
                  // ),
                ],
              ),
              child: CircleAvatar(
                // backgroundColor: avatarColor,
                radius: 50,
                child: ClipOval(
                  child: Image.network(
                    _profileUrl,
                    width: 100,
                    height: 100,
                    fit: BoxFit.cover,
                  ),
                ),
              )),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                userName,
                style:
                    Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
              ),
              const SizedBox(height: 6),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  userID,
                  style: Theme.of(context)
                      .textTheme
                      .montserratParagraphXsmall
                      .copyWith(
                        color: AppColors.primaryBlue,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
              const SizedBox(height: 12),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Padding(
                    padding: EdgeInsets.only(top: 3),
                    child: Icon(
                      Icons.email_outlined,
                      size: 16,
                      color: AppColors.blackTint1,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      userEmail,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      style: Theme.of(context)
                          .textTheme
                          .montserratParagraphSmall
                          .copyWith(
                              // color: AppColors.blackTint1,
                              ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGridSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Column(
        children: [
          Expanded(
            child: Row(
              children: [
                _buildGridButton(
                  'Edit profile',
                  Icons.edit_outlined,
                  hasError: true,
                  onTap: () {
                    context.router
                        .push(EditProfileRoute(profileData: _profileData));
                  },
                  color: Colors.blue,
                ),
                _buildGridButton(
                  'Availability',
                  Icons.access_time_outlined,
                  onTap: () {
                    context.router.push(const AvailabilityRoute());
                  },
                  color: Colors.green,
                ),
                _buildGridButton(
                  'Leave',
                  Icons.event_busy_outlined,
                  onTap: () {
                    context.router.push(const LeaveRoute());
                  },
                  color: Colors.orange,
                ),
              ],
            ),
          ),
          Expanded(
            child: Row(
              children: [
                _buildGridButton(
                  'Skills',
                  Icons.star_outline_rounded,
                  hasError: true,
                  onTap: () {
                    context.router.push(const SkillsRoute());
                  },
                  color: Colors.purple,
                ),
                _buildGridButton(
                  'Induction',
                  Icons.school_outlined,
                  onTap: () {
                    context.router.push(const InductionRoute());
                  },
                  color: Colors.teal,
                ),
                _buildGridButton(
                  'History',
                  Icons.history_outlined,
                  onTap: () {
                    context.router.push(const HistoryRoute());
                  },
                  color: Colors.indigo,
                ),
              ],
            ),
          ),
          Expanded(
            child: Row(
              children: [
                _buildGridButton(
                  'Logout',
                  Icons.logout_outlined,
                  onTap: () {},
                  color: Colors.red,
                ),
                _buildGridButton(
                  isShow: false,
                  bgcolor: AppColors.lightGrey2.withOpacity(.9),
                  '',
                  Icons.bar_chart_outlined,
                  onTap: () {},
                  color: AppColors.lightGrey2.withOpacity(.9),
                ),
                _buildGridButton(
                  isShow: false,
                  bgcolor: AppColors.lightGrey2.withOpacity(.9),
                  '',
                  Icons.support_agent_outlined,
                  onTap: () {},
                  color: AppColors.lightGrey2.withOpacity(.9),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridButton(
    String label,
    IconData? icon, {
    bool hasError = false,
    required VoidCallback onTap,
    Color? color,
    Color? bgcolor,
    bool isShow = true,
  }) {
    final buttonColor = color ?? AppColors.primaryBlue;
    // final buttonColor =  Colors.grey.shade800;

    return Expanded(
      child: Container(
        margin: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: bgcolor ?? Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            isShow == true
                ? BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 4,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  )
                : const BoxShadow(
                    color: Colors.transparent,
                    blurRadius: 0,
                    spreadRadius: 0,
                    offset: Offset(0, 2),
                  ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          child: InkWell(
            onTap: label == 'Logout' ? _handleLogout : onTap,
            borderRadius: BorderRadius.circular(12),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: buttonColor.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          icon ?? Icons.ac_unit_outlined,
                          size: 28,
                          color: buttonColor,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        label,
                        textAlign: TextAlign.center,
                        style: Theme.of(context)
                            .textTheme
                            .montserratParagraphSmall
                            .copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ],
                  ),
                ),
                if (hasError)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      width: 16,
                      height: 16,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                        boxShadow: [
                          // BoxShadow(
                          //   color: Colors.red.withOpacity(0.3),
                          //   blurRadius: 4,
                          //   spreadRadius: 0,
                          // ),
                        ],
                      ),
                      child: const Icon(
                        Icons.priority_high,
                        color: Colors.white,
                        size: 10,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
