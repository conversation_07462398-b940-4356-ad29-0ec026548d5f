import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_typography.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/vacancies/vacancies_cubit.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

class ReferralFormPage extends StatefulWidget {
  final int vacancyId;

  const ReferralFormPage({
    super.key,
    required this.vacancyId,
  });

  @override
  State<ReferralFormPage> createState() => _ReferralFormPageState();
}

class _ReferralFormPageState extends State<ReferralFormPage>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Form controllers
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _stateController = TextEditingController();
  final TextEditingController _suburbController = TextEditingController();
  final TextEditingController _commentController = TextEditingController();

  // Preferred work checkboxes
  bool _retail = false;
  bool _hardware = false;
  bool _demonstrations = false;
  bool _any = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _stateController.dispose();
    _suburbController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  void _submitReferral() {
    print('DEBUG: _submitReferral called');

    // Debug: Check individual field values
    print('DEBUG: Name: "${_nameController.text.trim()}"');
    print('DEBUG: Email: "${_emailController.text.trim()}"');
    print('DEBUG: Phone: "${_phoneController.text.trim()}"');
    print('DEBUG: State: "${_stateController.text.trim()}"');
    print('DEBUG: Suburb: "${_suburbController.text.trim()}"');
    print(
        'DEBUG: Preferred work - Retail: $_retail, Hardware: $_hardware, Demonstrations: $_demonstrations, Any: $_any');

    // Debug: Check validation manually
    final nameValid = _nameController.text.trim().isNotEmpty;
    final emailValid = _emailController.text.trim().isNotEmpty &&
        RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
            .hasMatch(_emailController.text.trim());
    final phoneValid = _phoneController.text.trim().isNotEmpty;
    final stateValid = _stateController.text.trim().isNotEmpty;
    final suburbValid = _suburbController.text.trim().isNotEmpty;
    final preferenceValid = _retail || _hardware || _demonstrations || _any;

    print(
        'DEBUG: Validation results - Name: $nameValid, Email: $emailValid, Phone: $phoneValid, State: $stateValid, Suburb: $suburbValid, Preference: $preferenceValid');

    print(
        'DEBUG: Form validation result: ${_formKey.currentState?.validate() ?? false}');

    if (_formKey.currentState?.validate() ?? false) {
      print('DEBUG: Form validation passed');

      // Collect preferred work preferences
      final List<String> preferredWork = [];
      if (_retail) preferredWork.add('Retail');
      if (_hardware) preferredWork.add('Hardware');
      if (_demonstrations) preferredWork.add('Demonstrations');
      if (_any) preferredWork.add('Any');

      print('DEBUG: Preferred work selections: $preferredWork');

      // Validate that at least one preference is selected
      if (preferredWork.isEmpty) {
        print('DEBUG: No preferred work selected, showing error');
        _showErrorSnackBar('Please select at least one preferred work type');
        return;
      }

      // Join preferences with comma
      final preference = preferredWork.join(',');

      // Get form data
      final name = _nameController.text.trim();
      final email = _emailController.text.trim();
      final phone = _phoneController.text.trim();
      final state = _stateController.text.trim();
      final suburb = _suburbController.text.trim();
      final comment = _commentController.text.trim();

      print(
          'DEBUG: Form data collected - name: $name, email: $email, phone: $phone, state: $state, suburb: $suburb, preference: $preference');
      print(
          'DEBUG: About to call referVacancyWithDetails for vacancy ID: ${widget.vacancyId}');

      // Call the enhanced referVacancy method
      try {
        final cubit = context.read<VacanciesCubit>();
        print('DEBUG: Successfully got VacanciesCubit: ${cubit.runtimeType}');
        cubit.referVacancyWithDetails(
          widget.vacancyId,
          name: name,
          email: email,
          phone: phone,
          stateName: state,
          suburb: suburb,
          comment: comment,
          preference: preference,
        );
        print('DEBUG: referVacancyWithDetails called successfully');
      } catch (e) {
        print('DEBUG: Error calling referVacancyWithDetails: $e');
      }
    } else {
      print('DEBUG: Form validation failed');

      // Show specific validation errors
      final errors = <String>[];
      if (_nameController.text.trim().isEmpty) errors.add('Name is required');
      if (_emailController.text.trim().isEmpty) {
        errors.add('Email is required');
      } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
          .hasMatch(_emailController.text.trim())) {
        errors.add('Email format is invalid');
      }
      if (_phoneController.text.trim().isEmpty) {
        errors.add('Phone number is required');
      }
      if (_stateController.text.trim().isEmpty) errors.add('State is required');
      if (_suburbController.text.trim().isEmpty) {
        errors.add('Suburb is required');
      }
      if (!_retail && !_hardware && !_demonstrations && !_any) {
        errors.add('At least one preferred work type is required');
      }

      print('DEBUG: Validation errors: ${errors.join(', ')}');

      // Show error message to user
      _showErrorSnackBar(
          'Please fix the following errors: ${errors.join(', ')}');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 20),
            const Gap(8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey1,
      appBar: const CustomAppBar(
        title: 'Refer a Friend',
        showBackButton: true,
      ),
      body: BlocListener<VacanciesCubit, VacanciesState>(
        listener: (context, state) {
          print('DEBUG: BlocListener state changed to: ${state.runtimeType}');

          if (state is VacancyActionSuccess) {
            print('DEBUG: VacancyActionSuccess received: ${state.message}');
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.check_circle,
                        color: Colors.white, size: 20),
                    const Gap(8),
                    Expanded(child: Text(state.message)),
                  ],
                ),
                backgroundColor: AppColors.loginGreen,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                margin: const EdgeInsets.all(16),
              ),
            );
            Navigator.of(context).pop(); // Pop back to vacancies page
          } else if (state is VacancyActionError) {
            print('DEBUG: VacancyActionError received: ${state.message}');
            _showErrorSnackBar(state.message);
          } else if (state is VacancyActionInProgress) {
            print(
                'DEBUG: VacancyActionInProgress received for action: ${state.action}');
          }
        },
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header Card
                    _buildHeaderCard(),
                    const Gap(16),

                    // Personal Information Card
                    _buildSectionCard(
                      title: 'Personal Information',
                      icon: Icons.person_outline,
                      children: [
                        _buildModernTextField(
                          controller: _nameController,
                          label: 'Full Name',
                          hint: 'Enter full name',
                          icon: Icons.person,
                          validator: (value) {
                            if (value?.trim().isEmpty ?? true) {
                              return 'Name is required';
                            }
                            return null;
                          },
                        ),
                        const Gap(16),
                        _buildModernTextField(
                          controller: _emailController,
                          label: 'Email Address',
                          hint: 'Enter email address',
                          icon: Icons.email_outlined,
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value?.trim().isEmpty ?? true) {
                              return 'Email is required';
                            }
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                .hasMatch(value!)) {
                              return 'Please enter a valid email address';
                            }
                            return null;
                          },
                        ),
                        const Gap(16),
                        _buildModernTextField(
                          controller: _phoneController,
                          label: 'Phone Number',
                          hint: 'Enter phone number',
                          icon: Icons.phone_outlined,
                          keyboardType: TextInputType.phone,
                          validator: (value) {
                            if (value?.trim().isEmpty ?? true) {
                              return 'Phone number is required';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const Gap(16),

                    // Location Card
                    _buildSectionCard(
                      title: 'Location Details',
                      icon: Icons.location_on_outlined,
                      children: [
                        _buildModernTextField(
                          controller: _stateController,
                          label: 'State',
                          hint: 'e.g., NSW, VIC, QLD',
                          icon: Icons.map_outlined,
                          validator: (value) {
                            if (value?.trim().isEmpty ?? true) {
                              return 'State is required';
                            }
                            return null;
                          },
                        ),
                        const Gap(16),
                        _buildModernTextField(
                          controller: _suburbController,
                          label: 'Suburb',
                          hint: 'Enter suburb',
                          icon: Icons.home_outlined,
                          validator: (value) {
                            if (value?.trim().isEmpty ?? true) {
                              return 'Suburb is required';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const Gap(16),

                    // Preferred Work Card
                    _buildSectionCard(
                      title: 'Preferred Work Type',
                      icon: Icons.work_outline,
                      children: [
                        Text(
                          'Select at least one work type preference:',
                          style:
                              AppTypography.montserratParagraphSmall.copyWith(
                            color: AppColors.blackTint1,
                          ),
                        ),
                        const Gap(12),
                        _buildModernCheckboxGrid(),
                      ],
                    ),
                    const Gap(16),

                    // Comments Card
                    _buildSectionCard(
                      title: 'Additional Comments',
                      icon: Icons.comment_outlined,
                      children: [
                        _buildModernTextField(
                          controller: _commentController,
                          label: 'Comments (Optional)',
                          hint:
                              'Any additional comments or notes about the referral...',
                          icon: Icons.notes,
                          maxLines: 3,
                          validator: null, // Optional field
                        ),
                      ],
                    ),
                    const Gap(24),

                    // Submit Button
                    _buildSubmitButton(),
                    const Gap(24),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryBlue,
            AppColors.primaryBlue.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.person_add,
                  color: Colors.white,
                  size: 22,
                ),
              ),
              const Gap(14),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Referral Form',
                      style: AppTypography.montserratHeadingMedium.copyWith(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const Gap(2),
                    Text(
                      'Help us find great talent by referring someone you know',
                      style: AppTypography.montserratParagraphSmall.copyWith(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withOpacity(0.06),
            blurRadius: 12,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primaryBlue,
                  size: 18,
                ),
              ),
              const Gap(10),
              Text(
                title,
                style: AppTypography.montserratHeadingMedium.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const Gap(18),
          ...children,
        ],
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int? maxLines,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTypography.montserratFormsfield.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.black,
          ),
        ),
        const Gap(6),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withOpacity(0.04),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            keyboardType: keyboardType,
            maxLines: maxLines ?? 1,
            validator: validator,
            style: AppTypography.montserratParagraphSmall.copyWith(
              fontSize: 15,
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: AppTypography.montserratParagraphSmall.copyWith(
                color: AppColors.blackTint1,
                fontSize: 15,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(10),
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primaryBlue,
                  size: 16,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: AppColors.borderColor,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: AppColors.borderColor,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: AppColors.primaryBlue,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.red.shade400,
                  width: 1,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.red.shade400,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 14,
                vertical: 14,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernCheckboxGrid() {
    final options = [
      {'title': 'Retail', 'value': _retail},
      {'title': 'Hardware', 'value': _hardware},
      {'title': 'Demonstrations', 'value': _demonstrations},
      {'title': 'Any', 'value': _any},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
        childAspectRatio: 2.8,
      ),
      itemCount: options.length,
      itemBuilder: (context, index) {
        final option = options[index];
        final isSelected = option['value'] as bool;

        return GestureDetector(
          onTap: () {
            setState(() {
              switch (index) {
                case 0:
                  _retail = !_retail;
                  break;
                case 1:
                  _hardware = !_hardware;
                  break;
                case 2:
                  _demonstrations = !_demonstrations;
                  break;
                case 3:
                  _any = !_any;
                  break;
              }
            });
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.primaryBlue.withOpacity(0.1)
                  : AppColors.lightGrey1,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color:
                    isSelected ? AppColors.primaryBlue : AppColors.borderColor,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  isSelected ? Icons.check_circle : Icons.circle_outlined,
                  color:
                      isSelected ? AppColors.primaryBlue : AppColors.blackTint1,
                  size: 18,
                ),
                const Gap(6),
                Expanded(
                  child: Text(
                    option['title'] as String,
                    style: AppTypography.montserratParagraphSmall.copyWith(
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w500,
                      color:
                          isSelected ? AppColors.primaryBlue : AppColors.black,
                      fontSize: 12,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      height: 52,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryBlue,
            AppColors.primaryBlue.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () {
          print('DEBUG: Submit button pressed');
          _submitReferral();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.send,
              color: Colors.white,
              size: 18,
            ),
            const Gap(8),
            Text(
              'Submit Referral',
              style: AppTypography.montserratTitleSmall.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.white,
                fontSize: 15,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
