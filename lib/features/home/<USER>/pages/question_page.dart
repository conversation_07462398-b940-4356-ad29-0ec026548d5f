import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/question_card.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/comment_card.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_questions_state.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';

import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/services/photo_service.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_cubit.dart';

@RoutePage()
class QuestionPage extends StatefulWidget {
  final num formId;
  final num? taskId;

  const QuestionPage({
    super.key,
    required this.formId,
    this.taskId,
  });

  @override
  State<QuestionPage> createState() => _QuestionPageState();
}

class _QuestionPageState extends State<QuestionPage> {
  // State for comment fields
  final Map<num, String?> _selectedCommentTypes = {};
  final Map<num, TextEditingController> _commentControllers = {};

  // State for question visibility based on conditional logic
  final Map<num, bool> _questionVisibility = {};

  // State for comment validation
  final Map<num, String?> _commentValidationErrors = {};

  // State for photo validation errors (separate from regular validation)
  final Map<num, String?> _photoValidationErrors = {};

  // Photo refresh counter to trigger photo reload in QuestionCard widgets
  int _photoRefreshCounter = 0;

  // Form data loaded from database
  entities.Form? _form;
  bool _isLoading = true;
  String? _errorMessage;

  // Photo service for loading and managing photos
  late final PhotoService _photoService;

  // State management for photos per question
  final Map<num, List<String>> _questionPhotos = {};

  // Store last validation error message for better user feedback
  String? _lastValidationError;

  // Get questions from the form
  List<entities.Question>? get questionItems => _form?.questions;

  // Get visible questions based on conditional logic
  List<entities.Question> get visibleQuestions {
    if (questionItems == null) return [];

    debugPrint(
        'QCOND: Getting visible questions from ${questionItems!.length} total questions');

    final visible = questionItems!.where((question) {
      // Always show questions without questionId (like comment items)
      if (question.questionId == null) {
        debugPrint(
            'QCOND: Question without ID always visible: ${question.questionDescription}');
        return true;
      }

      // Show questions based on visibility state (default to true if not set)
      final isVisible = _questionVisibility[question.questionId!] ?? true;
      debugPrint(
          'QCOND: Question ${question.questionId} visibility check: ${isVisible ? 'VISIBLE' : 'HIDDEN'}');
      return isVisible;
    }).toList();

    debugPrint('QCOND: === QUESTION VISIBILITY SUMMARY ===');
    debugPrint(
        'QCOND: Total questions: ${questionItems!.length}, Visible questions: ${visible.length}');
    debugPrint('QCOND: Visibility map: $_questionVisibility');
    debugPrint('QCOND: === END SUMMARY ===');

    return visible;
  }

  @override
  void initState() {
    debugPrint('QCOND: === INITSTATE CALLED ===');
    debugPrint('QCOND: TaskId=${widget.taskId}, FormId=${widget.formId}');

    super.initState();
    _photoService = sl<PhotoService>();
    _loadFormFromDatabase();

    debugPrint('QCOND: === INITSTATE COMPLETE ===');
  }

  @override
  void dispose() {
    _commentControllers.forEach((_, controller) => controller.dispose());
    super.dispose();
  }

  /// Load form from database using formId
  Future<void> _loadFormFromDatabase() async {
    debugPrint('QCOND: === STARTING FORM LOAD FROM DATABASE ===');
    debugPrint('QCOND: TaskId=${widget.taskId}, FormId=${widget.formId}');

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Use FormUtils to get task and form data
      final taskWithForm = await FormUtils.getTaskWithFormModel(
        widget.taskId!.toInt(),
        widget.formId.toInt(),
      );

      if (taskWithForm == null) {
        debugPrint('QCOND: TaskWithForm is null');
        setState(() {
          _isLoading = false;
          _errorMessage = 'Task or form not found in database';
        });
        return;
      }

      final taskEntity = taskWithForm['task'] as entities.TaskDetail;
      final formEntity = taskEntity.forms
          ?.where((form) => form.formId == widget.formId)
          .firstOrNull;

      if (formEntity == null) {
        debugPrint('QCOND: FormEntity is null');
        setState(() {
          _isLoading = false;
          _errorMessage = 'Form entity not found';
        });
        return;
      }

      debugPrint('QCOND: Form loaded successfully: ${formEntity.formName}');
      debugPrint(
          'QCOND: Form has ${formEntity.questions?.length ?? 0} questions');

      setState(() {
        _form = formEntity;
        _isLoading = false;
      });

      // Initialize form-related data after loading
      await _initializeFormData();

      debugPrint('QCOND: === FORM LOAD COMPLETE ===');
    } catch (e) {
      debugPrint('QCOND: ERROR loading form: ${e.toString()}');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading form: ${e.toString()}';
      });
    }
  }

  /// Initialize form-related data after form is loaded
  Future<void> _initializeFormData() async {
    if (_form?.questions != null) {
      for (final question in _form!.questions!) {
        if (question.isComment == true && question.questionId != null) {
          final questionId = question.questionId!;
          _commentControllers[questionId] = TextEditingController();
          // Initialize with null or a default first item if applicable
          _selectedCommentTypes[questionId] = null;

          // Add listener to clear validation errors when user types and auto-save
          _commentControllers[questionId]!.addListener(() {
            if (_commentValidationErrors.containsKey(questionId)) {
              setState(() {
                _commentValidationErrors[questionId] = null;
              });
            }
            // Auto-save when comment becomes complete
            _autoSaveCommentIfComplete(questionId);
          });
        }
      }
    }

    debugPrint('QCOND: === INITIALIZING FORM DATA ===');

    // Check if any answers are saved for the questions that trigger conditional logic
    final hasTriggeringAnswers = _hasAnswersForTriggeringQuestions();

    // Initialize question visibility and process conditional logic
    _initializeQuestionVisibility();

    // Only process conditional logic if relevant answers exist
    if (hasTriggeringAnswers) {
      _processQuestionConditionalLogic();
    }

    // Load saved comment data AFTER controllers are set up
    _loadSavedCommentData();

    // Load saved photos for all questions
    await _loadSavedPhotos();

    // Update progress tracking in database
    await _updateProgressTracking();

    debugPrint('QCOND: === FORM DATA INITIALIZATION COMPLETE ===');
  }

  /// Gets all measurement IDs that are used in question conditions.
  Set<num> _getTriggeringMeasurementIds() {
    final ids = <num>{};
    if (_form?.questions == null) return ids;

    for (final question in _form!.questions!) {
      if (question.questionConditions != null) {
        for (final condition in question.questionConditions!) {
          if (condition.measurementId != null) {
            ids.add(condition.measurementId!);
          }
        }
      }
    }
    debugPrint('QCOND: Found ${ids.length} triggering measurement IDs: $ids');
    return ids;
  }

  /// Checks if there are any saved answers for the questions that trigger conditional logic.
  bool _hasAnswersForTriggeringQuestions() {
    if (widget.taskId == null || _form?.formId == null) return false;

    final triggeringIds = _getTriggeringMeasurementIds();
    if (triggeringIds.isEmpty) {
      debugPrint(
          'QCOND: No triggering questions found, so no answers to check.');
      return false; // No conditions means no answers to check for.
    }

    try {
      final realm = RealmDatabase.instance.realm;
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;
      if (taskModel == null) return false;

      final formModel = taskModel.forms
          .where((form) => form.formId == _form?.formId?.toInt())
          .firstOrNull;
      if (formModel == null) return false;

      for (final answer in formModel.questionAnswers) {
        if (answer.measurementId != null &&
            triggeringIds.contains(answer.measurementId!.toInt())) {
          debugPrint(
              'QCOND: Found saved answer for triggering measurement ID: ${answer.measurementId}');
          return true; // Found at least one relevant answer
        }
      }

      debugPrint('QCOND: No saved answers found for any triggering questions.');
      return false;
    } catch (e) {
      debugPrint('QCOND: Error checking for triggering answers: $e');
      return false;
    }
  }

  /// Load saved photos for questions with level 2 (photo_tags_two)
  Future<void> _loadSavedPhotos() async {
    if (widget.taskId == null || _form?.questions == null) {
      logger('TaskId or Questions is null, cannot load saved photos');
      return;
    }

    try {
      // Clear existing photo data
      _questionPhotos.clear();

      final taskId = widget.taskId!.toInt();

      final savedPhotos = await _photoService.getPhotosFromTask(
        taskId: taskId,
        folderId: null,
      );

      // Filter photos for level 2 (photo_tags_two) and group by questionId
      final filteredPhotos = savedPhotos.where((photo) {
        return photo.combineTypeId == 2; // Level 2 for photo_tags_two
      }).toList();

      if (filteredPhotos.isNotEmpty && mounted) {
        setState(() {
          // Group photos by question ID
          for (final photo in filteredPhotos) {
            final imageUrl = photo.photoUrl ?? photo.localPath;
            if (photo.questionId != null && imageUrl != null) {
              final questionId = photo.questionId!;
              if (!_questionPhotos.containsKey(questionId)) {
                _questionPhotos[questionId] = [];
              }
              _questionPhotos[questionId]!.add(imageUrl);
            }
          }
        });
      }

      logger('Loaded ${filteredPhotos.length} photos for Question page');
      logger('Photo distribution: $_questionPhotos');
    } catch (e) {
      logger('Error loading saved photos: $e');
    }
  }

  /// Initialize question visibility based on conditional logic
  /// All questions should be visible by default unless they are targets of conditional logic
  void _initializeQuestionVisibility() {
    if (_form?.questions == null) return;

    debugPrint(
        'QCOND: Initializing visibility for ${_form!.questions!.length} questions');

    // Make all questions visible by default.
    // The conditional logic will hide them later if necessary.
    for (final question in _form!.questions!) {
      if (question.questionId != null) {
        _questionVisibility[question.questionId!] = true;
        debugPrint(
            'QCOND: Question ${question.questionId} set to VISIBLE by default');
      }
    }

    debugPrint(
        'QCOND: Initialized visibility for ${_questionVisibility.length} questions: $_questionVisibility');

    // Apply restricted multi-parent question visibility logic
    _handleRestrictedMultiQuestionVisibility();
  }

  /// Check if a question is the target of any conditional logic
  bool _isQuestionTargetOfConditionalLogic(num questionId) {
    if (_form?.questions == null) return false;

    debugPrint(
        'QCOND: Checking if question $questionId is target of conditional logic');

    for (final question in _form!.questions!) {
      if (question.questionConditions == null ||
          question.questionConditions!.isEmpty) {
        continue;
      }

      debugPrint(
          'QCOND: Question ${question.questionId} has ${question.questionConditions!.length} conditions');

      for (final condition in question.questionConditions!) {
        debugPrint(
            'QCOND: Condition -> measurementId=${condition.measurementId}, optionId=${condition.measurementOptionId}, actionQuestionId=${condition.actionQuestionId}, action=${condition.action}');

        if (condition.actionQuestionId == questionId) {
          // This question is a target of conditional logic
          // Hide it by default if the action is 'appear'
          if (condition.action?.toLowerCase() == 'appear') {
            debugPrint(
                'QCOND: Question $questionId IS TARGET of "appear" action');
            return true;
          } else {
            debugPrint(
                'QCOND: Question $questionId is target of "${condition.action}" action (not appear)');
          }
        }
      }
    }

    debugPrint(
        'QCOND: Question $questionId is NOT target of conditional logic');
    return false;
  }

  /// Process question conditional logic based on saved QuestionAnswer data
  void _processQuestionConditionsFromSavedData() {
    if (widget.taskId == null || _form?.questions == null) return;

    debugPrint(
        'QCOND: Starting to process question conditions from saved data');
    debugPrint('QCOND: TaskId=${widget.taskId}, FormId=${_form?.formId}');

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('QCOND: No task found for taskId: ${widget.taskId}');
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == _form?.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('QCOND: No form found for formId: ${_form?.formId}');
        return;
      }

      // Get all saved QuestionAnswer objects for this form
      final savedAnswers = formModel.questionAnswers;
      debugPrint('QCOND: Found ${savedAnswers.length} saved answers');

      // If no answers are saved, there's no conditional logic to process, so we leave all questions visible.
      if (savedAnswers.isEmpty) {
        debugPrint('QCOND: No saved answers, skipping conditional logic.');
        return;
      }

      // First, preemptively hide all questions that are targets of an 'appear' action.
      // They will be made visible again only if a condition is met.
      for (final question in _form!.questions!) {
        if (question.questionId != null &&
            _isQuestionTargetOfConditionalLogic(question.questionId!)) {
          _questionVisibility[question.questionId!] = false;
          debugPrint(
              'QCOND: Hiding target question ${question.questionId} before processing conditions.');
        }
      }

      // Debug: Print all saved answers
      for (final answer in savedAnswers) {
        debugPrint(
            'QCOND: Saved answer -> measurementId=${answer.measurementId}, optionId=${answer.measurementOptionId}, textResult=${answer.measurementTextResult}');
      }

      // Track which questions have had conditional actions applied to them
      final Set<num> questionsWithAppliedActions = {};

      // Process each question's conditions
      for (final question in _form!.questions!) {
        if (question.questionConditions == null ||
            question.questionConditions!.isEmpty) {
          continue;
        }

        debugPrint(
            'QCOND: Processing ${question.questionConditions!.length} conditions for question ${question.questionId}');

        // Process each condition for this question
        for (final condition in question.questionConditions!) {
          if (condition.measurementId == null ||
              condition.measurementOptionId == null ||
              condition.actionQuestionId == null ||
              condition.action == null) {
            debugPrint(
                'QCOND: Skipping incomplete condition: measurementId=${condition.measurementId}, optionId=${condition.measurementOptionId}, actionQuestionId=${condition.actionQuestionId}, action=${condition.action}');
            continue;
          }

          debugPrint(
              'QCOND: Checking condition: measurementId=${condition.measurementId}, optionId=${condition.measurementOptionId} -> ${condition.action} question ${condition.actionQuestionId}');

          // Find matching saved answer for this measurement
          final matchingAnswer = savedAnswers
              .where((answer) =>
                  answer.measurementId == condition.measurementId!.toInt() &&
                  answer.measurementOptionId ==
                      condition.measurementOptionId!.toInt())
              .firstOrNull;

          if (matchingAnswer != null) {
            debugPrint(
                'QCOND: MATCH FOUND! Applying action: ${condition.action} to question ${condition.actionQuestionId}');

            // Apply the conditional action
            _applyQuestionConditionalAction(
              condition.actionQuestionId!,
              condition.action!,
            );

            // Track that this question has had an action applied
            questionsWithAppliedActions.add(condition.actionQuestionId!);
          } else {
            debugPrint(
                'QCOND: No matching answer found for measurementId=${condition.measurementId}, optionId=${condition.measurementOptionId}');
          }
        }
      }

      // Handle questions that are targets of conditional logic but have no matching conditions
      // These should be made visible when the user selects an answer that doesn't trigger hiding
      _handleQuestionsWithoutMatchingConditions(
          savedAnswers, questionsWithAppliedActions);

      debugPrint(
          'QCOND: Finished processing question conditional logic from saved data');
    } catch (e) {
      debugPrint('QCOND: ERROR processing question conditional logic: $e');
    }
  }

  /// Handle questions that are targets of conditional logic but have no matching conditions applied
  /// This makes questions visible when the user selects answers that don't trigger conditional hiding
  void _handleQuestionsWithoutMatchingConditions(
    List<QuestionAnswerModel> savedAnswers,
    Set<num> questionsWithAppliedActions,
  ) {
    if (_form?.questions == null) return;

    debugPrint('QCOND: Handling questions without matching conditions');
    debugPrint(
        'QCOND: Questions with applied actions: $questionsWithAppliedActions');

    // Get all measurement IDs that have saved answers
    final Set<num> answeredMeasurements = savedAnswers
        .where((answer) => answer.measurementId != null)
        .map((answer) => answer.measurementId!.toInt())
        .toSet();

    debugPrint('QCOND: Answered measurements: $answeredMeasurements');

    // Find all questions that are targets of conditional logic but haven't had actions applied
    for (final question in _form!.questions!) {
      if (question.questionConditions == null ||
          question.questionConditions!.isEmpty) {
        continue;
      }

      for (final condition in question.questionConditions!) {
        if (condition.measurementId == null ||
            condition.actionQuestionId == null ||
            condition.action == null) {
          continue;
        }

        final targetQuestionId = condition.actionQuestionId!;
        final measurementId = condition.measurementId!;

        // If this measurement has been answered but no action was applied to this target question
        if (answeredMeasurements.contains(measurementId.toInt()) &&
            !questionsWithAppliedActions.contains(targetQuestionId)) {
          debugPrint(
              'QCOND: Question $targetQuestionId is target of conditional logic but no action was applied');
          debugPrint(
              'QCOND: Making question $targetQuestionId VISIBLE (default behavior when conditions don\'t match)');

          // Make the question visible since the answered option doesn't trigger conditional hiding
          _questionVisibility[targetQuestionId] = true;
        }
      }
    }

    debugPrint(
        'QCOND: Updated visibility after handling unmatched conditions: $_questionVisibility');
  }

  /// Process question conditional logic based on saved QuestionAnswer data from QPMDPage
  /// This method coordinates the conditional logic processing
  void _processQuestionConditionalLogic() {
    if (widget.taskId == null || _form?.questions == null) return;

    debugPrint('QCOND: Starting main conditional logic processing');

    try {
      // Process question-level conditional logic based on saved answers
      _processQuestionConditionsFromSavedData();

      // Apply restricted multi-parent question visibility logic
      _handleRestrictedMultiQuestionVisibility();

      debugPrint('QCOND: Conditional logic processing complete');

      // Update UI after processing all conditional logic
      setState(() {});
    } catch (e) {
      debugPrint('QCOND: ERROR in main conditional logic processing: $e');
    }
  }

  /// Apply conditional action to a target question
  void _applyQuestionConditionalAction(
    num targetQuestionId,
    String action,
  ) {
    debugPrint(
        'QCOND: Applying action "$action" to question $targetQuestionId');

    if (action.toLowerCase() == 'appear') {
      _questionVisibility[targetQuestionId] = true;
      debugPrint(
          'QCOND: Question $targetQuestionId set to APPEAR (visible=true)');
    } else if (action.toLowerCase() == 'disappear') {
      _questionVisibility[targetQuestionId] = false;
      debugPrint(
          'QCOND: Question $targetQuestionId set to DISAPPEAR (visible=false)');
    } else {
      debugPrint(
          'QCOND: Unknown action "$action" for question $targetQuestionId');
    }

    debugPrint('QCOND: Updated visibility map: $_questionVisibility');
  }

  /// Handle restricted multi question visibility logic
  /// If any question meets the restrictedMultiQuestion criteria:
  /// - Initially: Hide the restrictedMultiQuestion, show all other questions
  /// - After filling: Show ALL questions when any other question has saved counter value > 0
  /// This logic only applies if there are no question conditions that would override it
  void _handleRestrictedMultiQuestionVisibility() {
    if (_form?.questions == null || _form!.questions!.isEmpty) return;

    debugPrint('QCOND: Checking restricted multi question visibility logic');

    try {
      // Check if any questions have question conditions - if so, let those take precedence
      final hasQuestionConditions = _form!.questions!.any((question) =>
          question.questionConditions != null &&
          question.questionConditions!.isNotEmpty);

      if (hasQuestionConditions) {
        debugPrint(
            'QCOND: Skipping restricted multi question logic - question conditions found');
        return;
      }

      debugPrint(
          'QCOND: No question conditions found, proceeding with restricted multi logic');

      // Step 1: Identify restrictedMultiQuestion
      entities.Question? restrictedMultiQuestion;

      for (final question in _form!.questions!) {
        if (question.isMulti == true &&
            question.multiMeasurementId != null &&
            question.multiMeasurementId != 0) {
          restrictedMultiQuestion = question;
          debugPrint(
              'QCOND: Found restrictedMultiQuestion: ${question.questionId} - ${question.questionDescription}');
          break;
        }
      }

      // Step 2: If no restrictedMultiQuestion found, return early
      if (restrictedMultiQuestion == null) {
        debugPrint('QCOND: No restrictedMultiQuestion found');
        return;
      }

      // Step 3: Check if any OTHER question (not the restrictedMultiQuestion) has saved counter value > 0
      final hasOtherQuestionWithValue =
          _hasOtherQuestionWithCounterValue(restrictedMultiQuestion);
      debugPrint(
          'QCOND: Other questions have counter value > 0: $hasOtherQuestionWithValue');

      // Step 4: Apply visibility rules
      if (hasOtherQuestionWithValue) {
        // Make ALL questions visible (including restrictedMultiQuestion) when other questions have value > 0
        for (final question in _form!.questions!) {
          if (question.questionId != null) {
            _questionVisibility[question.questionId!] = true;
          }
        }
        debugPrint(
            'QCOND: Made all questions visible due to other questions having counter value > 0');
      } else {
        // Initially: Hide the restrictedMultiQuestion, show all other questions
        for (final question in _form!.questions!) {
          if (question.questionId != null) {
            if (question.questionId == restrictedMultiQuestion.questionId) {
              // Hide the restrictedMultiQuestion
              _questionVisibility[question.questionId!] = false;
            } else {
              // Show all other questions
              _questionVisibility[question.questionId!] = true;
            }
          }
        }
        debugPrint(
            'QCOND: Hidden restrictedMultiQuestion, showing all other questions');
      }

      debugPrint(
          'QCOND: Final visibility after restricted multi logic: $_questionVisibility');
    } catch (e) {
      debugPrint(
          'QCOND: ERROR in _handleRestrictedMultiQuestionVisibility: $e');
    }
  }

  /// Check if any OTHER question (not the restrictedMultiQuestion) has saved counter value > 0
  /// Returns true if any other question has a counter value > 0 in the QuestionAnswer database
  bool _hasOtherQuestionWithCounterValue(
      entities.Question restrictedMultiQuestion) {
    if (widget.taskId == null || _form?.questions == null) {
      return false;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return false;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == _form?.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${_form?.formId}');
        return false;
      }

      // Check all questions except the restrictedMultiQuestion
      for (final question in _form!.questions!) {
        if (question.questionId == null ||
            question.questionId == restrictedMultiQuestion.questionId) {
          continue; // Skip the restrictedMultiQuestion itself
        }

        // Look for QuestionAnswer entries for this question with counter values
        final questionAnswers = formModel.questionAnswers
            .where(
                (answer) => answer.questionId == question.questionId!.toInt())
            .toList();

        for (final answer in questionAnswers) {
          if (answer.measurementTextResult != null) {
            final counterValue = int.tryParse(answer.measurementTextResult!);
            if (counterValue != null && counterValue > 0) {
              debugPrint(
                  'Found counter value > 0 in other question: ${question.questionId}, value: $counterValue');
              return true;
            }
          }
        }
      }

      debugPrint('No other questions have counter value > 0');
      return false;
    } catch (e) {
      debugPrint('Error checking other questions counter values: $e');
      return false;
    }
  }

  /// Load saved comment data from database
  void _loadSavedCommentData() {
    if (widget.taskId == null || _form?.formId == null) return;

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) return;

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == _form?.formId!.toInt())
          .firstOrNull;

      if (formModel == null) return;

      // Get saved comment answers
      final savedCommentAnswers = formModel.questionAnswers
          .where((qa) => qa.isComment == true)
          .toList();

      // Prepare data for state update
      final Map<num, String> textData = {};
      final Map<num, String> dropdownData = {};

      // Restore comment data
      for (final commentAnswer in savedCommentAnswers) {
        if (commentAnswer.questionId != null) {
          final questionId = commentAnswer.questionId!;

          // Restore text field value
          if (_commentControllers.containsKey(questionId) &&
              commentAnswer.measurementTextResult != null) {
            _commentControllers[questionId]!.text =
                commentAnswer.measurementTextResult!;
            textData[questionId] = commentAnswer.measurementTextResult!;
          }

          // Restore dropdown selection
          if (commentAnswer.commentTypeId != null) {
            // Find the comment type by ID
            final question = _form?.questions?.firstWhere(
                (q) => q.questionId == questionId,
                orElse: () => entities.Question());

            if (question?.commentTypes != null) {
              final commentType = question!.commentTypes!.firstWhere(
                  (ct) => ct.commentTypeId == commentAnswer.commentTypeId,
                  orElse: () => entities.CommentType());

              if (commentType.commentType != null) {
                dropdownData[questionId] = commentType.commentType!;
              }
            }
          }
        }
      }

      // Update state with all loaded data at once
      if (dropdownData.isNotEmpty) {
        setState(() {
          _selectedCommentTypes.addAll(dropdownData);
        });
      }

      debugPrint('Loaded ${savedCommentAnswers.length} saved comments');
      debugPrint('Text data: $textData');
      debugPrint('Dropdown data: $dropdownData');
    } catch (e) {
      debugPrint('Error loading saved comment data: $e');
    }
  }

  /// Check if a particular comment is validated
  bool _isCommentValidated(entities.Question question) {
    if (question.isComment != true || question.questionId == null) {
      return false; // Not a comment question
    }

    final questionId = question.questionId!;
    final controller = _commentControllers[questionId];
    final selectedType = _selectedCommentTypes[questionId];

    // Check if comment has dropdown options
    final hasDropdownOptions =
        question.commentTypes != null && question.commentTypes!.isNotEmpty;

    // For mandatory comments, validate both dropdown and text field
    if (question.isCommentMandatory == true) {
      // Validate dropdown if it exists
      if (hasDropdownOptions) {
        if (selectedType == null || selectedType.isEmpty) {
          return false; // Dropdown not selected
        }
      }

      // Always validate text field for mandatory comments
      if (controller == null || controller.text.trim().isEmpty) {
        return false; // Text field is empty
      }
    } else {
      // For non-mandatory comments, consider it validated if either:
      // 1. Both dropdown and text are filled (if dropdown exists)
      // 2. Text is filled (if no dropdown)
      // 3. Nothing is filled (optional comment)

      final hasTextContent =
          controller != null && controller.text.trim().isNotEmpty;
      final hasDropdownSelection =
          selectedType != null && selectedType.isNotEmpty;

      if (hasDropdownOptions) {
        return (hasTextContent && hasDropdownSelection);
      } else {
        return hasTextContent;
      }
    }

    return true; // Comment is validated
  }

  /// Generate QuestionAnswer objects for comments
  List<entities.QuestionAnswer> _generateCommentAnswers() {
    final commentAnswers = <entities.QuestionAnswer>[];

    if (_form?.questions == null) return commentAnswers;

    for (final question in _form!.questions!) {
      if (question.isComment == true && question.questionId != null) {
        final questionId = question.questionId!;
        final controller = _commentControllers[questionId];
        final selectedType = _selectedCommentTypes[questionId];

        // Only save if there's actual content
        if ((controller != null && controller.text.trim().isNotEmpty) ||
            (selectedType != null && selectedType.isNotEmpty)) {
          // Find comment type ID if dropdown is selected
          num? commentTypeId;
          if (selectedType != null && question.commentTypes != null) {
            final commentType = question.commentTypes!.firstWhere(
                (ct) => ct.commentType == selectedType,
                orElse: () => entities.CommentType());
            commentTypeId = commentType.commentTypeId;
          }

          final questionAnswer = entities.QuestionAnswer(
            taskId: widget.taskId,
            formId: _form?.formId,
            questionId: questionId,
            questionpartId: null,
            measurementId: null,
            measurementTypeId: null,
            measurementOptionId: null,
            measurementTextResult: controller?.text.trim(),
            isComment: true,
            commentTypeId: commentTypeId,
          );

          commentAnswers.add(questionAnswer);
        }
      }
    }

    return commentAnswers;
  }

  /// Save comment answers to database
  Future<bool> _saveCommentAnswersToDatabase(
      List<entities.QuestionAnswer> commentAnswers) async {
    if (widget.taskId == null || _form?.formId == null) {
      debugPrint('TaskId or FormId is null, cannot save comment answers');
      return false;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database');
        return false;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == _form?.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task');
        return false;
      }

      // Convert QuestionAnswer entities to models
      final commentAnswerModels = commentAnswers
          .map((qa) => QuestionAnswerModel(
                taskId: qa.taskId?.toInt(),
                formId: qa.formId?.toInt(),
                questionId: qa.questionId?.toInt(),
                questionpartId: qa.questionpartId?.toInt(),
                measurementId: qa.measurementId?.toInt(),
                measurementTypeId: qa.measurementTypeId?.toInt(),
                measurementOptionId: qa.measurementOptionId?.toInt(),
                measurementTextResult: qa.measurementTextResult,
                isComment: qa.isComment,
                commentTypeId: qa.commentTypeId?.toInt(),
              ))
          .toList();

      // Save to database in a write transaction
      realm.write(() {
        // Remove existing comment answers for this form
        final existingCommentAnswers = formModel.questionAnswers
            .where((qa) => qa.isComment == true)
            .toList();

        for (final existingAnswer in existingCommentAnswers) {
          formModel.questionAnswers.remove(existingAnswer);
        }

        // Add new comment answers
        for (final newAnswer in commentAnswerModels) {
          formModel.questionAnswers.add(newAnswer);
        }
      });

      debugPrint(
          'Successfully saved ${commentAnswerModels.length} comment answers to database');
      return true;
    } catch (e) {
      debugPrint('Error saving comment answers to database: $e');
      return false;
    }
  }

  /// Handle save button press with comprehensive validation
  void _handleSave() async {
    // Validate all questions including photos and mandatory questions
    final isValid = await _validateAllQuestionsAsync();

    if (!mounted) return;

    var budget = FormUtils.calculateBudget(widget.taskId!.toInt());
    // only if all mandatory questions are answered
    if (isValid && budget != null) {
      logger(
          "budget: ${budget.budgetHasChanged} ${budget.originalBudget} ${budget.tempBudget}");
      if (budget.budgetHasChanged) {
        ConfirmDialog.show(
          context: context,
          title: "Budget changed",
          message:
              "Based on the answers you provided for this form, the budget allowed for this task has been changed.",
          onConfirm: () {},
        );
      }
    }

    if (isValid) {
      final commentAnswers = _generateCommentAnswers();
      debugPrint(
          'Generated ${commentAnswers.length} comment answers for saving');

      // Save comment answers to database
      final saveSuccess = await _saveCommentAnswersToDatabase(commentAnswers);

      if (mounted) {
        if (saveSuccess) {
          // Update progress tracking after successful save
          await _updateProgressTracking();

          SnackBarService.success(
            context: context,
            message: 'Saved successfully!',
          );
          // Navigate back
          // Navigator.of(context).pop();
        } else {
          SnackBarService.error(
            context: context,
            message: 'Failed to save. Please try again.',
          );
        }
      }
    } else {
      // Show error message for validation errors
      SnackBarService.error(
        context: context,
        message: _lastValidationError ??
            'Please complete all mandatory items before saving.',
      );
    }
  }

  /// Check if a question is mandatory based on the complex logic provided
  bool _isQuestionMandatory(entities.Question question) {
    // Handle regular questions (non-multi, non-comment)
    if (question.isMulti != true && question.isComment != true) {
      // Loop through measurements, then loop through measurement_validations to check if any "required" key is true
      if (question.measurements != null) {
        for (final measurement in question.measurements!) {
          if (measurement.measurementValidations != null) {
            for (final validation in measurement.measurementValidations!) {
              if (validation.required == true) {
                return true;
              }
            }
          }
        }
      }
    }
    // Handle multi questions (FQPD pages)
    else if (question.isMulti == true) {
      // Check if this is a supplementary multi question
      final isSupplementaryQuestion = question.multiMeasurementId != null &&
          question.multiMeasurementId.toString() != "0" &&
          question.multiMeasurementId.toString().isNotEmpty;

      if (isSupplementaryQuestion) {
        // For supplementary multi questions, they're mandatory if there's a positive answer
        // in the referenced measurement that indicates items should be selected
        return _isSupplementaryMultiQuestionMandatory(question);
      } else {
        // For regular multi questions, check if they have required measurements within their parts
        return _hasRequiredMeasurementsInQuestionParts(question);
      }
    }
    // Handle supplementary questions (non-multi with multiMeasurementId)
    else if (question.multiMeasurementId != null &&
        question.multiMeasurementId.toString() != "0" &&
        question.multiMeasurementId.toString().isNotEmpty) {
      // Check if isOneOption is true (any questionpart_id contains "-")
      if (question.questionParts != null) {
        for (final questionPart in question.questionParts!) {
          if (questionPart.questionpartId != null &&
              questionPart.questionpartId.toString().contains("-")) {
            return true;
          }
        }
      }
    }
    return false;
  }

  /// Check if a supplementary multi question is mandatory based on related question answers
  bool _isSupplementaryMultiQuestionMandatory(entities.Question question) {
    if (widget.taskId == null ||
        _form?.formId == null ||
        question.multiMeasurementId == null) {
      return false;
    }

    try {
      final realm = RealmDatabase.instance.realm;
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) return false;

      final formModel = taskModel.forms
          .where((form) => form.formId == _form?.formId!.toInt())
          .firstOrNull;

      if (formModel == null) return false;

      // Find answer for the measurement that this multi question depends on
      final referencedAnswer = formModel.questionAnswers
          .where(
              (qa) => qa.measurementId == question.multiMeasurementId!.toInt())
          .firstOrNull;

      if (referencedAnswer?.measurementTextResult != null &&
          referencedAnswer!.measurementTextResult != '-') {
        final counterValue =
            int.tryParse(referencedAnswer.measurementTextResult!);
        return counterValue != null && counterValue > 0;
      }

      return false;
    } catch (e) {
      debugPrint(
          'Error checking supplementary multi question mandatory status: $e');
      return false;
    }
  }

  /// Check if question parts contain required measurements
  bool _hasRequiredMeasurementsInQuestionParts(entities.Question question) {
    if (question.questionParts == null) return false;

    // For regular multi questions, we need to check if any of the measurements
    // in the question parts have required validations
    if (question.measurements != null) {
      for (final measurement in question.measurements!) {
        if (measurement.measurementValidations != null) {
          for (final validation in measurement.measurementValidations!) {
            if (validation.required == true) {
              return true;
            }
          }
        }
      }
    }

    return false;
  }

  /// Validate FQPD (multi) question completion
  /// Returns true if the mandatory FQPD question is properly completed
  Future<bool> _validateFQPDQuestionCompletion(
      entities.Question question) async {
    if (widget.taskId == null ||
        _form?.formId == null ||
        question.questionId == null) {
      return false;
    }

    try {
      // Use FormUtils to get FQPD progress
      final progressData = await FormUtils.getFQPDPageProgress(
        taskId: widget.taskId!,
        formId: _form!.formId!,
        questionId: question.questionId!,
      );

      final totalVisible = progressData.totalVisible;
      final totalCompleted = progressData.totalCompleted;

      debugPrint(
          'FQPD validation for question ${question.questionId}: $totalCompleted of $totalVisible');

      // For FQPD questions, we need to check different criteria:

      // 1. If it's a restrictedMultiQuestion, check if it has the expected number of items
      final isRestrictedMultiQuestion = question.multiMeasurementId != null &&
          question.multiMeasurementId != 0;

      if (isRestrictedMultiQuestion) {
        // For restrictedMultiQuestion, totalVisible is based on saved value count
        // It should be completed if totalVisible > 0 and all visible items are completed
        return totalVisible > 0 && totalCompleted >= totalVisible;
      } else {
        // For regular multi questions, they're complete if:
        // 1. At least one item has been selected (totalVisible > 0), AND
        // 2. All selected items are completed (totalCompleted >= totalVisible)

        // However, for mandatory regular multi questions, we need at least one item selected
        // So if totalVisible = 0, it means no items have been selected, which is incomplete
        return totalVisible > 0 && totalCompleted >= totalVisible;
      }
    } catch (e) {
      debugPrint('Error validating FQPD question completion: $e');
      return false;
    }
  }

  /// Get camera icon info for a question based on photo_tags_two array
  Map<String, dynamic> _getCameraIconInfo(entities.Question question) {
    final result = {'show': false, 'isMandatory': false};

    if (question.photoTagsTwo == null || question.photoTagsTwo!.isEmpty) {
      return result;
    }

    result['show'] = true;

    // Check if any item has is_mandatory set to true
    for (final photoTag in question.photoTagsTwo!) {
      if (photoTag.isMandatory == true) {
        result['isMandatory'] = true;
        break;
      }
    }

    return result;
  }

  /// Check if question has a photo URL
  bool _hasPhotoUrl(entities.Question question) {
    return question.photoUrl != null && question.photoUrl!.isNotEmpty;
  }

  /// Handle question tap navigation - moved from QuestionCard
  void _handleQuestionTap(entities.Question question) async {
    final questionParts = question.questionParts ?? [];
    final hasSignature = question.hasSignature ?? false;
    final isMulti = question.isMulti ?? false;

    if (question.flip == true) {
      await context.pushRoute(FlipRoute(
        taskId: widget.taskId,
        formId: _form?.formId,
        questionId: question.questionId,
      ));
      return;
    }

    if (questionParts.length != 1 || hasSignature) {
      if (isMulti) {
        // Navigate to FQPDPage for multi questions
        await context.pushRoute(FQPDRoute(
          questionId: question.questionId,
          taskId: widget.taskId,
          formId: _form?.formId,
        ));
      } else {
        // Navigate to SubHeaderPage for questions with multiple parts or signature
        await context.pushRoute(SubHeaderRoute(
          title: question.questionDescription ?? 'Details',
          questionId: question.questionId,
          taskId: widget.taskId,
          formId: _form?.formId,
        ));
      }
    } else {
      // Navigate to QPMDPage for single measurement questions
      await context.pushRoute(QPMDRoute(
        questionId: question.questionId,
        questionpartId: questionParts.first.questionpartId,
        taskId: widget.taskId,
        formId: _form?.formId,
      ));
    }

    // Refresh page state after returning from navigation
    await _refreshPageState();
  }

  /// Handle photo section tap navigation - moved from QuestionCard
  void _handleAddPhotosTap(entities.Question question) async {
    final showCameraIcon = question.photoTagsTwo?.isNotEmpty == true ||
        question.photoTagsThree?.isNotEmpty == true;

    if (showCameraIcon &&
        (question.photoTagsTwo?.isNotEmpty == true ||
            question.photoTagsThree?.isNotEmpty == true)) {
      // Navigate to MPTPage with question and level
      final level = question.photoTagsTwo?.isNotEmpty == true ? 2 : 3;
      await context.pushRoute(MPTRoute(
        taskId: widget.taskId?.toString(),
        formId: _form?.formId?.toString(),
        questionId: question.questionId?.toString(),
        images: const [],
        question: question,
        level: level,
        questionPartId: null,
      ));

      // Refresh page state after returning from navigation
      await _refreshPageState();
    }
  }

  /// Refresh page state after returning from navigation
  /// This updates progress indicators and question visibility based on latest data
  Future<void> _refreshPageState() async {
    if (mounted) {
      debugPrint('QCOND: === REFRESHING PAGE STATE ===');

      // Check for answers to questions that trigger conditional logic
      final hasTriggeringAnswers = _hasAnswersForTriggeringQuestions();

      // Reinitialize question visibility first
      _initializeQuestionVisibility();

      // Reprocess conditional logic to update question visibility based on latest answers
      if (hasTriggeringAnswers) {
        _processQuestionConditionalLogic();
      }

      // Load saved comment data in case it was updated
      _loadSavedCommentData();

      // Reload photos in case photos were added/removed
      await _loadSavedPhotos();

      // Update progress tracking in database
      await _updateProgressTracking();

      // Increment photo refresh counter to trigger photo reload in QuestionCard widgets
      _photoRefreshCounter++;

      // Clear photo validation errors as photos may have been added/removed
      setState(() {
        _photoValidationErrors.clear();
      });

      debugPrint(
          'QCOND: Question visibility after refresh: $_questionVisibility');
      debugPrint('QCOND: === END PAGE REFRESH ===');

      // Trigger UI rebuild to reflect updated progress and visibility
      setState(() {});
    }
  }

  /// Update progress tracking in database based on visible questions
  Future<void> _updateProgressTracking() async {
    if (widget.taskId == null || _form?.formId == null) return;

    try {
      // Use FormUtils to get form progress
      final formProgress = await FormUtils.getQuestionPageItemsCount(
        taskId: widget.taskId!,
        formId: _form!.formId!,
      );

      final totalVisible = formProgress.totalVisible;
      final completedCount = formProgress.totalCompleted;

      // Update database with new counts
      final realm = RealmDatabase.instance.realm;
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel != null) {
        final formModel = taskModel.forms
            .where((form) => form.formId == _form?.formId!.toInt())
            .firstOrNull;

        if (formModel != null) {
          realm.write(() {
            formModel.kTotal = totalVisible;
            formModel.kCompleted = completedCount;
          });
        }
      }

      debugPrint(
          'Updated progress tracking: $completedCount of $totalVisible completed');
    } catch (e) {
      debugPrint('Error updating progress tracking: $e');
    }
  }

  /// Calculate progress for a question using FormUtils.getNextPageProgress
  Future<Map<String, dynamic>> _calculateQuestionProgress(
      entities.Question question) async {
    double progress = 0.0;
    String progressText = '0 of 0';

    if (question.isComment == true) {
      // For comment questions, check if there is any saved answer
      if (_isCommentValidated(question)) {
        progress = 1.0;
        progressText = '1 of 1';
      }
      return {'progress': progress, 'progressText': progressText};
    }

    if (widget.taskId == null ||
        _form?.formId == null ||
        question.questionId == null) {
      return {'progress': progress, 'progressText': progressText};
    }

    try {
      // Use FormUtils.getNextPageProgress to get accurate progress counts
      final qpmdProgress = await FormUtils.getQuestionPageProgress(
        taskId: widget.taskId!,
        formId: _form!.formId!,
        question: question,
      );

      final totalVisible = qpmdProgress.totalVisible;
      final totalCompleted = qpmdProgress.totalCompleted;

      // Calculate progress percentage
      progress = totalVisible > 0 ? totalCompleted / totalVisible : 0.0;
      progressText = '$totalCompleted of $totalVisible';

      return {'progress': progress, 'progressText': progressText};
    } catch (e) {
      debugPrint('Error calculating question progress using FormUtils: $e');
      return {'progress': progress, 'progressText': progressText};
    }
  }

  /// Validate photo requirements for a question
  String? _validatePhotos(entities.Question question) {
    if (question.questionId == null) return null;

    // Check if this question has photo requirements
    final cameraInfo = _getCameraIconInfo(question);
    if (!cameraInfo['show']) return null; // No photo requirement

    // Find the corresponding PhotoTagsTwo object
    final photoTag = _getPhotoTagForQuestion(question);
    if (photoTag == null) return null;

    // Get the required number of photos
    final requiredPhotos = photoTag.numberOfPhotos?.toInt() ?? 0;
    if (requiredPhotos <= 0) return null; // No specific requirement

    // Get the current number of uploaded photos
    final questionId = question.questionId!;
    final uploadedPhotos = _questionPhotos[questionId]?.length ?? 0;

    logger(
        'Photo validation for question $questionId: required=$requiredPhotos, uploaded=$uploadedPhotos');

    // Check if photo requirement is met
    if (uploadedPhotos < requiredPhotos) {
      return 'Please upload at least $requiredPhotos photo(s). Currently uploaded: $uploadedPhotos';
    }

    return null;
  }

  /// Get the PhotoTagsTwo object for a specific question
  entities.PhotoTagsT? _getPhotoTagForQuestion(entities.Question question) {
    if (question.photoTagsTwo == null ||
        question.photoTagsTwo!.isEmpty ||
        question.questionId == null) {
      return null;
    }

    // Find the photo tag that matches this question
    // For level 2 (photo_tags_two), we typically use the first photo tag or find by questionId
    for (final photoTag in question.photoTagsTwo!) {
      // For question-level photos, we might not have specific measurement matching
      // So we'll use the first available photo tag for this question
      return photoTag;
    }

    return null;
  }

  /// Enhanced validation that includes checking mandatory questions completion
  Future<bool> _validateAllQuestionsAsync() async {
    bool isValid = true;
    final missingItems = <String>{};

    setState(() {
      _commentValidationErrors.clear();
      _photoValidationErrors.clear();
    });

    if (_form?.questions == null) return isValid;

    for (final question in _form!.questions!) {
      if (question.questionId == null) continue;

      final questionId = question.questionId!;

      // Skip validation for hidden questions
      final isVisible = _questionVisibility[questionId] ?? true;
      if (!isVisible) continue;

      // Validate comment fields
      if (question.isComment == true && question.isCommentMandatory == true) {
        final controller = _commentControllers[questionId];
        final selectedType = _selectedCommentTypes[questionId];

        // Check if comment has dropdown options
        final hasDropdownOptions =
            question.commentTypes != null && question.commentTypes!.isNotEmpty;

        bool commentIsValid = true;

        // Validate based on comment type
        if (hasDropdownOptions) {
          // For dropdown comments, check if dropdown is selected
          if (selectedType == null || selectedType.isEmpty) {
            setState(() {
              _commentValidationErrors[questionId] = 'This field is required';
            });
            commentIsValid = false;
          }
        }

        // Always validate text field for mandatory comments
        if (controller == null || controller.text.trim().isEmpty) {
          setState(() {
            _commentValidationErrors[questionId] = 'This field is required';
          });
          commentIsValid = false;
        }

        if (!commentIsValid) {
          isValid = false;
          missingItems.add('mandatory comments');
        }
      }

      // Validate photo requirements (mandatory photos only)
      final photoError = _validatePhotos(question);
      if (photoError != null) {
        final cameraInfo = _getCameraIconInfo(question);
        final isMandatory = cameraInfo['isMandatory'] as bool;

        if (isMandatory) {
          setState(() {
            _photoValidationErrors[questionId] = photoError;
          });
          isValid = false;
          missingItems.add('mandatory photos');
        }
      }

      // Validate mandatory regular questions (non-comment questions)
      if (question.isComment != true) {
        final isMandatory = _isQuestionMandatory(question);

        if (isMandatory) {
          try {
            // Special handling for FQPD (multi) questions
            if (question.isMulti == true) {
              final isCompleted =
                  await _validateFQPDQuestionCompletion(question);
              if (!isCompleted) {
                debugPrint(
                    'Mandatory FQPD question $questionId is not completed');
                isValid = false;
                missingItems.add('mandatory questions');
              }
            } else {
              // Use FormUtils to check if this mandatory question is completed
              final progressData = await FormUtils.getQuestionPageProgress(
                taskId: widget.taskId!,
                formId: _form!.formId!,
                question: question,
              );

              final totalVisible = progressData.totalVisible;
              final totalCompleted = progressData.totalCompleted;

              // A mandatory question is considered incomplete if:
              // 1. It has visible items but not all are completed
              // 2. It has no visible items (which shouldn't happen for mandatory questions)
              final isQuestionCompleted =
                  totalVisible > 0 && totalCompleted >= totalVisible;

              if (!isQuestionCompleted) {
                debugPrint(
                    'Mandatory question $questionId is not completed: $totalCompleted of $totalVisible');
                isValid = false;
                missingItems.add('mandatory questions');
              }
            }
          } catch (e) {
            debugPrint('Error validating mandatory question $questionId: $e');
            // If we can't determine completion status, consider it incomplete for safety
            isValid = false;
            missingItems.add('mandatory questions');
          }
        }
      }
    }

    // Generate appropriate error message based on missing items
    if (!isValid) {
      if (missingItems.isEmpty) {
        _lastValidationError =
            'Please complete all mandatory items before saving.';
      } else {
        final itemsList = missingItems.toList();
        if (itemsList.length == 1) {
          _lastValidationError =
              'Please complete all ${itemsList.first} before saving.';
        } else if (itemsList.length == 2) {
          _lastValidationError =
              'Please complete all ${itemsList.first} and ${itemsList.last} before saving.';
        } else {
          final allButLast = itemsList.take(itemsList.length - 1).join(', ');
          _lastValidationError =
              'Please complete all $allButLast, and ${itemsList.last} before saving.';
        }
      }
    }

    return isValid;
  }

  /// Auto-save a comment when it becomes complete
  Future<void> _autoSaveCommentIfComplete(num questionId) async {
    // Find the question for this comment
    final question = _form?.questions?.firstWhere(
      (q) => q.questionId == questionId,
      orElse: () => entities.Question(),
    );

    if (question?.isComment != true) return;

    // Check if the comment is now complete
    if (_isCommentValidated(question!)) {
      // Generate comment answer for this specific question
      final commentAnswer = _generateSingleCommentAnswer(questionId);
      if (commentAnswer != null) {
        // Save just this comment to database
        await _saveSingleCommentToDatabase(commentAnswer);
        debugPrint('Auto-saved comment for question $questionId');
      }
    }
  }

  /// Generate QuestionAnswer object for a single comment
  entities.QuestionAnswer? _generateSingleCommentAnswer(num questionId) {
    final question = _form?.questions?.firstWhere(
      (q) => q.questionId == questionId,
      orElse: () => entities.Question(),
    );

    if (question?.isComment != true) return null;

    final controller = _commentControllers[questionId];
    final selectedType = _selectedCommentTypes[questionId];

    // Only generate if there's actual content
    if ((controller != null && controller.text.trim().isNotEmpty) ||
        (selectedType != null && selectedType.isNotEmpty)) {
      // Find comment type ID if dropdown is selected
      num? commentTypeId;
      if (selectedType != null && question!.commentTypes != null) {
        final commentType = question.commentTypes!.firstWhere(
          (ct) => ct.commentType == selectedType,
          orElse: () => entities.CommentType(),
        );
        commentTypeId = commentType.commentTypeId;
      }

      return entities.QuestionAnswer(
        taskId: widget.taskId,
        formId: _form?.formId,
        questionId: questionId,
        questionpartId: null,
        measurementId: null,
        measurementTypeId: null,
        measurementOptionId: null,
        measurementTextResult: controller?.text.trim(),
        isComment: true,
        commentTypeId: commentTypeId,
      );
    }

    return null;
  }

  /// Save a single comment to database
  Future<void> _saveSingleCommentToDatabase(
      entities.QuestionAnswer commentAnswer) async {
    if (widget.taskId == null || _form?.formId == null) {
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) return;

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == _form?.formId!.toInt())
          .firstOrNull;

      if (formModel == null) return;

      // Convert QuestionAnswer entity to model
      final commentAnswerModel = QuestionAnswerModel(
        taskId: commentAnswer.taskId?.toInt(),
        formId: commentAnswer.formId?.toInt(),
        questionId: commentAnswer.questionId?.toInt(),
        questionpartId: commentAnswer.questionpartId?.toInt(),
        measurementId: commentAnswer.measurementId?.toInt(),
        measurementTypeId: commentAnswer.measurementTypeId?.toInt(),
        measurementOptionId: commentAnswer.measurementOptionId?.toInt(),
        measurementTextResult: commentAnswer.measurementTextResult,
        isComment: commentAnswer.isComment,
        commentTypeId: commentAnswer.commentTypeId?.toInt(),
      );

      // Save to database in a write transaction
      realm.write(() {
        // Remove existing comment answer for this specific question
        final existingAnswer = formModel.questionAnswers
            .where((qa) =>
                qa.isComment == true &&
                qa.questionId == commentAnswer.questionId?.toInt())
            .firstOrNull;

        if (existingAnswer != null) {
          formModel.questionAnswers.remove(existingAnswer);
        }

        // Add new comment answer
        formModel.questionAnswers.add(commentAnswerModel);
      });
    } catch (e) {
      debugPrint('Error saving single comment to database: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('QCOND: === BUILD METHOD CALLED ===');
    debugPrint(
        'QCOND: _isLoading=$_isLoading, _errorMessage=$_errorMessage, _form=${_form?.formName}');

    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          // Trigger refresh for form-related widgets when user navigates back
          context.read<FormRefreshCubit>().refresh();
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.lightGrey2,
        appBar: CustomAppBar(
          title: _form?.formName ?? 'Questions',
          actions: [
            IconButton(
              icon: const Icon(
                Icons.save_rounded,
                color: AppColors.primaryBlue,
              ),
              onPressed: _handleSave,
            ),
          ],
        ),
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    debugPrint(
        'QCOND: _buildBody called - _isLoading=$_isLoading, _errorMessage=$_errorMessage');

    if (_isLoading) {
      debugPrint('QCOND: Rendering loading indicator');
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryBlue,
        ),
      );
    }

    if (_errorMessage != null) {
      debugPrint('QCOND: Rendering error message: $_errorMessage');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const Gap(16),
            Text(
              'Error loading form',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const Gap(16),
            ElevatedButton(
              onPressed: _loadFormFromDatabase,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    debugPrint('QCOND: Rendering main content with visibleQuestions');
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Gap(8),
          visibleQuestions.isEmpty
              ? const EmptyQuestionsState()
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8.0),
                  itemCount: visibleQuestions.length,
                  itemBuilder: (context, index) {
                    final question = visibleQuestions[index];

                    if (question.isComment == true &&
                        question.questionId != null) {
                      final questionId = question.questionId!;
                      // Ensure controller and selected value exists, though initState should handle this
                      if (!_commentControllers.containsKey(questionId)) {
                        _commentControllers[questionId] =
                            TextEditingController();
                      }
                      if (!_selectedCommentTypes.containsKey(questionId)) {
                        _selectedCommentTypes[questionId] = null;
                      }
                      // Check if this particular CommentCard is validated
                      final bool isCommentValidated =
                          _isCommentValidated(question);

                      return CommentCard(
                        question: question,
                        selectedCommentType: _selectedCommentTypes[questionId],
                        commentController: _commentControllers[questionId]!,
                        onCommentTypeChanged: (String? newValue) {
                          setState(() {
                            _selectedCommentTypes[questionId] = newValue;
                            // Clear validation error when value changes
                            _commentValidationErrors[questionId] = null;
                          });
                          // Auto-save when comment becomes complete
                          _autoSaveCommentIfComplete(questionId);
                        },
                        errorText: _commentValidationErrors[questionId],
                        isCompleted: isCommentValidated,
                      );
                    } else {
                      final isMandatory = _isQuestionMandatory(question);
                      final cameraInfo = _getCameraIconInfo(question);
                      final hasPhoto = _hasPhotoUrl(question);

                      return FutureBuilder<Map<String, dynamic>>(
                        future: _calculateQuestionProgress(question),
                        builder: (context, snapshot) {
                          double progress = 0.0;
                          String progressText = '0 of 0';

                          if (snapshot.hasData) {
                            final progressData = snapshot.data!;
                            progress = progressData['progress'] as double;
                            progressText =
                                progressData['progressText'] as String;
                          } else if (snapshot.hasError) {
                            debugPrint(
                                'Error calculating progress: ${snapshot.error}');
                          }

                          return QuestionCard(
                            question: question,
                            progress: progress,
                            progressText: progressText,
                            isMandatory: isMandatory,
                            showCameraIcon: cameraInfo['show'] as bool,
                            isCameraMandatory:
                                cameraInfo['isMandatory'] as bool,
                            hasPhotoUrl: hasPhoto,
                            taskId: widget.taskId,
                            formId: _form?.formId,
                            onQuestionTap: () => _handleQuestionTap(question),
                            onPhotoSectionTap: () =>
                                _handleAddPhotosTap(question),
                            photoRefreshCounter: _photoRefreshCounter,
                            photoErrorText: question.questionId != null
                                ? _photoValidationErrors[question.questionId!]
                                : null,
                          );
                        },
                      );
                    }
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return const Gap(8);
                  },
                ),
          const Gap(8),
        ],
      ),
    );
  }
}
