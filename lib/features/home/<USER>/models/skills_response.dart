import 'dart:convert';
import '../../domain/entities/skills_response_entity.dart';

class SkillsResponse {
  final SkillsData? data;

  SkillsResponse({
    this.data,
  });

  factory SkillsResponse.fromRawJson(String str) =>
      SkillsResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SkillsResponse.fromJson(Map<String, dynamic> json) => SkillsResponse(
        data: json["data"] == null ? null : SkillsData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };

  SkillsResponseEntity toEntity() {
    return SkillsResponseEntity(
      skills: data?.skills.map((skill) => skill.toEntity()).toList() ?? [],
    );
  }
}

class SkillsData {
  final List<Skill> skills;

  SkillsData({
    required this.skills,
  });

  factory SkillsData.fromJson(Map<String, dynamic> json) => SkillsData(
        skills: json["skills"] == null
            ? []
            : List<Skill>.from(json["skills"].map((x) => Skill.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "skills": List<dynamic>.from(skills.map((x) => x.toJson())),
      };
}

class Skill {
  final int skillId;
  final String skillName;
  final String skillDescription;
  final bool has;
  final bool? isEdit;

  Skill({
    required this.skillId,
    required this.skillName,
    required this.skillDescription,
    required this.has,
    this.isEdit,
  });

  factory Skill.fromJson(Map<String, dynamic> json) => Skill(
        skillId: json["skill_id"] ?? 0,
        skillName: json["skill_name"] ?? "",
        skillDescription: json["skill_description"] ?? "",
        has: json["has"] ?? false,
        isEdit: json["is_edit"] ?? false,
      );

  Map<String, dynamic> toJson() => {
        "skill_id": skillId,
        "skill_name": skillName,
        "skill_description": skillDescription,
        "has": has,
        "is_edit": isEdit,
      };

  Skill copyWith({
    int? skillId,
    String? skillName,
    String? skillDescription,
    bool? has,
    bool? isEdit,
  }) {
    return Skill(
      skillId: skillId ?? this.skillId,
      skillName: skillName ?? this.skillName,
      skillDescription: skillDescription ?? this.skillDescription,
      has: has ?? this.has,
      isEdit: isEdit ?? this.isEdit,
    );
  }

  SkillEntity toEntity() {
    return SkillEntity(
      skillId: skillId,
      skillName: skillName,
      skillDescription: skillDescription,
      has: has,
    );
  }
}
