import 'dart:convert';

class HistoryResponse {
  final HistoryData? data;

  HistoryResponse({
    this.data,
  });

  factory HistoryResponse.fromRawJson(String str) =>
      HistoryResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory HistoryResponse.fromJson(Map<String, dynamic> json) =>
      HistoryResponse(
        data: json["data"] == null ? null : HistoryData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class HistoryData {
  final List<HistoryItem>? history;

  HistoryData({
    this.history,
  });

  factory HistoryData.fromRawJson(String str) =>
      HistoryData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory HistoryData.fromJson(Map<String, dynamic> json) => HistoryData(
        history: json["history"] == null
            ? []
            : List<HistoryItem>.from(
                json["history"]!.map((x) => HistoryItem.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "history": history == null
            ? []
            : List<dynamic>.from(history!.map((x) => x.toJson())),
      };
}

class HistoryItem {
  final int? storeId;
  final String? storeName;
  final String? clientName;
  final String? cycle;
  final int? budget;
  final int? minutes;
  final String? scheduledDate;
  final double? latitude;
  final double? longitude;
  final int? forms;
  final int? photos;
  final int? unacceptedReasonId;
  final String? unacceptedReason;

  HistoryItem({
    this.storeId,
    this.storeName,
    this.clientName,
    this.cycle,
    this.budget,
    this.minutes,
    this.scheduledDate,
    this.latitude,
    this.longitude,
    this.forms,
    this.photos,
    this.unacceptedReasonId,
    this.unacceptedReason,
  });

  factory HistoryItem.fromRawJson(String str) =>
      HistoryItem.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory HistoryItem.fromJson(Map<String, dynamic> json) => HistoryItem(
        storeId: json["store_id"],
        storeName: json["store_name"],
        clientName: json["client_name"],
        cycle: json["cycle"],
        budget: json["budget"],
        minutes: json["minutes"],
        scheduledDate: json["scheduled_date"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
        forms: json["forms"],
        photos: json["photos"],
        unacceptedReasonId: json["unaccepted_reason_id"],
        unacceptedReason: json["unaccepted_reason"],
      );

  Map<String, dynamic> toJson() => {
        "store_id": storeId,
        "store_name": storeName,
        "client_name": clientName,
        "cycle": cycle,
        "budget": budget,
        "minutes": minutes,
        "scheduled_date": scheduledDate,
        "latitude": latitude,
        "longitude": longitude,
        "forms": forms,
        "photos": photos,
        "unaccepted_reason_id": unacceptedReasonId,
        "unaccepted_reason": unacceptedReason,
      };
}
