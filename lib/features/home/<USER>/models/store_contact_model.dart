// Store Contact Data Models
class StoreContactResponse {
  final List<StoreContactData> data;
  final String? message;
  final bool success;

  StoreContactResponse({
    required this.data,
    this.message,
    required this.success,
  });

  factory StoreContactResponse.fromJson(Map<String, dynamic> json) {
    List<StoreContactData> contacts = [];

    if (json['data'] != null && json['data']['stores'] != null) {
      final stores = json['data']['stores'] as List;
      for (final store in stores) {
        if (store['employees'] != null) {
          final employees = store['employees'] as List;
          for (final employee in employees) {
            contacts.add(StoreContactData.fromJson(employee));
          }
        }
      }
    }

    return StoreContactResponse(
      data: contacts,
      message: json['message'],
      success: json['success'] ?? true,
    );
  }
}

class StoreContactData {
  final String? employeeId;
  final String? contactTypeId;
  final String? contactTypeName;
  final String firstName;
  final String lastName;
  final String jobTitle;
  final String email;
  final String phone;
  final String? storeId;

  StoreContactData({
    this.employeeId,
    this.contactTypeId,
    this.contactTypeName,
    required this.firstName,
    required this.lastName,
    required this.jobTitle,
    required this.email,
    required this.phone,
    this.storeId,
  });

  factory StoreContactData.fromJson(Map<String, dynamic> json) {
    return StoreContactData(
      employeeId: json['employee_id']?.toString(),
      contactTypeId: json['contact_type_id']?.toString(),
      contactTypeName: json['contact_type'] ?? json['contact_type_name'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      jobTitle: json['job_title'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      storeId: json['store_id']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (employeeId != null) 'employee_id': employeeId,
      if (contactTypeId != null) 'contact_type_id': contactTypeId,
      'first_name': firstName,
      'last_name': lastName,
      'job_title': jobTitle,
      'email': email,
      'phone': phone,
    };
  }

  // For the UI display
  String get fullName => '$firstName $lastName'.trim();
  String get displayName => fullName.isNotEmpty ? fullName : 'Unknown Contact';
}

class StoreContactRequest {
  final String token;
  final String userId;
  final String storeId;
  final List<StoreContactData> employees;

  StoreContactRequest({
    required this.token,
    required this.userId,
    required this.storeId,
    required this.employees,
  });

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'user_id': userId,
      'store_id': storeId,
      'employees': employees.map((e) => e.toJson()).toList(),
    };
  }
}

class ContactType {
  final String id;
  final String name;

  ContactType({
    required this.id,
    required this.name,
  });

  factory ContactType.fromJson(Map<String, dynamic> json) {
    // Debug: Print the incoming JSON
    print('ContactType.fromJson: $json');

    return ContactType(
      id: json['contactTypeID']?.toString() ??
          json['contact_type_id']?.toString() ??
          json['id']?.toString() ??
          json['typeId']?.toString() ??
          '',
      name: json['ContactTypeName']?.toString() ??
          json['contact_type_name']?.toString() ??
          json['name']?.toString() ??
          json['typeName']?.toString() ??
          json['type_name']?.toString() ??
          '',
    );
  }
}

class StoreContactTypesResponse {
  final List<ContactType> data;
  final String? message;
  final bool success;

  StoreContactTypesResponse({
    required this.data,
    this.message,
    required this.success,
  });

  factory StoreContactTypesResponse.fromJson(Map<String, dynamic> json) {
    List<ContactType> contactTypes = [];

    // Debug: Print the entire response
    print('StoreContactTypesResponse.fromJson: $json');

    // Handle the API response structure: { "data": { "storeContactTypes": [...] } }
    if (json['data'] != null) {
      if (json['data'] is Map<String, dynamic>) {
        final dataMap = json['data'] as Map<String, dynamic>;

        // Look for storeContactTypes array
        if (dataMap['storeContactTypes'] != null &&
            dataMap['storeContactTypes'] is List) {
          final types = dataMap['storeContactTypes'] as List;
          for (final item in types) {
            try {
              contactTypes.add(ContactType.fromJson(item));
            } catch (e) {
              print('Error parsing contact type: $e');
            }
          }
        }
        // Fallback to other possible keys
        else if (dataMap['contact_types'] != null) {
          final types = dataMap['contact_types'] as List;
          for (final item in types) {
            try {
              contactTypes.add(ContactType.fromJson(item));
            } catch (e) {
              print('Error parsing contact type: $e');
            }
          }
        }
      } else if (json['data'] is List) {
        // Direct list in data
        final dataList = json['data'] as List;
        for (final item in dataList) {
          try {
            contactTypes.add(ContactType.fromJson(item));
          } catch (e) {
            print('Error parsing contact type: $e');
          }
        }
      }
    }
    // Fallback: Direct contact_types array at root level
    else if (json['contact_types'] != null) {
      final types = json['contact_types'] as List;
      for (final item in types) {
        try {
          contactTypes.add(ContactType.fromJson(item));
        } catch (e) {
          print('Error parsing contact type: $e');
        }
      }
    }

    print('Parsed ${contactTypes.length} contact types');

    return StoreContactTypesResponse(
      data: contactTypes,
      message: json['message'],
      success: json['success'] ?? true,
    );
  }
}
