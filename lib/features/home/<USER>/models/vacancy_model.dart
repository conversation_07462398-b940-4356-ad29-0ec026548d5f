import 'dart:convert';

import 'package:storetrack_app/features/home/<USER>/entities/vacancy_entity.dart';

class VacancyModel extends VacancyEntity {
  const VacancyModel({
    required super.id,
    required super.jobTitle,
    required super.jobLocation,
    required super.jobDescription,
    super.companyName,
    super.salaryRange,
    super.employmentType,
    super.postedDate,
    super.canApply,
    super.canRefer,
  });

  factory VacancyModel.fromRawJson(String str) =>
      VacancyModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory VacancyModel.fromJson(Map<String, dynamic> json) => VacancyModel(
        id: json["id"] ?? 0,
        jobTitle: json["job_title"] ?? "",
        jobLocation: json["job_location"] ?? "",
        jobDescription: json["job_description"] ?? "",
        companyName: json["company_name"],
        salaryRange: json["salary_range"],
        employmentType: json["employment_type"],
        postedDate: json["posted_date"] != null
            ? DateTime.tryParse(json["posted_date"])
            : null,
        canApply: json["can_apply"] ?? true,
        canRefer: json["can_refer"] ?? true,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "job_title": jobTitle,
        "job_location": jobLocation,
        "job_description": jobDescription,
        "company_name": companyName,
        "salary_range": salaryRange,
        "employment_type": employmentType,
        "posted_date": postedDate?.toIso8601String(),
        "can_apply": canApply,
        "can_refer": canRefer,
      };

  VacancyEntity toEntity() => VacancyEntity(
        id: id,
        jobTitle: jobTitle,
        jobLocation: jobLocation,
        jobDescription: jobDescription,
        companyName: companyName,
        salaryRange: salaryRange,
        employmentType: employmentType,
        postedDate: postedDate,
        canApply: canApply,
        canRefer: canRefer,
      );
}
