import 'dart:convert';

class InductionResponse {
  final InductionData? data;

  InductionResponse({
    this.data,
  });

  factory InductionResponse.fromRawJson(String str) =>
      InductionResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory InductionResponse.fromJson(Map<String, dynamic> json) =>
      InductionResponse(
        data:
            json["data"] == null ? null : InductionData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class InductionData {
  final List<InductionItem>? inductions;

  InductionData({
    this.inductions,
  });

  factory InductionData.fromRawJson(String str) =>
      InductionData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory InductionData.fromJson(Map<String, dynamic> json) => InductionData(
        inductions: json["inductions"] == null
            ? []
            : List<InductionItem>.from(
                json["inductions"]!.map((x) => InductionItem.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "inductions": inductions == null
            ? []
            : List<dynamic>.from(inductions!.map((x) => x.toJson())),
      };
}

class InductionItem {
  final int? inductionId;
  final int? inductionCategoryId;
  final String? inductionName;
  final String? inductionCategory;
  final bool? has;
  final bool? isEdit;

  InductionItem({
    this.inductionId,
    this.inductionCategoryId,
    this.inductionName,
    this.inductionCategory,
    this.has,
    this.isEdit,
  });

  factory InductionItem.fromRawJson(String str) =>
      InductionItem.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory InductionItem.fromJson(Map<String, dynamic> json) => InductionItem(
        inductionId: json["induction_id"],
        inductionCategoryId: json["induction_category_id"],
        inductionName: json["induction_name"],
        inductionCategory: json["induction_category"],
        has: json["has"] ?? false,
        isEdit: json["is_edit"] ?? false,
      );

  Map<String, dynamic> toJson() => {
        "induction_id": inductionId,
        "induction_category_id": inductionCategoryId,
        "induction_name": inductionName,
        "induction_category": inductionCategory,
        "has": has,
        "is_edit": isEdit,
      };

  InductionItem copyWith({
    int? inductionId,
    int? inductionCategoryId,
    String? inductionName,
    String? inductionCategory,
    bool? has,
    bool? isEdit,
  }) {
    return InductionItem(
      inductionId: inductionId ?? this.inductionId,
      inductionCategoryId: inductionCategoryId ?? this.inductionCategoryId,
      inductionName: inductionName ?? this.inductionName,
      inductionCategory: inductionCategory ?? this.inductionCategory,
      has: has ?? this.has,
      isEdit: isEdit ?? this.isEdit,
    );
  }
}
