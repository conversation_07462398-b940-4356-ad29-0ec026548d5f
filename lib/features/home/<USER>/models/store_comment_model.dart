import '../../domain/entities/store_comment_entity.dart';

class StoreCommentsResponse {
  final List<StoreCommentData> comments;
  final String? message;
  final bool success;

  StoreCommentsResponse({
    required this.comments,
    this.message,
    required this.success,
  });

  factory StoreCommentsResponse.fromJson(Map<String, dynamic> json) {
    List<StoreCommentData> comments = [];

    if (json['data'] != null && json['data']['comments'] != null) {
      final commentsData = json['data']['comments'] as List;
      comments = commentsData
          .map((comment) => StoreCommentData.fromJson(comment))
          .toList();
    }

    return StoreCommentsResponse(
      comments: comments,
      message: json['message'],
      success: json['success'] ?? true,
    );
  }

  StoreCommentsResponseEntity toEntity() {
    return StoreCommentsResponseEntity(
      comments: comments.map((comment) => comment.toEntity()).toList(),
      message: message,
      success: success,
    );
  }
}

class StoreCommentData {
  final String? storeCommentId;
  final String? userName;
  final String? userProfileImageUrl;
  final String? comment;
  final String? operatorId;
  final String? dateCreated;

  StoreCommentData({
    this.storeCommentId,
    this.userName,
    this.userProfileImageUrl,
    this.comment,
    this.operatorId,
    this.dateCreated,
  });

  factory StoreCommentData.fromJson(Map<String, dynamic> json) {
    return StoreCommentData(
      storeCommentId: json['store_comment_id']?.toString(),
      userName: json['user_name'],
      userProfileImageUrl: json['user_profile_image_url'],
      comment: json['comment'],
      operatorId: json['operatorId']?.toString(),
      dateCreated: json['dateCreated'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (storeCommentId != null) 'store_comment_id': storeCommentId,
      if (userName != null) 'user_name': userName,
      if (userProfileImageUrl != null)
        'user_profile_image_url': userProfileImageUrl,
      if (comment != null) 'comment': comment,
      if (operatorId != null) 'operatorId': operatorId,
      if (dateCreated != null) 'dateCreated': dateCreated,
    };
  }

  StoreCommentEntity toEntity() {
    return StoreCommentEntity(
      storeCommentId: storeCommentId,
      userName: userName,
      userProfileImageUrl: userProfileImageUrl,
      comment: comment,
      operatorId: operatorId,
      dateCreated: dateCreated,
    );
  }
}

class StoreCommentRequest {
  final String taskId;
  final String storeCommentId;
  final String token;
  final String comment;
  final String userId;

  StoreCommentRequest({
    required this.taskId,
    required this.storeCommentId,
    required this.token,
    required this.comment,
    required this.userId,
  });

  Map<String, dynamic> toJson() {
    return {
      'task_id': taskId,
      'store_comment_id': storeCommentId,
      'token': token,
      'comment': comment,
      'user_id': userId,
    };
  }

  factory StoreCommentRequest.fromEntity(StoreCommentRequestEntity entity) {
    return StoreCommentRequest(
      taskId: entity.taskId,
      storeCommentId: entity.storeCommentId,
      token: entity.token,
      comment: entity.comment,
      userId: entity.userId,
    );
  }
}
