// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'calendar_info_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class CalendarInfoModel extends _CalendarInfoModel
    with RealmEntity, RealmObjectBase, RealmObject {
  static var _defaultsSet = false;

  CalendarInfoModel(
    int id, {
    DateTime? timestamp,
    bool? dollarSymbol,
    bool? publicHoliday,
    double? budgetAmount,
  }) {
    if (!_defaultsSet) {
      _defaultsSet = RealmObjectBase.setDefaults<CalendarInfoModel>({
        'id': 0,
      });
    }
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'timestamp', timestamp);
    RealmObjectBase.set(this, 'dollarSymbol', dollarSymbol);
    RealmObjectBase.set(this, 'publicHoliday', publicHoliday);
    RealmObjectBase.set(this, 'budgetAmount', budgetAmount);
  }

  CalendarInfoModel._();

  @override
  int get id => RealmObjectBase.get<int>(this, 'id') as int;
  @override
  set id(int value) => RealmObjectBase.set(this, 'id', value);

  @override
  DateTime? get timestamp =>
      RealmObjectBase.get<DateTime>(this, 'timestamp') as DateTime?;
  @override
  set timestamp(DateTime? value) =>
      RealmObjectBase.set(this, 'timestamp', value);

  @override
  bool? get dollarSymbol =>
      RealmObjectBase.get<bool>(this, 'dollarSymbol') as bool?;
  @override
  set dollarSymbol(bool? value) =>
      RealmObjectBase.set(this, 'dollarSymbol', value);

  @override
  bool? get publicHoliday =>
      RealmObjectBase.get<bool>(this, 'publicHoliday') as bool?;
  @override
  set publicHoliday(bool? value) =>
      RealmObjectBase.set(this, 'publicHoliday', value);

  @override
  double? get budgetAmount =>
      RealmObjectBase.get<double>(this, 'budgetAmount') as double?;
  @override
  set budgetAmount(double? value) =>
      RealmObjectBase.set(this, 'budgetAmount', value);

  @override
  Stream<RealmObjectChanges<CalendarInfoModel>> get changes =>
      RealmObjectBase.getChanges<CalendarInfoModel>(this);

  @override
  Stream<RealmObjectChanges<CalendarInfoModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<CalendarInfoModel>(this, keyPaths);

  @override
  CalendarInfoModel freeze() =>
      RealmObjectBase.freezeObject<CalendarInfoModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'timestamp': timestamp.toEJson(),
      'dollarSymbol': dollarSymbol.toEJson(),
      'publicHoliday': publicHoliday.toEJson(),
      'budgetAmount': budgetAmount.toEJson(),
    };
  }

  static EJsonValue _toEJson(CalendarInfoModel value) => value.toEJson();
  static CalendarInfoModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        CalendarInfoModel(
          fromEJson(ejson['id'], defaultValue: 0),
          timestamp: fromEJson(ejson['timestamp']),
          dollarSymbol: fromEJson(ejson['dollarSymbol']),
          publicHoliday: fromEJson(ejson['publicHoliday']),
          budgetAmount: fromEJson(ejson['budgetAmount']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(CalendarInfoModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, CalendarInfoModel, 'CalendarInfoModel', [
      SchemaProperty('id', RealmPropertyType.int, primaryKey: true),
      SchemaProperty('timestamp', RealmPropertyType.timestamp, optional: true),
      SchemaProperty('dollarSymbol', RealmPropertyType.bool, optional: true),
      SchemaProperty('publicHoliday', RealmPropertyType.bool, optional: true),
      SchemaProperty('budgetAmount', RealmPropertyType.double, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
