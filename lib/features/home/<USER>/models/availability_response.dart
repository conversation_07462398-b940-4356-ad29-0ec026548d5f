import 'dart:convert';
import '../../domain/entities/availability_response_entity.dart';

class AvailabilityResponse {
  final AvailabilityData? data;

  AvailabilityResponse({
    this.data,
  });

  factory AvailabilityResponse.fromRawJson(String str) =>
      AvailabilityResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AvailabilityResponse.fromJson(Map<String, dynamic> json) =>
      AvailabilityResponse(
        data: json["data"] == null
            ? null
            : AvailabilityData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };

  AvailabilityResponseEntity toEntity() {
    return AvailabilityResponseEntity(
      days: data?.days.map((day) => day.toEntity()).toList() ?? [],
    );
  }
}

class AvailabilityData {
  final List<DayAvailability> days;

  AvailabilityData({
    required this.days,
  });

  factory AvailabilityData.fromJson(Map<String, dynamic> json) =>
      AvailabilityData(
        days: json["days"] == null
            ? []
            : List<DayAvailability>.from(
                json["days"].map((x) => DayAvailability.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "days": List<dynamic>.from(days.map((x) => x.toJson())),
      };
}

class DayAvailability {
  final int dayNumber;
  final int dayOrder;
  final String dayDescription;
  final List<DaySpan> daySpans;

  DayAvailability({
    required this.dayNumber,
    required this.dayOrder,
    required this.dayDescription,
    required this.daySpans,
  });

  factory DayAvailability.fromJson(Map<String, dynamic> json) =>
      DayAvailability(
        dayNumber: json["day_number"] ?? 0,
        dayOrder: json["day_order"] ?? 0,
        dayDescription: json["day_description"] ?? "",
        daySpans: json["day_spans"] == null
            ? []
            : List<DaySpan>.from(
                json["day_spans"].map((x) => DaySpan.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "day_number": dayNumber,
        "day_order": dayOrder,
        "day_description": dayDescription,
        "day_spans": List<dynamic>.from(daySpans.map((x) => x.toJson())),
      };

  DayAvailabilityEntity toEntity() {
    return DayAvailabilityEntity(
      dayNumber: dayNumber,
      dayOrder: dayOrder,
      dayDescription: dayDescription,
      daySpans: daySpans.map((span) => span.toEntity()).toList(),
    );
  }
}

class DaySpan {
  final int dayEntryNumber;
  final String startHour;
  final String endHour;

  DaySpan({
    required this.dayEntryNumber,
    required this.startHour,
    required this.endHour,
  });

  factory DaySpan.fromJson(Map<String, dynamic> json) => DaySpan(
        dayEntryNumber: json["day_entry_number"] ?? 0,
        startHour: json["start_hour"] ?? "",
        endHour: json["end_hour"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "day_entry_number": dayEntryNumber,
        "start_hour": startHour,
        "end_hour": endHour,
      };

  DaySpanEntity toEntity() {
    return DaySpanEntity(
      dayEntryNumber: dayEntryNumber,
      startHour: startHour,
      endHour: endHour,
    );
  }
}
