import 'dart:convert';

class UpdateProfileRequest {
  final String userId;
  final String token;
  final String address;
  final String countryId;
  final String country;
  final String stateId;
  final String state;
  final String suburb;
  final String postcode;
  final String pAddress;
  final String pCountryId;
  final String pCountry;
  final String pSuburb;
  final String pPostcode;
  final String pRegion;
  final String pDeliveryComment;
  final String mobile;
  final double deviceLatitude;
  final double deviceLongitude;

  UpdateProfileRequest({
    required this.userId,
    required this.token,
    required this.address,
    required this.countryId,
    required this.country,
    required this.stateId,
    required this.state,
    required this.suburb,
    required this.postcode,
    required this.pAddress,
    required this.pCountryId,
    required this.pCountry,
    required this.pSuburb,
    required this.pPostcode,
    required this.pRegion,
    required this.pDeliveryComment,
    required this.mobile,
    required this.deviceLatitude,
    required this.deviceLongitude,
  });

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "token": token,
        "address": address,
        "country_id": countryId,
        "country": country,
        "state_id": stateId,
        "state": state,
        "suburb": suburb,
        "postcode": postcode,
        "P_Address": pAddress,
        "P_Country_id": pCountryId,
        "P_Country": pCountry,
        "P_Suburb": pSuburb,
        "P_Postcode": pPostcode,
        "P_Region": pRegion,
        "P_Delivery_Comment": pDeliveryComment,
        "mobile": mobile,
        "device_latitude": deviceLatitude,
        "device_longitude": deviceLongitude,
      };

  String toRawJson() => json.encode(toJson());

  factory UpdateProfileRequest.fromJson(Map<String, dynamic> json) =>
      UpdateProfileRequest(
        userId: json["user_id"],
        token: json["token"],
        address: json["address"],
        countryId: json["country_id"],
        country: json["country"],
        stateId: json["state_id"],
        state: json["state"],
        suburb: json["suburb"],
        postcode: json["postcode"],
        pAddress: json["P_Address"],
        pCountryId: json["P_Country_id"],
        pCountry: json["P_Country"],
        pSuburb: json["P_Suburb"],
        pPostcode: json["P_Postcode"],
        pRegion: json["P_Region"],
        pDeliveryComment: json["P_Delivery_Comment"],
        mobile: json["mobile"],
        deviceLatitude: json["device_latitude"]?.toDouble(),
        deviceLongitude: json["device_longitude"]?.toDouble(),
      );
}
