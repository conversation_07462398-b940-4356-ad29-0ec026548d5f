import 'dart:convert';

class LeaveResponse {
  final LeaveData? data;

  LeaveResponse({
    this.data,
  });

  factory LeaveResponse.fromRawJson(String str) =>
      LeaveResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LeaveResponse.fromJson(Map<String, dynamic> json) => LeaveResponse(
        data: json["data"] == null ? null : LeaveData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class LeaveData {
  final List<Leave> leaves;

  LeaveData({
    required this.leaves,
  });

  factory LeaveData.fromJson(Map<String, dynamic> json) => LeaveData(
        leaves: json["leaves"] == null
            ? []
            : List<Leave>.from(json["leaves"].map((x) => Leave.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "leaves": List<dynamic>.from(leaves.map((x) => x.toJson())),
      };
}

class Leave {
  final int? leaveId;
  final String? leaveDate;
  final String? leaveTitle;
  final String? leaveDescription;
  final bool? isSelected;

  Leave({
    this.leaveId,
    this.leaveDate,
    this.leaveTitle,
    this.leaveDescription,
    this.isSelected,
  });

  factory Leave.fromRawJson(String str) => Leave.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Leave.fromJson(Map<String, dynamic> json) => Leave(
        leaveId: json["leave_id"],
        leaveDate: json["leave_date"],
        leaveTitle: json["leave_title"],
        leaveDescription: json["leave_description"],
        isSelected: json["is_selected"] ?? false,
      );

  Map<String, dynamic> toJson() => {
        "leave_id": leaveId,
        "leave_date": leaveDate,
        "leave_title": leaveTitle,
        "leave_description": leaveDescription,
        "is_selected": isSelected,
      };

  Leave copyWith({
    int? leaveId,
    String? leaveDate,
    String? leaveTitle,
    String? leaveDescription,
    bool? isSelected,
  }) {
    return Leave(
      leaveId: leaveId ?? this.leaveId,
      leaveDate: leaveDate ?? this.leaveDate,
      leaveTitle: leaveTitle ?? this.leaveTitle,
      leaveDescription: leaveDescription ?? this.leaveDescription,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}
