// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'store_history_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class PreviousTaskModel extends _PreviousTaskModel
    with RealmEntity, RealmObjectBase, RealmObject {
  static var _defaultsSet = false;

  PreviousTaskModel(
    int id, {
    bool isSynced = false,
    int schedulePersonId = 0,
    String cycleName = '',
    String completedBy = '',
    DateTime? dateScheduled,
    String taskComment = '',
  }) {
    if (!_defaultsSet) {
      _defaultsSet = RealmObjectBase.setDefaults<PreviousTaskModel>({
        'id': 0,
        'isSynced': false,
        'schedulePersonId': 0,
        'cycleName': '',
        'completedBy': '',
        'taskComment': '',
      });
    }
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'isSynced', isSynced);
    RealmObjectBase.set(this, 'schedulePersonId', schedulePersonId);
    RealmObjectBase.set(this, 'cycleName', cycleName);
    RealmObjectBase.set(this, 'completedBy', completedBy);
    RealmObjectBase.set(this, 'dateScheduled', dateScheduled);
    RealmObjectBase.set(this, 'taskComment', taskComment);
  }

  PreviousTaskModel._();

  @override
  int get id => RealmObjectBase.get<int>(this, 'id') as int;
  @override
  set id(int value) => RealmObjectBase.set(this, 'id', value);

  @override
  bool get isSynced => RealmObjectBase.get<bool>(this, 'isSynced') as bool;
  @override
  set isSynced(bool value) => RealmObjectBase.set(this, 'isSynced', value);

  @override
  int get schedulePersonId =>
      RealmObjectBase.get<int>(this, 'schedulePersonId') as int;
  @override
  set schedulePersonId(int value) =>
      RealmObjectBase.set(this, 'schedulePersonId', value);

  @override
  String get cycleName =>
      RealmObjectBase.get<String>(this, 'cycleName') as String;
  @override
  set cycleName(String value) => RealmObjectBase.set(this, 'cycleName', value);

  @override
  String get completedBy =>
      RealmObjectBase.get<String>(this, 'completedBy') as String;
  @override
  set completedBy(String value) =>
      RealmObjectBase.set(this, 'completedBy', value);

  @override
  DateTime? get dateScheduled =>
      RealmObjectBase.get<DateTime>(this, 'dateScheduled') as DateTime?;
  @override
  set dateScheduled(DateTime? value) =>
      RealmObjectBase.set(this, 'dateScheduled', value);

  @override
  String get taskComment =>
      RealmObjectBase.get<String>(this, 'taskComment') as String;
  @override
  set taskComment(String value) =>
      RealmObjectBase.set(this, 'taskComment', value);

  @override
  Stream<RealmObjectChanges<PreviousTaskModel>> get changes =>
      RealmObjectBase.getChanges<PreviousTaskModel>(this);

  @override
  Stream<RealmObjectChanges<PreviousTaskModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<PreviousTaskModel>(this, keyPaths);

  @override
  PreviousTaskModel freeze() =>
      RealmObjectBase.freezeObject<PreviousTaskModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'isSynced': isSynced.toEJson(),
      'schedulePersonId': schedulePersonId.toEJson(),
      'cycleName': cycleName.toEJson(),
      'completedBy': completedBy.toEJson(),
      'dateScheduled': dateScheduled.toEJson(),
      'taskComment': taskComment.toEJson(),
    };
  }

  static EJsonValue _toEJson(PreviousTaskModel value) => value.toEJson();
  static PreviousTaskModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        PreviousTaskModel(
          fromEJson(ejson['id'], defaultValue: 0),
          isSynced: fromEJson(ejson['isSynced'], defaultValue: false),
          schedulePersonId:
              fromEJson(ejson['schedulePersonId'], defaultValue: 0),
          cycleName: fromEJson(ejson['cycleName'], defaultValue: ''),
          completedBy: fromEJson(ejson['completedBy'], defaultValue: ''),
          dateScheduled: fromEJson(ejson['dateScheduled']),
          taskComment: fromEJson(ejson['taskComment'], defaultValue: ''),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(PreviousTaskModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, PreviousTaskModel, 'PreviousTaskModel', [
      SchemaProperty('id', RealmPropertyType.int, primaryKey: true),
      SchemaProperty('isSynced', RealmPropertyType.bool),
      SchemaProperty('schedulePersonId', RealmPropertyType.int),
      SchemaProperty('cycleName', RealmPropertyType.string),
      SchemaProperty('completedBy', RealmPropertyType.string),
      SchemaProperty('dateScheduled', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('taskComment', RealmPropertyType.string),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
