import 'dart:convert';

class UsefulLinksResponse {
  final UsefulLinksData? data;

  UsefulLinksResponse({
    this.data,
  });

  factory UsefulLinksResponse.fromRawJson(String str) =>
      UsefulLinksResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UsefulLinksResponse.fromJson(Map<String, dynamic> json) =>
      UsefulLinksResponse(
        data: json["data"] == null
            ? null
            : UsefulLinksData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class UsefulLinksData {
  final List<UsefulLink>? usefulLinks;

  UsefulLinksData({
    this.usefulLinks,
  });

  factory UsefulLinksData.fromRawJson(String str) =>
      UsefulLinksData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UsefulLinksData.fromJson(Map<String, dynamic> json) =>
      UsefulLinksData(
        usefulLinks: json["usefulLinks"] == null
            ? []
            : List<UsefulLink>.from(
                json["usefulLinks"]!.map((x) => UsefulLink.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "usefulLinks": usefulLinks == null
            ? []
            : List<dynamic>.from(usefulLinks!.map((x) => x.toJson())),
      };
}

class UsefulLink {
  final String? linkTitle;
  final String? linkUrl;

  UsefulLink({
    this.linkTitle,
    this.linkUrl,
  });

  factory UsefulLink.fromRawJson(String str) =>
      UsefulLink.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UsefulLink.fromJson(Map<String, dynamic> json) => UsefulLink(
        linkTitle: json["link_title"],
        linkUrl: json["link_url"],
      );

  Map<String, dynamic> toJson() => {
        "link_title": linkTitle,
        "link_url": linkUrl,
      };
}
