import 'dart:convert';

import 'package:storetrack_app/features/home/<USER>/models/vacancy_model.dart';

class VacanciesResponse {
  final bool success;
  final String? message;
  final List<VacancyModel> data;

  VacanciesResponse({
    required this.success,
    this.message,
    required this.data,
  });

  factory VacanciesResponse.fromRawJson(String str) =>
      VacanciesResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory VacanciesResponse.fromJson(Map<String, dynamic> json) =>
      VacanciesResponse(
        success: json["success"] ?? false,
        message: json["message"],
        data: json["data"] != null
            ? List<VacancyModel>.from(
                json["data"].map((x) => VacancyModel.fromJson(x)))
            : [],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
      };
}
