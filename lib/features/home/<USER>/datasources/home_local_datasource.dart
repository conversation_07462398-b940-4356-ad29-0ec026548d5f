import 'package:storetrack_app/features/home/<USER>/models/calendar_info_model.dart';

import '../../../../core/database/realm_database.dart';
import '../../domain/entities/tasks_response_entity.dart';
import '../../domain/entities/calendar_response_entity.dart';
import '../models/task_detail_model.dart';
import '../mappers/task_detail_mapper.dart';
import '../mappers/calendar_info_mapper.dart';

abstract class HomeLocalDataSource {
  Future<void> saveTasks(TasksResponseEntity response);
  Future<TasksResponseEntity?> getTasks();
  Future<TaskDetail?> getTaskDetail(int taskId);
  Future<void> saveCalendarInfo(CalendarResponseEntity response);
  Future<CalendarResponseEntity?> getCalendarInfo();
}

class HomeLocalDataSourceImpl implements HomeLocalDataSource {
  final RealmDatabase realmDatabase;

  HomeLocalDataSourceImpl({required this.realmDatabase});

  @override
  Future<void> saveTasks(TasksResponseEntity response) async {
    final realm = realmDatabase.realm;

    // Clear existing tasks
    realm.write(() {
      realm.deleteAll<TaskDetailModel>();
    });

    // Save new tasks
    if (response.addTasks != null) {
      realm.write(() {
        int id = 1;
        for (final task in response.addTasks!) {
          final taskModel = TaskDetailMapper.toModel(task, id++);
          realm.add(taskModel);
        }
      });
    }
  }

  @override
  Future<TasksResponseEntity?> getTasks() async {
    final realm = realmDatabase.realm;

    final taskModels = realm.all<TaskDetailModel>();

    if (taskModels.isEmpty) {
      return null;
    }

    // Convert models back to entities
    final taskEntities =
        taskModels.map((model) => TaskDetailMapper.toEntity(model)).toList();

    return TasksResponseEntity(addTasks: taskEntities);
  }

  @override
  Future<TaskDetail?> getTaskDetail(int taskId) async {
    final realm = realmDatabase.realm;

    // Find the task with the matching taskId
    final taskModel =
        realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

    if (taskModel == null) {
      return null;
    }

    // Convert model back to entity
    return TaskDetailMapper.toEntity(taskModel);
  }

  @override
  Future<void> saveCalendarInfo(CalendarResponseEntity response) async {
    final realm = realmDatabase.realm;

    // Clear existing calendar info
    realm.write(() {
      realm.deleteAll<CalendarInfoModel>();
    });

    // Save new calendar info
    if (response.data?.calendarInfo != null) {
      realm.write(() {
        int id = 1;
        for (var calendarInfo in response.data!.calendarInfo!) {
          final calendarInfoModel =
              CalendarInfoMapper.toModel(calendarInfo, id++);
          realm.add(calendarInfoModel);
        }
      });
    }
  }

  @override
  Future<CalendarResponseEntity?> getCalendarInfo() async {
    final realm = realmDatabase.realm;

    final calendarInfoModels = realm.all<CalendarInfoModel>();

    if (calendarInfoModels.isEmpty) {
      return null;
    }

    // Convert models back to entities
    final calendarInfoEntities = calendarInfoModels
        .map((model) => CalendarInfoMapper.toEntity(model))
        .toList();

    final data = Data(calendarInfo: calendarInfoEntities);

    return CalendarResponseEntity(data: data);
  }
}
