import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

typedef MeasurementId = int;

/// Pure Dart class for validating measurement values
/// Centralizes all validation logic for different measurement types
class MeasurementValidator {
  /// Validate a measurement value with all applicable validations
  /// Returns null if valid, error message if invalid
  String? validate(
    Measurement measurement,
    dynamic value, {
    List<String> photos = const [],
    bool isQuizForm = false,
  }) {
    if (measurement.measurementId == null) return null;

    // 1. Standard field validation (required, range, regex)
    final standardError = _validateStandardRules(measurement, value, photos);
    if (standardError != null) return standardError;

    // 2. Photo validation
    final photoError = _validatePhotos(measurement, photos);
    if (photoError != null) return photoError;

    // 3. Quiz validation (if applicable)
    if (isQuizForm) {
      final quizError = _validateQuizAnswers(measurement, value);
      if (quizError != null) return quizError;
    }

    return null;
  }

  /// Check if a value is considered empty based on measurement type
  bool isEmpty(Measurement measurement, dynamic value) {
    final measurementTypeId = measurement.measurementTypeId;

    switch (measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
        return value == null || value.toString().trim().isEmpty;
      case 3: // Checkbox
        return value == null;
      case 4: // Dropdown
      case 5: // Dropdown
      case 9: // Date picker
        return value == null;
      case 6: // Multi-select
        return value == null || (value is List && value.isEmpty);
      case 7: // Counter
        return value == null;
      default:
        return value == null;
    }
  }

  /// Check if a measurement is required based on its validations
  bool isRequired(Measurement measurement) {
    // Check direct required field
    if (measurement.required == true) return true;

    final validations = measurement.measurementValidations;
    if (validations == null) return false;

    for (final validation in validations) {
      if (validation.required == true) {
        return true;
      }
    }
    return false;
  }

  /// Validate standard measurement rules (required, range, regex)
  String? _validateStandardRules(
      Measurement measurement, dynamic value, List<String> photos) {
    // Check direct required field first
    if (measurement.required == true && isEmpty(measurement, value)) {
      final description = measurement.measurementDescription ?? 'This field';
      return '$description is required';
    }

    // Check direct rangeValidation field
    if (measurement.rangeValidation != null &&
        measurement.rangeValidation!.isNotEmpty &&
        !isEmpty(measurement, value)) {
      final numValue = _parseToNumber(value);
      if (numValue != null) {
        if (!_isWithinRangeSimple(numValue, measurement.rangeValidation!)) {
          return 'Value must be at least ${_getMinFromRange(measurement.rangeValidation!)} and at most ${_getMaxFromRange(measurement.rangeValidation!)}';
        }
      }
    }

    // Check photo requirements using mandatoryPhototypesCount
    if ((measurement.mandatoryPhototypesCount ?? 0) > 0 &&
        photos.length < (measurement.mandatoryPhototypesCount?.toInt() ?? 0)) {
      return 'At least ${measurement.mandatoryPhototypesCount} photo is required';
    }

    final validations = measurement.measurementValidations;
    if (validations == null) return null;

    for (final validation in validations) {
      // Validation Type 1: Required validation
      if (validation.validationTypeId == 1 && validation.required == true) {
        if (isEmpty(measurement, value)) {
          return validation.errorMessage ?? 'This field is required';
        }
      }

      // Validation Type 2: Range validation
      if (validation.validationTypeId == 2 &&
          validation.rangeValidation != null &&
          validation.rangeValidation!.isNotEmpty) {
        // Parse value to number for range validation
        final numValue = _parseToNumber(value);
        if (!isEmpty(measurement, value) && numValue != null) {
          if (!_isWithinRange(numValue, validation.rangeValidation!)) {
            return validation.errorMessage ??
                'Value is not within the valid range';
          }
        }
      }

      // Validation Type 3: Expression validation (regex)
      if (validation.validationTypeId == 3 &&
          validation.expressionValidation != null &&
          validation.expressionValidation!.isNotEmpty) {
        if (!isEmpty(measurement, value)) {
          if (!_matchesExpression(value, validation.expressionValidation!)) {
            return validation.errorMessage ??
                'Value does not match the required format';
          }
        }
      }
    }

    return null;
  }

  /// Validate photo requirements
  String? _validatePhotos(Measurement measurement, List<String> photos) {
    // Photo validation will be handled by the repository layer
    // as it needs access to PhotoTagsThree data
    // This method is kept for interface consistency
    return null;
  }

  /// Validate quiz answers for quiz forms
  String? _validateQuizAnswers(Measurement measurement, dynamic value) {
    // Only validate dropdown and multi-select widgets
    if (![4, 5, 6].contains(measurement.measurementTypeId)) return null;

    // Skip validation if value is empty (handled by required validation)
    if (isEmpty(measurement, value)) return null;

    if ([4, 5].contains(measurement.measurementTypeId)) {
      // Dropdown validation: Check if selected answer is correct
      return _validateDropdownQuizAnswer(measurement, value);
    } else if (measurement.measurementTypeId == 6) {
      // Multi-select validation: Check if all correct answers are selected
      return _validateMultiSelectQuizAnswer(measurement, value);
    }

    return null;
  }

  /// Validate dropdown quiz answer (types 4 and 5)
  String? _validateDropdownQuizAnswer(Measurement measurement, dynamic value) {
    final options = measurement.measurementOptions;
    if (options == null) return null;

    try {
      // Find the selected option
      final selectedOption = options.firstWhere(
        (option) => option.measurementOptionDescription == value,
      );

      // Check if the selected answer is correct
      if (selectedOption.isAnswer != true) {
        return 'The answer you selected is incorrect';
      }
    } catch (e) {
      // Option not found
      return 'The answer you selected is incorrect';
    }

    return null;
  }

  /// Validate multi-select quiz answer (type 6)
  String? _validateMultiSelectQuizAnswer(
      Measurement measurement, dynamic value) {
    final options = measurement.measurementOptions;
    if (options == null || value is! List) return null;

    // Get all correct answers (where is_answer is true)
    final correctOptions =
        options.where((option) => option.isAnswer == true).toList();

    // Get all incorrect answers (where is_answer is false)
    final incorrectOptions =
        options.where((option) => option.isAnswer == false).toList();

    if (correctOptions.isEmpty) return null;

    // Convert selected values (option IDs as strings) to integers for comparison
    final selectedOptionIds = <int>[];
    for (final selectedValue in value) {
      final optionId = int.tryParse(selectedValue.toString());
      if (optionId != null) {
        selectedOptionIds.add(optionId);
      }
    }

    // Check if all correct answers are selected
    for (final correctOption in correctOptions) {
      if (!selectedOptionIds
          .contains(correctOption.measurementOptionId?.toInt())) {
        return 'Some of the answers are incorrect';
      }
    }

    // Check if any incorrect answers are selected
    for (final incorrectOption in incorrectOptions) {
      if (selectedOptionIds
          .contains(incorrectOption.measurementOptionId?.toInt())) {
        return 'Some of the answers are incorrect';
      }
    }

    return null;
  }

  /// Parse value to number for range validation
  double? _parseToNumber(dynamic value) {
    if (value == null) return null;
    return double.tryParse(value.toString());
  }

  /// Validate range for numeric values
  bool _isWithinRange(double value, String rangeValidation) {
    try {
      final ranges = rangeValidation.split('|');
      if (ranges.length < 2) return false;

      final min = double.tryParse(ranges[0].trim());
      final max = double.tryParse(ranges[1].trim());

      if (min != null && max != null) {
        return value >= min && value <= max;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Validate range for simple format like "1,10"
  bool _isWithinRangeSimple(double value, String rangeValidation) {
    try {
      final ranges = rangeValidation.split(',');
      if (ranges.length < 2) return false;

      final min = double.tryParse(ranges[0].trim());
      final max = double.tryParse(ranges[1].trim());

      if (min != null && max != null) {
        return value >= min && value <= max;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Get minimum value from range validation string
  double? _getMinFromRange(String rangeValidation) {
    try {
      final ranges = rangeValidation.split(',');
      if (ranges.isNotEmpty) {
        return double.tryParse(ranges[0].trim());
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Get maximum value from range validation string
  double? _getMaxFromRange(String rangeValidation) {
    try {
      final ranges = rangeValidation.split(',');
      if (ranges.length >= 2) {
        return double.tryParse(ranges[1].trim());
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Validate expression using regex
  bool _matchesExpression(dynamic value, String expressionValidation) {
    try {
      final regex = RegExp(expressionValidation);
      return regex.hasMatch(value.toString());
    } catch (e) {
      return false;
    }
  }
}
