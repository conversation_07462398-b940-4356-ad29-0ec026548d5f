import 'package:equatable/equatable.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

abstract class TaskDetailsState extends Equatable {
  const TaskDetailsState();

  @override
  List<Object?> get props => [];
}

class TaskDetailsInitial extends TaskDetailsState {}

class TaskDetailsLoading extends TaskDetailsState {}

class TaskDetailsSuccess extends TaskDetailsState {
  final TaskDetail taskDetail;

  const TaskDetailsSuccess(this.taskDetail);

  @override
  List<Object?> get props => [taskDetail];
}

class TaskDetailsError extends TaskDetailsState {
  final String message;

  const TaskDetailsError(this.message);

  @override
  List<Object?> get props => [message];
}
