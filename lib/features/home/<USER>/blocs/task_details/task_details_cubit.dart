import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_task_detail_usecase.dart';

import 'task_details_state.dart';

class TaskDetailsCubit extends Cubit<TaskDetailsState> {
  final GetTaskDetailUseCase _getTaskDetailUseCase;

  TaskDetailsCubit(this._getTaskDetailUseCase) : super(TaskDetailsInitial());

  Future<void> getTaskDetail(int taskId) async {
    emit(TaskDetailsLoading());

    try {
      final result = await _getTaskDetailUseCase(taskId);

      if (result.isSuccess && result.data != null) {
        emit(TaskDetailsSuccess(result.data!));
      } else {
        final errorMessage =
            result.error?.toString() ?? 'Failed to load task details';
        logger('TaskDetailsCubit: Error loading task $taskId: $errorMessage');
        emit(TaskDetailsError(errorMessage));
      }
    } catch (e) {
      final errorMessage = 'Unexpected error: ${e.toString()}';
      logger('TaskDetailsCubit: Exception loading task $taskId: $errorMessage');
      emit(TaskDetailsError(errorMessage));
    }
  }

  void reset() {
    emit(TaskDetailsInitial());
  }
}
