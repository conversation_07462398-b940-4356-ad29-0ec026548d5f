import 'package:equatable/equatable.dart';

abstract class FormRefreshState extends Equatable {
  const FormRefreshState();

  @override
  List<Object> get props => [];
}

class FormRefreshInitial extends FormRefreshState {}

class RefreshForm extends FormRefreshState {
  // Adding timestamp to ensure each refresh emission is unique
  final DateTime timestamp;

  const RefreshForm(this.timestamp);

  @override
  List<Object> get props => [timestamp];
}
