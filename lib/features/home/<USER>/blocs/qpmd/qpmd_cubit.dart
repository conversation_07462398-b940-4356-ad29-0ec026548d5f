import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/question_answer_repository.dart';
import 'package:storetrack_app/core/services/photo_service.dart';
import 'package:storetrack_app/features/home/<USER>/engines/conditional_logic_engine.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/validators/measurement_validator.dart';

import 'qpmd_state.dart';

class QPMDCubit extends Cubit<QPMDState> {
  final MeasurementValidator _validator;
  final QuestionAnswerRepository _repository;
  final PhotoService _photoService;
  final ConditionalLogicEngine _conditionalEngine;

  // Page parameters
  Question? _question;
  QuestionPart? _questionPart;
  int? _taskId;
  int? _formId;
  bool _isQuizForm = false;

  QPMDCubit({
    required MeasurementValidator validator,
    required QuestionAnswerRepository repository,
    required PhotoService photoService,
    required ConditionalLogicEngine conditionalEngine,
  })  : _validator = validator,
        _repository = repository,
        _photoService = photoService,
        _conditionalEngine = conditionalEngine,
        super(QPMDInitial());

  /// Initialize the cubit with page parameters
  Future<void> initialize({
    Question? question,
    QuestionPart? questionPart,
    int? taskId,
    int? formId,
  }) async {
    if (question == null || taskId == null || formId == null) {
      emit(const QPMDError('Missing required parameters'));
      return;
    }

    _question = question;
    _questionPart = questionPart;
    _taskId = taskId;
    _formId = formId;

    emit(QPMDLoading());

    try {
      // Check if this is a quiz form
      _isQuizForm = await _repository.isQuizForm(
        taskId: taskId,
        formId: formId,
      );

      // Initialize measurement values and visibility
      final measurements = question.measurements ?? [];
      final values = <int, dynamic>{};
      final errors = <int, String?>{};
      final photos = <int, List<String>>{};

      // Initialize default values based on measurement type
      for (final measurement in measurements) {
        final id = measurement.measurementId?.toInt();
        if (id == null) continue;

        values[id] = _getDefaultValue(measurement);
        errors[id] = null;
        photos[id] = [];
      }

      // Get initial visibility
      final visibility = _conditionalEngine.getInitialVisibility(measurements);

      // Load saved data
      await _loadSavedData(values, photos);

      // Load saved photos
      await _loadSavedPhotos(photos);

      // Process initial conditional logic
      _processInitialConditionalLogic(values, visibility);

      // Calculate completion status
      final isCompleted =
          _calculateCompletionStatus(values, errors, visibility);

      emit(QPMDLoaded(
        values: values,
        errors: errors,
        visibility: visibility,
        photos: photos,
        isCompleted: isCompleted,
      ));
    } catch (e) {
      logger('Error initializing QPMD: $e');
      emit(QPMDError('Failed to initialize: $e'));
    }
  }

  /// Update a measurement value
  Future<void> updateValue(int measurementId, dynamic value) async {
    final currentState = state;
    if (currentState is! QPMDLoaded) return;

    final newValues = Map<int, dynamic>.from(currentState.values);
    final newErrors = Map<int, String?>.from(currentState.errors);
    final newVisibility = Map<int, bool>.from(currentState.visibility);

    // Update the value
    newValues[measurementId] = value;
    newErrors[measurementId] = null; // Clear error

    // Find the measurement
    final measurement = _findMeasurementById(measurementId);
    if (measurement != null) {
      // Process conditional logic if applicable
      if (_conditionalEngine.canTriggerConditionalLogic(measurement)) {
        final updatedVisibility = _conditionalEngine.processConditionalLogic(
          measurements: _question?.measurements ?? [],
          currentValues: newValues,
          currentVisibility: newVisibility,
          changedMeasurementId: measurementId,
          newValue: value,
          isMll: _question?.isMll ?? false,
        );

        // Clear values for hidden widgets
        _clearHiddenWidgetValues(
          newValues,
          newErrors,
          currentState.visibility,
          updatedVisibility,
        );

        newVisibility.addAll(updatedVisibility);
      }
    }

    // Calculate completion status
    final isCompleted =
        _calculateCompletionStatus(newValues, newErrors, newVisibility);

    emit(currentState.copyWith(
      values: newValues,
      errors: newErrors,
      visibility: newVisibility,
      isCompleted: isCompleted,
    ));

    // Auto-save if valid
    _autoSaveField(measurementId, value);
  }

  /// Add a photo for a measurement
  Future<void> addPhoto(int measurementId, String photoUrl) async {
    final currentState = state;
    if (currentState is! QPMDLoaded) return;

    final newPhotos = Map<int, List<String>>.from(currentState.photos);
    newPhotos[measurementId] = [...(newPhotos[measurementId] ?? []), photoUrl];

    emit(currentState.copyWith(photos: newPhotos));
  }

  /// Remove a photo for a measurement
  Future<void> removePhoto(int measurementId, String photoUrl) async {
    final currentState = state;
    if (currentState is! QPMDLoaded) return;

    final newPhotos = Map<int, List<String>>.from(currentState.photos);
    newPhotos[measurementId] = (newPhotos[measurementId] ?? [])
        .where((photo) => photo != photoUrl)
        .toList();

    emit(currentState.copyWith(photos: newPhotos));
  }

  /// Save all data
  Future<void> save() async {
    final currentState = state;
    if (currentState is! QPMDLoaded) return;

    // Validate all measurements
    final validationResult = _validateAll(
      currentState.values,
      currentState.errors,
      currentState.visibility,
      currentState.photos,
    );

    if (validationResult.hasErrors) {
      emit(currentState.copyWith(errors: validationResult.errors));
      return;
    }

    emit(QPMDSaving(
      values: currentState.values,
      errors: currentState.errors,
      visibility: currentState.visibility,
      photos: currentState.photos,
    ));

    try {
      final questionAnswers = _generateQuestionAnswers(
        currentState.values,
        currentState.visibility,
      );

      final success = await _repository.upsert(
        questionAnswers: questionAnswers,
        taskId: _taskId!,
        formId: _formId!,
        questionId: _question!.questionId!.toInt(),
        questionPartId: _questionPart?.questionpartId?.toInt(),
        questionPartMultiId: _questionPart?.questionpartMultiId,
      );

      if (success) {
        emit(const QPMDSaveSuccess('Answers saved successfully!'));
      } else {
        emit(const QPMDError('Failed to save answers. Please try again.'));
      }
    } catch (e) {
      logger('Error saving data: $e');
      emit(QPMDError('Error saving data: $e'));
    }
  }

  /// Auto-save on navigation
  Future<void> autoSaveOnNavigation() async {
    final currentState = state;
    if (currentState is! QPMDLoaded) return;

    try {
      final validAnswers = _generateValidQuestionAnswers(
        currentState.values,
        currentState.visibility,
        currentState.photos,
      );

      if (validAnswers.isNotEmpty) {
        await _repository.upsert(
          questionAnswers: validAnswers,
          taskId: _taskId!,
          formId: _formId!,
          questionId: _question!.questionId!.toInt(),
          questionPartId: _questionPart?.questionpartId?.toInt(),
          questionPartMultiId: _questionPart?.questionpartMultiId,
        );
        logger('Auto-saved ${validAnswers.length} valid answers on navigation');
      }
    } catch (e) {
      logger('Error during auto-save on navigation: $e');
    }
  }

  /// Get completion progress
  Map<String, int> getProgress() {
    final currentState = state;
    if (currentState is! QPMDLoaded) return {'completed': 0, 'total': 0};

    int completed = 0;
    int total = 0;

    for (final measurement in _question?.measurements ?? []) {
      final id = measurement.measurementId?.toInt();
      if (id == null) continue;

      final isVisible = currentState.visibility[id] ?? true;
      if (!isVisible) continue;

      total++;

      final value = currentState.values[id];
      final photos = currentState.photos[id] ?? [];

      // Check if measurement is completed (has valid value and passes validation)
      if (!_validator.isEmpty(measurement, value)) {
        final error = _validator.validate(
          measurement,
          value,
          photos: photos,
          isQuizForm: _isQuizForm,
        );
        if (error == null) {
          completed++;
        }
      }
    }

    return {'completed': completed, 'total': total};
  }

  /// Get default value for a measurement type
  dynamic _getDefaultValue(Measurement measurement) {
    switch (measurement.measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
        return '';
      case 3: // Checkbox
        return false;
      case 4: // Dropdown
      case 5: // Dropdown
      case 8: // Radio button
      case 9: // Date picker
        return null;
      case 6: // Multi-select
        return <String>[];
      case 7: // Counter
        return 0;
      default:
        return null;
    }
  }

  /// Load saved data from repository
  Future<void> _loadSavedData(
    Map<int, dynamic> values,
    Map<int, List<String>> photos,
  ) async {
    if (_taskId == null || _formId == null || _question?.questionId == null) {
      return;
    }

    try {
      final savedAnswers = await _repository.fetch(
        taskId: _taskId!,
        formId: _formId!,
        questionId: _question!.questionId!.toInt(),
        questionPartId: _questionPart?.questionpartId?.toInt(),
        questionPartMultiId: _questionPart?.questionpartMultiId,
      );

      for (final answer in savedAnswers) {
        final measurementId = answer.measurementId?.toInt();
        if (measurementId == null) continue;

        final measurement = _findMeasurementById(measurementId);
        if (measurement == null) continue;

        final restoredValue =
            _restoreValueFromQuestionAnswer(answer, measurement);
        if (restoredValue != null) {
          values[measurementId] = restoredValue;
          logger(
              'Restored value for measurement $measurementId: $restoredValue');
        }
      }
    } catch (e) {
      logger('Error loading saved data: $e');
    }
  }

  /// Load saved photos
  Future<void> _loadSavedPhotos(Map<int, List<String>> photos) async {
    if (_taskId == null || _question?.questionId == null) return;

    try {
      final savedPhotos = await _photoService.getPhotosFromTask(
        taskId: _taskId!,
        folderId: null,
      );

      final filteredPhotos = savedPhotos.where((photo) {
        return photo.questionId == _question!.questionId!.toInt() &&
            photo.questionpartId == _questionPart?.questionpartId?.toInt() &&
            photo.combineTypeId == 3; // Level 3 for photo_tags_three
      }).toList();

      for (final photo in filteredPhotos) {
        final measurementId = photo.measurementId?.toInt();
        if (measurementId != null && photo.photoUrl != null) {
          photos[measurementId] = [
            ...(photos[measurementId] ?? []),
            photo.photoUrl!
          ];
        }
      }

      logger('Loaded ${filteredPhotos.length} photos for QPMD page');
    } catch (e) {
      logger('Error loading saved photos: $e');
    }
  }

  /// Process initial conditional logic
  void _processInitialConditionalLogic(
    Map<int, dynamic> values,
    Map<int, bool> visibility,
  ) {
    final measurements = _question?.measurements ?? [];

    for (final measurement in measurements) {
      final id = measurement.measurementId?.toInt();
      if (id == null) continue;

      if (_conditionalEngine.canTriggerConditionalLogic(measurement)) {
        final value = values[id];
        if (value != null && !_validator.isEmpty(measurement, value)) {
          final updatedVisibility = _conditionalEngine.processConditionalLogic(
            measurements: measurements,
            currentValues: values,
            currentVisibility: visibility,
            changedMeasurementId: id,
            newValue: value,
            isMll: _question?.isMll ?? false,
            isInitialization: true,
          );
          visibility.addAll(updatedVisibility);
        }
      }
    }
  }

  /// Auto-save a single field if valid
  Future<void> _autoSaveField(int measurementId, dynamic value) async {
    final measurement = _findMeasurementById(measurementId);
    if (measurement == null) return;

    // Skip auto-save if value is empty
    if (_validator.isEmpty(measurement, value)) return;

    final currentState = state;
    if (currentState is! QPMDLoaded) return;

    final photos = currentState.photos[measurementId] ?? [];
    final error = _validator.validate(
      measurement,
      value,
      photos: photos,
      isQuizForm: _isQuizForm,
    );

    // Only auto-save if validation passes
    if (error == null) {
      try {
        final questionAnswer =
            _generateSingleQuestionAnswer(measurement, value);
        if (questionAnswer != null) {
          await _repository.upsert(
            questionAnswers: [questionAnswer],
            taskId: _taskId!,
            formId: _formId!,
            questionId: _question!.questionId!.toInt(),
            questionPartId: _questionPart?.questionpartId?.toInt(),
            questionPartMultiId: _questionPart?.questionpartMultiId,
          );
          logger('Auto-saved answer for measurement $measurementId: $value');
        }
      } catch (e) {
        logger('Error auto-saving field: $e');
      }
    }
  }

  /// Generate a single QuestionAnswer for a measurement
  QuestionAnswer? _generateSingleQuestionAnswer(
    Measurement measurement,
    dynamic value,
  ) {
    final measurementId = measurement.measurementId?.toInt();
    if (measurementId == null) return null;

    final questionAnswer = QuestionAnswer(
      taskId: _taskId,
      flip: _question?.flip,
      formId: _formId,
      questionId: _question?.questionId,
      isComment: false,
      commentTypeId: null,
      questionpartId: _questionPart?.questionpartId,
      questionPartMultiId: _questionPart?.questionpartMultiId,
      measurementId: measurementId,
      measurementTypeId: measurement.measurementTypeId,
    );

    _setQuestionAnswerValues(questionAnswer, measurement, value);
    return questionAnswer;
  }

  /// Generate all QuestionAnswers from current values
  List<QuestionAnswer> _generateQuestionAnswers(
    Map<int, dynamic> values,
    Map<int, bool> visibility,
  ) {
    final questionAnswers = <QuestionAnswer>[];
    final measurements = _question?.measurements ?? [];

    for (final measurement in measurements) {
      final id = measurement.measurementId?.toInt();
      if (id == null) continue;

      final isVisible = visibility[id] ?? true;
      if (!isVisible) continue;

      final value = values[id];
      final questionAnswer = _generateSingleQuestionAnswer(measurement, value);
      if (questionAnswer != null) {
        questionAnswers.add(questionAnswer);
      }
    }

    return questionAnswers;
  }

  /// Generate only valid QuestionAnswers for auto-save
  List<QuestionAnswer> _generateValidQuestionAnswers(
    Map<int, dynamic> values,
    Map<int, bool> visibility,
    Map<int, List<String>> photos,
  ) {
    final validAnswers = <QuestionAnswer>[];
    final measurements = _question?.measurements ?? [];

    for (final measurement in measurements) {
      final id = measurement.measurementId?.toInt();
      if (id == null) continue;

      final isVisible = visibility[id] ?? true;
      if (!isVisible) continue;

      final value = values[id];
      if (_validator.isEmpty(measurement, value)) continue;

      final measurementPhotos = photos[id] ?? [];
      final error = _validator.validate(
        measurement,
        value,
        photos: measurementPhotos,
        isQuizForm: _isQuizForm,
      );

      if (error == null) {
        final questionAnswer =
            _generateSingleQuestionAnswer(measurement, value);
        if (questionAnswer != null) {
          validAnswers.add(questionAnswer);
        }
      }
    }

    return validAnswers;
  }

  /// Set QuestionAnswer values based on measurement type
  void _setQuestionAnswerValues(
    QuestionAnswer questionAnswer,
    Measurement measurement,
    dynamic value,
  ) {
    switch (measurement.measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
      case 7: // Counter
      case 9: // Date picker
        questionAnswer.measurementOptionId = null;
        questionAnswer.measurementOptionIds = null;
        questionAnswer.measurementTextResult =
            _validator.isEmpty(measurement, value) ? null : value.toString();
        break;

      case 3: // Checkbox
        questionAnswer.measurementOptionId = value == true ? 1 : 2;
        questionAnswer.measurementOptionIds = null;
        questionAnswer.measurementTextResult = value == true ? '1' : '2';
        break;

      case 4: // Dropdown
      case 5: // Dropdown
        if (value != null && measurement.measurementOptions != null) {
          try {
            final selectedOption = measurement.measurementOptions!.firstWhere(
              (option) => option.measurementOptionDescription == value,
            );
            questionAnswer.measurementOptionId =
                selectedOption.measurementOptionId;
            questionAnswer.measurementOptionIds = null;
            questionAnswer.measurementTextResult = value.toString();
          } catch (e) {
            questionAnswer.measurementOptionId = null;
            questionAnswer.measurementOptionIds = null;
            questionAnswer.measurementTextResult = null;
          }
        } else {
          questionAnswer.measurementOptionId = null;
          questionAnswer.measurementOptionIds = null;
          questionAnswer.measurementTextResult = null;
        }
        break;

      case 6: // Multi-select
        if (value != null &&
            value is List<String> &&
            measurement.measurementOptions != null) {
          final optionIds = value
              .map((id) => int.tryParse(id))
              .where((id) => id != null)
              .cast<int>()
              .toList();

          final selectedDescriptions = <String>[];
          for (final optionId in optionIds) {
            try {
              final option = measurement.measurementOptions!.firstWhere(
                (opt) => opt.measurementOptionId == optionId,
              );
              if (option.measurementOptionDescription != null) {
                selectedDescriptions.add(option.measurementOptionDescription!);
              }
            } catch (e) {
              // Option not found, skip
            }
          }

          questionAnswer.measurementOptionId = null;
          questionAnswer.measurementOptionIds = optionIds.join(',');
          questionAnswer.measurementTextResult = selectedDescriptions.join('|');
        } else {
          questionAnswer.measurementOptionId = null;
          questionAnswer.measurementOptionIds = null;
          questionAnswer.measurementTextResult = null;
        }
        break;

      default:
        // Handle unknown measurement types
        questionAnswer.measurementOptionId = null;
        questionAnswer.measurementOptionIds = null;
        questionAnswer.measurementTextResult =
            _validator.isEmpty(measurement, value) ? null : value?.toString();
        break;
    }
  }

  /// Restore value from QuestionAnswer based on measurement type
  dynamic _restoreValueFromQuestionAnswer(
    QuestionAnswer qa,
    Measurement measurement,
  ) {
    switch (measurement.measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
        return qa.measurementTextResult ?? '';

      case 3: // Checkbox
        return qa.measurementOptionId != null && qa.measurementOptionId! > 0;

      case 4: // Dropdown
      case 5: // Dropdown
        if (qa.measurementOptionId != null &&
            measurement.measurementOptions != null) {
          try {
            final option = measurement.measurementOptions!.firstWhere(
              (opt) => opt.measurementOptionId == qa.measurementOptionId,
            );
            return option.measurementOptionDescription;
          } catch (e) {
            return null;
          }
        }
        return null;

      case 6: // Multi-select
        if (qa.measurementOptionIds != null &&
            qa.measurementOptionIds!.isNotEmpty) {
          try {
            return qa.measurementOptionIds!
                .split(',')
                .map((id) => id.trim())
                .where((id) => id.isNotEmpty)
                .toList();
          } catch (e) {
            return <String>[];
          }
        }
        return <String>[];

      case 7: // Counter
        if (qa.measurementTextResult != null) {
          return int.tryParse(qa.measurementTextResult!) ?? 0;
        }
        return 0;

      case 9: // Date picker
        return qa.measurementTextResult;

      default:
        return qa.measurementTextResult;
    }
  }

  /// Find measurement by ID
  Measurement? _findMeasurementById(int measurementId) {
    final measurements = _question?.measurements ?? [];
    try {
      return measurements.firstWhere(
        (measurement) => measurement.measurementId == measurementId,
      );
    } catch (e) {
      return null;
    }
  }

  /// Validate all measurements
  ValidationResult _validateAll(
    Map<int, dynamic> values,
    Map<int, String?> currentErrors,
    Map<int, bool> visibility,
    Map<int, List<String>> photos,
  ) {
    final errors = Map<int, String?>.from(currentErrors);
    bool hasErrors = false;

    final measurements = _question?.measurements ?? [];

    for (final measurement in measurements) {
      final id = measurement.measurementId?.toInt();
      if (id == null) continue;

      final isVisible = visibility[id] ?? true;
      if (!isVisible) continue;

      final value = values[id];
      final measurementPhotos = photos[id] ?? [];

      final error = _validator.validate(
        measurement,
        value,
        photos: measurementPhotos,
        isQuizForm: _isQuizForm,
      );

      if (error != null) {
        errors[id] = error;
        hasErrors = true;
      } else {
        errors[id] = null;
      }
    }

    return ValidationResult(errors: errors, hasErrors: hasErrors);
  }

  /// Calculate completion status
  bool _calculateCompletionStatus(
    Map<int, dynamic> values,
    Map<int, String?> errors,
    Map<int, bool> visibility,
  ) {
    final progress = getProgress();
    return progress['total']! > 0 &&
        progress['completed']! == progress['total']!;
  }

  /// Clear values for widgets that became hidden
  void _clearHiddenWidgetValues(
    Map<int, dynamic> values,
    Map<int, String?> errors,
    Map<int, bool> oldVisibility,
    Map<int, bool> newVisibility,
  ) {
    for (final entry in newVisibility.entries) {
      final id = entry.key;
      final newVisible = entry.value;
      final oldVisible = oldVisibility[id] ?? true;

      // If widget became hidden, clear its value and error
      if (oldVisible && !newVisible) {
        final measurement = _findMeasurementById(id);
        if (measurement != null) {
          values[id] = _getDefaultValue(measurement);
          errors[id] = null;
        }
      }
    }
  }
}

/// Result of validation
class ValidationResult {
  final Map<int, String?> errors;
  final bool hasErrors;

  ValidationResult({required this.errors, required this.hasErrors});
}
