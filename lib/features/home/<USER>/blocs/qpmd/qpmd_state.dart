import 'package:equatable/equatable.dart';

/// State for QPMD page
abstract class QPMDState extends Equatable {
  const QPMDState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class QPMDInitial extends QPMDState {}

/// Loading state
class QPMDLoading extends Q<PERSON>DState {}

/// Loaded state with all data
class QPMDLoaded extends QPMDState {
  final Map<int, dynamic> values;
  final Map<int, String?> errors;
  final Map<int, bool> visibility;
  final Map<int, List<String>> photos;
  final bool isAutoSaving;
  final bool isSaving;
  final bool isCompleted;

  const QPMDLoaded({
    required this.values,
    required this.errors,
    required this.visibility,
    required this.photos,
    this.isAutoSaving = false,
    this.isSaving = false,
    this.isCompleted = false,
  });

  QPMDLoaded copyWith({
    Map<int, dynamic>? values,
    Map<int, String?>? errors,
    Map<int, bool>? visibility,
    Map<int, List<String>>? photos,
    bool? isAutoSaving,
    bool? isSaving,
    bool? isCompleted,
  }) {
    return QPMDLoaded(
      values: values ?? this.values,
      errors: errors ?? this.errors,
      visibility: visibility ?? this.visibility,
      photos: photos ?? this.photos,
      isAutoSaving: isAutoSaving ?? this.isAutoSaving,
      isSaving: isSaving ?? this.isSaving,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  @override
  List<Object?> get props => [
        values,
        errors,
        visibility,
        photos,
        isAutoSaving,
        isSaving,
        isCompleted,
      ];
}

/// Auto-save in progress state
class QPMDAutoSaving extends QPMDLoaded {
  const QPMDAutoSaving({
    required super.values,
    required super.errors,
    required super.visibility,
    required super.photos,
  }) : super(isAutoSaving: true);
}

/// Saving state
class QPMDSaving extends QPMDLoaded {
  const QPMDSaving({
    required super.values,
    required super.errors,
    required super.visibility,
    required super.photos,
  }) : super(isSaving: true);
}

/// Save success state
class QPMDSaveSuccess extends QPMDState {
  final String message;

  const QPMDSaveSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// Error state
class QPMDError extends QPMDState {
  final String message;

  const QPMDError(this.message);

  @override
  List<Object?> get props => [message];
}
