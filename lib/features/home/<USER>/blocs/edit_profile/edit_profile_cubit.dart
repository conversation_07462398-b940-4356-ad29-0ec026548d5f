import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../data/models/profile_response.dart';
import '../../../data/models/update_profile_request.dart';
import '../../../domain/usecases/get_profile_usecase.dart';
import '../../../domain/usecases/update_profile_usecase.dart';

part 'edit_profile_state.dart';

class EditProfileCubit extends Cubit<EditProfileState> {
  final GetProfileUseCase _getProfileUseCase;
  final UpdateProfileUseCase _updateProfileUseCase;

  EditProfileCubit(
    this._getProfileUseCase,
    this._updateProfileUseCase,
  ) : super(EditProfileInitial());

  Future<void> loadProfile({
    required String token,
    required String userId,
  }) async {
    emit(EditProfileLoading());

    final result = await _getProfileUseCase.call(
      token: token,
      userId: userId,
    );

    if (result.isSuccess && result.data != null) {
      emit(EditProfileLoaded(result.data!));
    } else {
      emit(EditProfileError(result.error ?? 'Unknown error occurred'));
    }
  }

  Future<void> updateProfile({
    required UpdateProfileRequest request,
  }) async {
    emit(EditProfileUpdating());

    final result = await _updateProfileUseCase.call(request: request);

    if (result.isSuccess && result.data != null) {
      emit(EditProfileUpdateSuccess(result.data!));
    } else {
      emit(EditProfileError(result.error ?? 'Failed to update profile'));
    }
  }

  void resetState() {
    emit(EditProfileInitial());
  }
}
