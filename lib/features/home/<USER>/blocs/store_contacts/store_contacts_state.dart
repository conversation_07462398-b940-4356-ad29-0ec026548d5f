part of 'store_contacts_cubit.dart';

abstract class StoreContactsState extends Equatable {
  const StoreContactsState();

  @override
  List<Object?> get props => [];
}

class StoreContactsLoading extends StoreContactsState {}

class StoreContactsLoaded extends StoreContactsState {
  final StoreContactResponse response;

  const StoreContactsLoaded(this.response);

  @override
  List<Object?> get props => [response];
}

class StoreContactsSaving extends StoreContactsState {
  final StoreContactResponse response;

  const StoreContactsSaving(this.response);

  @override
  List<Object?> get props => [response];
}

class StoreContactsSaved extends StoreContactsState {
  final StoreContactResponse response;

  const StoreContactsSaved(this.response);

  @override
  List<Object?> get props => [response];
}

class StoreContactsError extends StoreContactsState {
  final String message;

  const StoreContactsError(this.message);

  @override
  List<Object?> get props => [message];
}

// Store Contact Types States
class StoreContactTypesLoading extends StoreContactsState {}

class StoreContactTypesLoaded extends StoreContactsState {
  final StoreContactTypesResponse response;

  const StoreContactTypesLoaded(this.response);

  @override
  List<Object?> get props => [response];
}

class StoreContactTypesError extends StoreContactsState {
  final String message;

  const StoreContactTypesError(this.message);

  @override
  List<Object?> get props => [message];
}
