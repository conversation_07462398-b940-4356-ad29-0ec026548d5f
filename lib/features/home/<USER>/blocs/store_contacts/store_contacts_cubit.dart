import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/models/store_contact_model.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_store_contacts_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/save_store_contact_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_store_contact_types_usecase.dart';

part 'store_contacts_state.dart';

class StoreContactsCubit extends Cubit<StoreContactsState> {
  final GetStoreContactsUseCase getStoreContactsUseCase;
  final SaveStoreContactUseCase saveStoreContactUseCase;
  final GetStoreContactTypesUseCase getStoreContactTypesUseCase;

  StoreContactsCubit({
    required this.getStoreContactsUseCase,
    required this.saveStoreContactUseCase,
    required this.getStoreContactTypesUseCase,
  }) : super(StoreContactsLoading());

  Future<void> fetchStoreContacts({required String storeId}) async {
    emit(StoreContactsLoading());
    try {
      final dataManager = sl<DataManager>();
      final authToken = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (authToken != null && userId != null) {
        final result = await getStoreContactsUseCase.call(
          token: authToken,
          userId: userId,
          storeId: storeId,
        );

        if (result.isSuccess && result.data != null) {
          emit(StoreContactsLoaded(result.data!));
        } else {
          emit(StoreContactsError(
              result.error ?? 'Failed to fetch store contacts'));
        }
      } else {
        emit(const StoreContactsError('Authentication required'));
      }
    } catch (e) {
      emit(StoreContactsError(e.toString()));
    }
  }

  Future<void> saveStoreContact({
    required String storeId,
    required StoreContactData contact,
  }) async {
    emit(StoreContactsSaving(StoreContactResponse(data: [], success: false)));

    try {
      print('🟡 Getting auth token and user ID...');
      final dataManager = sl<DataManager>();
      final authToken = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (authToken != null && userId != null) {
        final request = StoreContactRequest(
          token: authToken,
          userId: userId,
          storeId: storeId,
          employees: [contact],
        );

        final result = await saveStoreContactUseCase.call(request: request);

        if (result.isSuccess) {
          print('🟢 Save successful, emitting StoreContactsSaved');
          emit(StoreContactsSaved(
              StoreContactResponse(data: [contact], success: true)));
        } else {
          print('🔴 Save failed, emitting StoreContactsError');
          emit(StoreContactsError(
              result.error ?? 'Failed to save store contact'));
        }
      } else {
        print('🔴 Authentication data missing');
        emit(const StoreContactsError('Authentication required'));
      }
    } catch (e) {
      print('🔴 Exception in saveStoreContact: $e');
      emit(StoreContactsError(e.toString()));
    }
  }

  Future<void> deleteStoreContact({
    required String storeId,
    required String contactId,
  }) async {
    final currentState = state;
    if (currentState is StoreContactsLoaded) {
      emit(StoreContactsSaving(currentState.response));

      try {
        // For now, just remove from local list until delete API is implemented
        final updatedContacts = currentState.response.data
            .where((contact) => contact.employeeId != contactId)
            .toList();

        final updatedResponse = StoreContactResponse(
          data: updatedContacts,
          success: true,
        );

        emit(StoreContactsLoaded(updatedResponse));
        emit(StoreContactsSaved(updatedResponse));
      } catch (e) {
        emit(StoreContactsError(e.toString()));
      }
    }
  }

  void addContact(StoreContactData contact) {
    final currentState = state;
    if (currentState is StoreContactsLoaded) {
      final updatedContacts = [...currentState.response.data, contact];

      final updatedResponse = StoreContactResponse(
        data: updatedContacts,
        success: true,
      );

      emit(StoreContactsLoaded(updatedResponse));
    }
  }

  void updateContact(int index, StoreContactData contact) {
    final currentState = state;
    if (currentState is StoreContactsLoaded) {
      final updatedContacts = [...currentState.response.data];
      if (index >= 0 && index < updatedContacts.length) {
        updatedContacts[index] = contact;

        final updatedResponse = StoreContactResponse(
          data: updatedContacts,
          success: true,
        );

        emit(StoreContactsLoaded(updatedResponse));
      }
    }
  }

  Future<void> fetchStoreContactTypes({required String storeId}) async {
    emit(StoreContactTypesLoading());
    try {
      final dataManager = sl<DataManager>();
      final authToken = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (authToken != null && userId != null) {
        final result = await getStoreContactTypesUseCase.call(
          token: authToken,
          userId: userId,
          storeId: storeId,
        );

        if (result.isSuccess && result.data != null) {
          emit(StoreContactTypesLoaded(result.data!));
        } else {
          emit(StoreContactTypesError(
              result.error ?? 'Failed to fetch store contact types'));
        }
      } else {
        emit(const StoreContactTypesError('Authentication required'));
      }
    } catch (e) {
      emit(StoreContactTypesError(e.toString()));
    }
  }
}
