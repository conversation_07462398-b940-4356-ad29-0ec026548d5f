import 'package:equatable/equatable.dart';

import '../../data/models/notification_response.dart';

abstract class NotificationState extends Equatable {
  const NotificationState();

  @override
  List<Object> get props => [];
}

class NotificationInitial extends NotificationState {}

class NotificationLoading extends NotificationState {}

class NotificationLoaded extends NotificationState {
  final NotificationResponse notificationResponse;

  const NotificationLoaded(this.notificationResponse);

  @override
  List<Object> get props => [notificationResponse];
}

class NotificationError extends NotificationState {
  final String message;

  const NotificationError(this.message);

  @override
  List<Object> get props => [message];
}
