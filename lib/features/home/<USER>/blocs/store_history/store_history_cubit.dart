import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/previous_tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_previous_tasks_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_previous_tasks_optimize_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import 'store_history_state.dart';

class StoreHistoryCubit extends Cubit<StoreHistoryState> {
  final GetPreviousTasksUseCase _getPreviousTasksUseCase;
  final GetPreviousTasksOptimizeUseCase _getPreviousTasksOptimizeUseCase;

  StoreHistoryCubit(
    this._getPreviousTasksUseCase,
    this._getPreviousTasksOptimizeUseCase,
  ) : super(StoreHistoryInitial());

  Future<void> fetchPreviousTasks(PreviousTasksRequestEntity request) async {
    emit(StoreHistoryLoading());

    try {
      final Result<PreviousTasksResponseEntity> result =
          await _getPreviousTasksOptimizeUseCase(request);

      if (result.isSuccess && result.data != null) {
        final previousTasks = result.data!.data?.previousTask ?? [];
        emit(StoreHistoryLoaded(previousTasks: previousTasks));
      } else {
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while fetching optimized previous tasks.';
        emit(StoreHistoryError(message: errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in StoreHistoryCubit: $e");
      emit(const StoreHistoryError(
          message: 'An unexpected error occurred. Please try again.'));
    }
  }

  Future<void> fetchPreviousTasksRegular(
      PreviousTasksRequestEntity request) async {
    emit(StoreHistoryLoading());

    try {
      final Result<PreviousTasksResponseEntity> result =
          await _getPreviousTasksUseCase(request);

      if (result.isSuccess && result.data != null) {
        final previousTasks = result.data!.data?.previousTask ?? [];
        emit(StoreHistoryLoaded(previousTasks: previousTasks));
      } else {
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while fetching previous tasks.';
        emit(StoreHistoryError(message: errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in StoreHistoryCubit: $e");
      emit(const StoreHistoryError(
          message: 'An unexpected error occurred. Please try again.'));
    }
  }
}
