import 'package:equatable/equatable.dart';
import '../../../domain/entities/previous_tasks_response_entity.dart';

abstract class StoreHistoryState extends Equatable {
  const StoreHistoryState();

  @override
  List<Object?> get props => [];
}

class StoreHistoryInitial extends StoreHistoryState {}

class StoreHistoryLoading extends StoreHistoryState {}

class StoreHistoryLoaded extends StoreHistoryState {
  final List<PreviousTaskEntity> previousTasks;

  const StoreHistoryLoaded({required this.previousTasks});

  @override
  List<Object?> get props => [previousTasks];
}

class StoreHistoryError extends StoreHistoryState {
  final String message;

  const StoreHistoryError({required this.message});

  @override
  List<Object?> get props => [message];
}
