import 'package:bloc/bloc.dart';
import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_calendar_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/submit_report_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import '../../../domain/entities/tasks_request_entity.dart';
import '../../../domain/entities/tasks_response_entity.dart';
import '../../../domain/usecases/get_tasks_usecase.dart';
import 'unschedule_state.dart';

class UnscheduleTaskCubit extends Cubit<UnscheduleTaskState> {
  final GetTasksUseCase _getTasksUseCase;
  final GetCalendarUseCase _getCalendarUseCase;
  final SubmitReportUseCase _submitReportUseCase;

  UnscheduleTaskCubit(
    this._getTasksUseCase,
    this._getCalendarUseCase,
    this._submitReportUseCase,
  ) : super(UnscheduleTaskInitial());

  Future<void> getData(TasksRequestEntity request) async {
    emit(UnscheduleTaskLoading());

    final results = await Future.wait([
      _getTasksUseCase(request),
      _getCalendarUseCase(GetCalendarParams(
        token: request.token,
        userId: request.userId,
      )),
    ]);

    final operationNames = ['Tasks', 'Calendar'];

    if (results.hasFailures) {
      final errorMessage = results.getCombinedErrorMessage(operationNames);
      emit(UnscheduleTaskError(errorMessage));
      return;
    }

    // All succeeded, extract data
    emit(UnscheduleTaskSuccess(
      tasksResponse: results[0].data as TasksResponseEntity,
      calendarResponse: results[1].data as CalendarResponseEntity,
    ));
  }

  void resetState() {
    emit(UnscheduleTaskInitial());
  }

  // Submit report for scheduling tasks
  Future<void> submitReport(SubmitReportRequestEntity request) async {
    emit(UnscheduleTaskLoading()); // Signal start of operation

    final Result<SubmitReportResponseEntity> result =
        await _submitReportUseCase.call(request);

    if (result.isSuccess) {
      // After successful submission, refresh the task list
      final tasksRequest = TasksRequestEntity(
        deviceUid: request.deviceUid ?? "",
        userId: request.userId ?? "",
        appversion: request.appversion ?? "",
        tasks: const [],
        token: request.token ?? "",
      );

      final calendarParams = GetCalendarParams(
        token: request.token ?? "",
        userId: request.userId ?? "",
      );

      final refreshResults = await Future.wait([
        _getTasksUseCase(tasksRequest),
        _getCalendarUseCase(calendarParams),
      ]);

      final refreshOperationNames = ['Tasks', 'Calendar'];

      if (refreshResults.hasFailures) {
        final errorMessage =
            refreshResults.getCombinedErrorMessage(refreshOperationNames);
        emit(UnscheduleTaskError(errorMessage));
        return;
      }

      emit(UnscheduleTaskSuccess(
        tasksResponse: refreshResults[0].data as TasksResponseEntity,
        calendarResponse: refreshResults[1].data as CalendarResponseEntity,
      ));
    } else {
      // Failure: Extract error message from Result
      final errorMessage = result.error?.toString() ??
          'Unknown error occurred while submitting report.';
      emit(UnscheduleTaskError(errorMessage));
    }
  }

  // Submit multiple reports for scheduling multiple tasks
  Future<void> submitMultipleReports(
      List<SubmitReportRequestEntity> requests) async {
    emit(UnscheduleTaskLoading()); // Signal start of operation

    int successCount = 0;
    List<String> failedTaskIds = [];
    String? lastErrorMessage;

    // Process each task individually in a loop
    for (int i = 0; i < requests.length; i++) {
      final request = requests[i];

      try {
        final Result<SubmitReportResponseEntity> result =
            await _submitReportUseCase.call(request);

        if (result.isSuccess) {
          successCount++;
        } else {
          failedTaskIds.add(request.taskId ?? 'Unknown');
          lastErrorMessage = result.error?.toString() ??
              'Unknown error occurred while submitting task ${request.taskId}.';
        }
      } catch (e) {
        failedTaskIds.add(request.taskId ?? 'Unknown');
        lastErrorMessage = 'Error submitting task ${request.taskId}: $e';
      }
    }

    // After processing all tasks, refresh the data
    if (successCount > 0 && requests.isNotEmpty) {
      final firstRequest = requests.first;
      final tasksRequest = TasksRequestEntity(
        deviceUid: firstRequest.deviceUid ?? "",
        userId: firstRequest.userId ?? "",
        appversion: firstRequest.appversion ?? "",
        tasks: const [],
        token: firstRequest.token ?? "",
      );

      final calendarParams = GetCalendarParams(
        token: firstRequest.token ?? "",
        userId: firstRequest.userId ?? "",
      );

      final refreshResults = await Future.wait([
        _getTasksUseCase(tasksRequest),
        _getCalendarUseCase(calendarParams),
      ]);

      final refreshOperationNames = ['Tasks', 'Calendar'];

      if (refreshResults.hasFailures) {
        final errorMessage =
            refreshResults.getCombinedErrorMessage(refreshOperationNames);
        emit(UnscheduleTaskError(errorMessage));
        return;
      }

      emit(UnscheduleTaskSuccess(
        tasksResponse: refreshResults[0].data as TasksResponseEntity,
        calendarResponse: refreshResults[1].data as CalendarResponseEntity,
      ));

      // If some tasks failed, provide detailed feedback but don't treat as complete error
      // since we successfully refreshed the data
    } else {
      // All failed or no requests
      final errorMessage = lastErrorMessage ?? 'Failed to schedule any tasks.';
      emit(UnscheduleTaskError(errorMessage));
    }
  }
}
