import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_auto_schedule_link_usecase.dart';

part 'auto_schedule_state.dart';

class AutoScheduleCubit extends Cubit<AutoScheduleState> {
  final GetAutoScheduleLinkUseCase getAutoScheduleLinkUseCase;

  AutoScheduleCubit({
    required this.getAutoScheduleLinkUseCase,
  }) : super(AutoScheduleInitial());

  Future<void> loadAutoScheduleLink({int dayOffset = 0}) async {
    emit(AutoScheduleLoading());
    try {
      final dataManager = sl<DataManager>();
      final authToken = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (authToken != null && userId != null) {
        final result = await getAutoScheduleLinkUseCase.call(
          AutoScheduleLinkParams(
            token: authToken,
            userId: userId,
            dayOffset: dayOffset,
          ),
        );

        if (result.isSuccess && result.data != null) {
          emit(AutoScheduleLoaded(result.data!, dayOffset));
        } else {
          emit(AutoScheduleError(
              result.error ?? 'Failed to load auto schedule link'));
        }
      } else {
        emit(const AutoScheduleError('Authentication required'));
      }
    } catch (e) {
      emit(AutoScheduleError(e.toString()));
    }
  }
}
