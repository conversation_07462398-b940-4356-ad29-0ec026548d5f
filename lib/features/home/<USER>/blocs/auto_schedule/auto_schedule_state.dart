part of 'auto_schedule_cubit.dart';

abstract class AutoScheduleState extends Equatable {
  const AutoScheduleState();

  @override
  List<Object?> get props => [];
}

class AutoScheduleInitial extends AutoScheduleState {}

class AutoScheduleLoading extends AutoScheduleState {}

class AutoScheduleLoaded extends AutoScheduleState {
  final String autoScheduleUrl;
  final int dayOffset;

  const AutoScheduleLoaded(this.autoScheduleUrl, this.dayOffset);

  @override
  List<Object?> get props => [autoScheduleUrl, dayOffset];
}

class AutoScheduleError extends AutoScheduleState {
  final String message;

  const AutoScheduleError(this.message);

  @override
  List<Object?> get props => [message];
}
