import 'package:equatable/equatable.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;

abstract class SignaturePageState extends Equatable {
  const SignaturePageState();

  @override
  List<Object?> get props => [];
}

class SignaturePageInitial extends SignaturePageState {}

class SignaturePageLoading extends SignaturePageState {}

class SignaturePageLoaded extends SignaturePageState {
  final entities.TaskDetail task;
  final entities.Form form;
  final List<entities.QuestionAnswer> questionAnswers;
  final String? existingSignatureUrl;

  const SignaturePageLoaded({
    required this.task,
    required this.form,
    required this.questionAnswers,
    this.existingSignatureUrl,
  });

  @override
  List<Object?> get props =>
      [task, form, questionAnswers, existingSignatureUrl];
}

class SignaturePageSaving extends SignaturePageState {
  final entities.TaskDetail task;
  final entities.Form form;
  final List<entities.QuestionAnswer> questionAnswers;

  const SignaturePageSaving({
    required this.task,
    required this.form,
    required this.questionAnswers,
  });

  @override
  List<Object?> get props => [task, form, questionAnswers];
}

class SignaturePageSaved extends SignaturePageState {
  final String signatureUrl;

  const SignaturePageSaved(this.signatureUrl);

  @override
  List<Object?> get props => [signatureUrl];
}

class SignaturePageError extends SignaturePageState {
  final String message;

  const SignaturePageError(this.message);

  @override
  List<Object?> get props => [message];
}
