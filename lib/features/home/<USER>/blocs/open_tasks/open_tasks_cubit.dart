import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_tasks_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import 'open_tasks_state.dart';

class OpenTasksCubit extends Cubit<OpenTasksState> {
  final GetTasksUseCase _getTasksUseCase;

  OpenTasksCubit(this._getTasksUseCase) : super(OpenTasksInitial());

  Future<void> getOpenTasks(TasksRequestEntity request) async {
    emit(OpenTasksLoading());

    try {
      final Result<TasksResponseEntity> result =
          await _getTasksUseCase(request);

      if (result.isSuccess && result.data != null) {
        emit(OpenTasksSuccess(result.data!));
      } else {
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while fetching open tasks.';
        emit(OpenTasksError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in OpenTasksCubit: $e");
      emit(OpenTasksError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  Future<void> acceptTask(String taskId, String userId, String token,
      String deviceUid, String appVersion) async {
    emit(AcceptTaskLoading());

    try {
      // Create a request to accept the task
      // This would typically call a different API endpoint for accepting tasks
      // For now, we'll simulate the acceptance by refreshing the data
      final request = TasksRequestEntity(
        deviceUid: deviceUid,
        userId: userId,
        appversion: appVersion,
        tasks: const [],
        token: token,
      );

      // In a real implementation, you would call an accept task API here
      // For now, we'll just refresh the open tasks data
      await getOpenTasks(request);

      emit(const AcceptTaskSuccess('Task accepted successfully'));
    } catch (e) {
      logger("Error accepting task: $e");
      emit(AcceptTaskError('Failed to accept task: ${e.toString()}'));
    }
  }

  void resetState() {
    emit(OpenTasksInitial());
  }
}
