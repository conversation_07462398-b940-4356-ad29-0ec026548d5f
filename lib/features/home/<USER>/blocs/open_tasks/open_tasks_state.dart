import 'package:equatable/equatable.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

abstract class OpenTasksState extends Equatable {
  const OpenTasksState();

  @override
  List<Object?> get props => [];
}

class OpenTasksInitial extends OpenTasksState {}

class OpenTasksLoading extends OpenTasksState {}

class OpenTasksSuccess extends OpenTasksState {
  final TasksResponseEntity response;

  const OpenTasksSuccess(this.response);

  @override
  List<Object?> get props => [response];
}

class OpenTasksError extends OpenTasksState {
  final String message;

  const OpenTasksError(this.message);

  @override
  List<Object?> get props => [message];
}

class AcceptTaskLoading extends OpenTasksState {}

class AcceptTaskSuccess extends OpenTasksState {
  final String message;

  const AcceptTaskSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

class AcceptTaskError extends OpenTasksState {
  final String message;

  const AcceptTaskError(this.message);

  @override
  List<Object?> get props => [message];
}
