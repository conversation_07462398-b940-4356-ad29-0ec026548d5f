import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/models/store_comment_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/store_comment_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_store_comments_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/save_store_comment_usecase.dart';

part 'store_comments_state.dart';

class StoreCommentsCubit extends Cubit<StoreCommentsState> {
  final GetStoreCommentsUseCase getStoreCommentsUseCase;
  final SaveStoreCommentUseCase saveStoreCommentUseCase;

  StoreCommentsCubit({
    required this.getStoreCommentsUseCase,
    required this.saveStoreCommentUseCase,
  }) : super(StoreCommentsLoading());

  Future<void> fetchStoreComments({required String taskId}) async {
    emit(StoreCommentsLoading());
    try {
      final dataManager = sl<DataManager>();
      final authToken = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (authToken != null && userId != null) {
        final result = await getStoreCommentsUseCase.call(
          taskId: taskId,
          userId: userId,
          token: authToken,
        );

        if (result.isSuccess && result.data != null) {
          emit(StoreCommentsLoaded(result.data!));
        } else {
          emit(StoreCommentsError(
              result.error ?? 'Failed to fetch store comments'));
        }
      } else {
        emit(const StoreCommentsError('Authentication required'));
      }
    } catch (e) {
      emit(StoreCommentsError(e.toString()));
    }
  }

  Future<void> saveStoreComment({
    required String taskId,
    required String comment,
    String? storeCommentId,
  }) async {
    emit(StoreCommentsSaving());

    try {
      final dataManager = sl<DataManager>();
      final authToken = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (authToken != null && userId != null) {
        final requestEntity = StoreCommentRequestEntity(
          taskId: taskId,
          storeCommentId: storeCommentId ?? '0', // '0' for new comments
          token: authToken,
          comment: comment,
          userId: userId,
        );

        final result =
            await saveStoreCommentUseCase.call(requestEntity: requestEntity);

        if (result.isSuccess) {
          emit(StoreCommentsSaved());
          // Refresh the comments list after saving
          await fetchStoreComments(taskId: taskId);
        } else {
          emit(StoreCommentsError(
              result.error ?? 'Failed to save store comment'));
        }
      } else {
        emit(const StoreCommentsError('Authentication required'));
      }
    } catch (e) {
      emit(StoreCommentsError(e.toString()));
    }
  }
}
