part of 'store_comments_cubit.dart';

abstract class StoreCommentsState extends Equatable {
  const StoreCommentsState();

  @override
  List<Object?> get props => [];
}

class StoreCommentsLoading extends StoreCommentsState {}

class StoreCommentsLoaded extends StoreCommentsState {
  final StoreCommentsResponse response;

  const StoreCommentsLoaded(this.response);

  @override
  List<Object?> get props => [response];
}

class StoreCommentsSaving extends StoreCommentsState {}

class StoreCommentsSaved extends StoreCommentsState {}

class StoreCommentsError extends StoreCommentsState {
  final String message;

  const StoreCommentsError(this.message);

  @override
  List<Object?> get props => [message];
}
