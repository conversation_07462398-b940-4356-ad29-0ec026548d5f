import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../data/models/history_response.dart';
import '../../../domain/usecases/get_history_usecase.dart';

part 'history_state.dart';

class HistoryCubit extends Cubit<HistoryState> {
  final GetHistoryUseCase _getHistoryUseCase;

  HistoryCubit({required GetHistoryUseCase getHistoryUseCase})
      : _getHistoryUseCase = getHistoryUseCase,
        super(HistoryInitial());

  Future<void> fetchHistory({
    required String token,
    required String userId,
  }) async {
    emit(HistoryLoading());

    try {
      final result = await _getHistoryUseCase.call(
        token: token,
        userId: userId,
      );

      if (result.isSuccess) {
        emit(HistoryLoaded(result.data!));
      } else {
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while fetching history.';
        emit(HistoryError(errorMessage));
      }
    } catch (e) {
      emit(HistoryError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  void resetState() {
    emit(HistoryInitial());
  }
}
