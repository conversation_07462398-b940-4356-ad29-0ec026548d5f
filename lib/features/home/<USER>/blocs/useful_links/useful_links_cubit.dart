import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../data/models/useful_links_response.dart';
import '../../../domain/usecases/get_useful_links_usecase.dart';

part 'useful_links_state.dart';

class UsefulLinksCubit extends Cubit<UsefulLinksState> {
  final GetUsefulLinksUseCase getUsefulLinksUseCase;

  UsefulLinksCubit({required this.getUsefulLinksUseCase})
      : super(UsefulLinksInitial());

  Future<void> fetchUsefulLinks({
    required String token,
    required String userId,
  }) async {
    emit(UsefulLinksLoading());

    final result = await getUsefulLinksUseCase.call(
      token: token,
      userId: userId,
    );

    if (result.isSuccess) {
      emit(UsefulLinksLoaded(result.data!));
    } else {
      emit(UsefulLinksError(result.error!));
    }
  }
}
