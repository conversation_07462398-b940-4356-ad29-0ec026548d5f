part of 'useful_links_cubit.dart';

abstract class UsefulLinksState extends Equatable {
  const UsefulLinksState();

  @override
  List<Object?> get props => [];
}

class UsefulLinksInitial extends UsefulLinksState {}

class UsefulLinksLoading extends UsefulLinksState {}

class UsefulLinksLoaded extends UsefulLinksState {
  final UsefulLinksResponse response;

  const UsefulLinksLoaded(this.response);

  @override
  List<Object?> get props => [response];
}

class UsefulLinksError extends UsefulLinksState {
  final String message;

  const UsefulLinksError(this.message);

  @override
  List<Object?> get props => [message];
}
