import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/availability_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_availability_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/save_availability_usecase.dart';

part 'availability_state.dart';

class AvailabilityCubit extends Cubit<AvailabilityState> {
  final GetAvailabilityUseCase getAvailabilityUseCase;
  final SaveAvailabilityUseCase saveAvailabilityUseCase;

  AvailabilityCubit({
    required this.getAvailabilityUseCase,
    required this.saveAvailabilityUseCase,
  }) : super(AvailabilityLoading());

  Future<void> fetchAvailability() async {
    emit(AvailabilityLoading());
    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (token != null && userId != null) {
        final result = await getAvailabilityUseCase.call(
          token: token,
          userId: userId,
        );

        if (result.isSuccess && result.data != null) {
          final days = result.data!.toEntity().days;

          // Sort days by day_order to show them in the correct sequence
          days.sort((a, b) => a.dayOrder.compareTo(b.dayOrder));

          emit(AvailabilityLoaded(days));
        } else {
          emit(AvailabilityError(
              result.error ?? 'Failed to fetch availability'));
        }
      } else {
        emit(const AvailabilityError('Authentication required'));
      }
    } catch (e) {
      emit(AvailabilityError(e.toString()));
    }
  }

  int _convert24HourTo24HourInteger(String timeStr) {
    try {
      final parts = timeStr.split(':');
      final hours = int.parse(parts[0]);
      final minutes = int.parse(parts[1]);
      return hours * 60 + minutes; // Convert to minutes for easier comparison
    } catch (e) {
      return 0;
    }
  }

  String? validateAvailabilityData(
    Map<String, List<Map<String, String>>> dayTimeSlots,
    Map<String, bool> dayAvailabilityStatus,
  ) {
    String errorMessage = "";
    bool hasAtLeastOneEntry = false;
    int startEndErrors = 0;
    int overlapErrors = 0;

    // Check each day
    for (final entry in dayTimeSlots.entries) {
      final day = entry.key;
      final timeSlots = entry.value;
      final isAvailable = dayAvailabilityStatus[day] ?? false;

      if (isAvailable && timeSlots.isNotEmpty) {
        hasAtLeastOneEntry = true;

        // (1) Validate start_hour < end_hour
        for (final slot in timeSlots) {
          final startTime = slot['start'] ?? '0:00';
          final endTime = slot['end'] ?? '0:00';

          final startMinutes = _convert24HourTo24HourInteger(startTime);
          final endMinutes = _convert24HourTo24HourInteger(endTime);

          if (startMinutes >= endMinutes) {
            startEndErrors++;
          }
        }

        // (2) Validate non-overlapping time spans
        if (timeSlots.length > 1) {
          // Sort slots by start time first
          final sortedSlots = List<Map<String, String>>.from(timeSlots);
          sortedSlots.sort((a, b) {
            final aStart = _convert24HourTo24HourInteger(a['start'] ?? '0:00');
            final bStart = _convert24HourTo24HourInteger(b['start'] ?? '0:00');
            return aStart.compareTo(bStart);
          });

          for (int i = 1; i < sortedSlots.length; i++) {
            final currentStartTime = sortedSlots[i]['start'] ?? '0:00';
            final previousEndTime = sortedSlots[i - 1]['end'] ?? '0:00';

            final currentStartMinutes =
                _convert24HourTo24HourInteger(currentStartTime);
            final previousEndMinutes =
                _convert24HourTo24HourInteger(previousEndTime);

            if (currentStartMinutes <= previousEndMinutes) {
              overlapErrors++;
            }
          }
        }
      }
    }

    // Generate error messages
    if (startEndErrors > 0 && overlapErrors > 0) {
      errorMessage =
          "The end time must be bigger than the start time. Also, the end time and the start time should not overlap.";
    } else if (startEndErrors > 0) {
      errorMessage = "The end time must be bigger than the start time.";
    } else if (overlapErrors > 0) {
      errorMessage = "The end time and the start time should not overlap.";
    }

    // Handle validation results
    if (startEndErrors > 0 || overlapErrors > 0) {
      return errorMessage;
    } else if (!hasAtLeastOneEntry) {
      return "Please select at least one day and set your availability times.";
    }

    return null; // No errors
  }

  Future<String?> saveAvailability(
    Map<String, List<Map<String, String>>> dayTimeSlots,
    Map<String, bool> dayAvailabilityStatus,
  ) async {
    final currentState = state;
    if (currentState is AvailabilityLoaded) {
      // Validate first
      final validationError =
          validateAvailabilityData(dayTimeSlots, dayAvailabilityStatus);
      if (validationError != null) {
        return validationError; // Return validation error instead of emitting state
      }

      emit(AvailabilitySaving(currentState.days));

      try {
        final dataManager = sl<DataManager>();
        final token = await dataManager.getAuthToken();
        final userId = await dataManager.getUserId();

        if (token != null && userId != null) {
          // Prepare the availability data for API - only include days with valid time spans
          final List<Map<String, dynamic>> days = [];

          for (final entry in dayTimeSlots.entries) {
            final day = entry.key;
            final timeSlots = entry.value;
            final isAvailable = dayAvailabilityStatus[day] ?? false;

            // Only include days that are available and have time slots
            if (isAvailable && timeSlots.isNotEmpty) {
              // Find the day entity from current state
              final dayEntity = currentState.days.firstWhere(
                (d) => d.dayDescription == day,
                orElse: () => currentState.days.first,
              );

              final List<Map<String, dynamic>> daySpans = [];

              for (int i = 0; i < timeSlots.length; i++) {
                final slot = timeSlots[i];
                daySpans.add({
                  'day_entry_number': (i + 1).toString(),
                  'start_hour': slot['start'],
                  'end_hour': slot['end'],
                });
              }

              days.add({
                'day_number': dayEntity.dayNumber.toString(),
                'day_description': dayEntity.dayDescription,
                'day_spans': daySpans,
              });
            }
            // Skip days that are disabled or have no time slots
          }

          // Log the request details for debugging
          print('🔄 Saving availability data...');
          print('📋 Days with valid time spans to save: ${days.length}');
          for (final day in days) {
            final daySpans = day['day_spans'] as List;
            print(
                '   ✅ ${day['day_description']}: ${daySpans.length} time spans');
          }

          // Log skipped days
          final skippedDays = dayTimeSlots.entries
              .where((entry) =>
                  !(dayAvailabilityStatus[entry.key] ?? false) ||
                  entry.value.isEmpty)
              .map((entry) => entry.key)
              .toList();
          if (skippedDays.isNotEmpty) {
            print(
                '🚫 Skipped days (disabled/no time spans): ${skippedDays.join(', ')}');
          }

          final result = await saveAvailabilityUseCase.call(
            token: token,
            userId: userId,
            days: days,
          );

          if (result.isSuccess) {
            emit(AvailabilitySaved(currentState.days));
            // Return to loaded state
            emit(AvailabilityLoaded(currentState.days));
            return null; // Success
          } else {
            emit(AvailabilityError(
                result.error ?? 'Failed to save availability'));
            return null; // API error handled by state
          }
        } else {
          emit(const AvailabilityError('Authentication required'));
          return null; // Auth error handled by state
        }
      } catch (e) {
        emit(AvailabilityError(e.toString()));
        return null; // Exception handled by state
      }
    }
    return null;
  }
}
