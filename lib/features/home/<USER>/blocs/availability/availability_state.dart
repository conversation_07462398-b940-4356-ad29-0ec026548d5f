part of 'availability_cubit.dart';

abstract class AvailabilityState extends Equatable {
  const AvailabilityState();

  @override
  List<Object?> get props => [];
}

class AvailabilityLoading extends AvailabilityState {}

class AvailabilityLoaded extends AvailabilityState {
  final List<DayAvailabilityEntity> days;

  const AvailabilityLoaded(this.days);

  @override
  List<Object?> get props => [days];
}

class AvailabilitySaving extends AvailabilityState {
  final List<DayAvailabilityEntity> days;

  const AvailabilitySaving(this.days);

  @override
  List<Object?> get props => [days];
}

class AvailabilitySaved extends AvailabilityState {
  final List<DayAvailabilityEntity> days;

  const AvailabilitySaved(this.days);

  @override
  List<Object?> get props => [days];
}

class AvailabilityError extends AvailabilityState {
  final String message;

  const AvailabilityError(this.message);

  @override
  List<Object?> get props => [message];
}
