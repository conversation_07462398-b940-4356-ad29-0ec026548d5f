part of 'induction_cubit.dart';

abstract class InductionState extends Equatable {
  const InductionState();

  @override
  List<Object?> get props => [];
}

class InductionLoading extends InductionState {}

class InductionLoaded extends InductionState {
  final InductionResponse response;
  final bool isEditMode;

  const InductionLoaded(this.response, {this.isEditMode = false});

  @override
  List<Object?> get props => [response, isEditMode];
}

class InductionSaving extends InductionState {
  final InductionResponse response;

  const InductionSaving(this.response);

  @override
  List<Object?> get props => [response];
}

class InductionSaved extends InductionState {
  final InductionResponse response;

  const InductionSaved(this.response);

  @override
  List<Object?> get props => [response];
}

class InductionError extends InductionState {
  final String message;

  const InductionError(this.message);

  @override
  List<Object?> get props => [message];
}
