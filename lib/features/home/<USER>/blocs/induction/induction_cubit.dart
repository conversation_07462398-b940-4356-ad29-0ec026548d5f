import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/save_induction_usecase.dart';
import '../../../data/models/induction_response.dart';
import '../../../domain/usecases/get_induction_usecase.dart';

part 'induction_state.dart';

class InductionCubit extends Cubit<InductionState> {
  final GetInductionUseCase getInductionUseCase;
  final SaveInductionUseCase saveInductionUseCase;

  InductionCubit({
    required this.getInductionUseCase,
    required this.saveInductionUseCase,
  }) : super(InductionLoading());

  Future<void> fetchInduction() async {
    emit(InductionLoading());
    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (token != null && userId != null) {
        final result = await getInductionUseCase.call(
          token: token,
          userId: userId,
        );

        if (result.isSuccess && result.data != null) {
          emit(InductionLoaded(result.data!));
        } else {
          emit(InductionError(result.error ?? 'Failed to fetch induction'));
        }
      } else {
        emit(const InductionError('Authentication required'));
      }
    } catch (e) {
      emit(InductionError(e.toString()));
    }
  }

  void toggleEditMode() {
    final currentState = state;
    if (currentState is InductionLoaded) {
      emit(InductionLoaded(currentState.response,
          isEditMode: !currentState.isEditMode));
    }
  }

  void toggleInductionSelection(int inductionId) {
    final currentState = state;
    if (currentState is InductionLoaded) {
      final updatedInductions =
          currentState.response.data?.inductions?.map((induction) {
        if (induction.inductionId == inductionId) {
          // Toggle the selection state regardless of current value
          final newHasValue = !(induction.has ?? false);

          // Mark as edited to track changes
          return induction.copyWith(
            has: newHasValue,
            isEdit: true,
          );
        }
        return induction;
      }).toList();

      if (updatedInductions != null) {
        final updatedData = InductionData(inductions: updatedInductions);
        final updatedResponse = InductionResponse(data: updatedData);
        emit(InductionLoaded(updatedResponse,
            isEditMode: currentState.isEditMode));
      }
    }
  }

  Future<void> saveInductionChanges() async {
    final currentState = state;
    if (currentState is InductionLoaded) {
      emit(InductionSaving(currentState.response));

      try {
        final dataManager = sl<DataManager>();
        final token = await dataManager.getAuthToken();
        final userId = await dataManager.getUserId();

        if (token != null && userId != null) {
          // Prepare the inductions data for API - send all items, not just edited ones
          final inductionsToSave = currentState.response.data?.inductions
                  ?.map((induction) => {
                        'induction_id': induction.inductionId,
                        'has': induction.has ?? false,
                      })
                  .toList() ??
              [];

          final result = await saveInductionUseCase.call(
            token: token,
            userId: userId,
            inductions: inductionsToSave,
          );

          if (result.isSuccess) {
            // Reset edit flags after successful save
            final updatedInductions =
                currentState.response.data?.inductions?.map((induction) {
              return induction.copyWith(isEdit: false);
            }).toList();

            if (updatedInductions != null) {
              final updatedData = InductionData(inductions: updatedInductions);
              final updatedResponse = InductionResponse(data: updatedData);
              emit(InductionSaved(updatedResponse));

              // Return to view mode
              emit(InductionLoaded(updatedResponse, isEditMode: false));
            }
          } else {
            emit(InductionError(
                result.error ?? 'Failed to save induction changes'));
          }
        } else {
          emit(const InductionError('Authentication required'));
        }
      } catch (e) {
        emit(InductionError(e.toString()));
      }
    }
  }
}
