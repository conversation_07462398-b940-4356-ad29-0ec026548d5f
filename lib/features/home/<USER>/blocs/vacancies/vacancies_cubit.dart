import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/vacancy_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/apply_to_vacancy_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_vacancies_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/refer_vacancy_usecase.dart';

part 'vacancies_state.dart';

class VacanciesCubit extends Cubit<VacanciesState> {
  final GetVacanciesUseCase getVacanciesUseCase;
  final ApplyToVacancyUseCase applyToVacancyUseCase;
  final ReferVacancyUseCase referVacancyUseCase;

  VacanciesCubit({
    required this.getVacanciesUseCase,
    required this.applyToVacancyUseCase,
    required this.referVacancyUseCase,
  }) : super(VacanciesInitial());

  Future<void> fetchVacancies() async {
    emit(VacanciesLoading());

    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (token != null && userId != null) {
        final result = await getVacanciesUseCase.call(
          token: token,
          userId: userId,
        );

        if (result.isSuccess && result.data != null) {
          emit(VacanciesLoaded(result.data!));
        } else {
          emit(VacanciesError(result.error ?? 'Failed to fetch vacancies'));
        }
      } else {
        emit(const VacanciesError('Authentication required'));
      }
    } catch (e) {
      emit(VacanciesError(e.toString()));
    }
  }

  Future<void> applyToVacancy(int vacancyId) async {
    final currentState = state;
    if (currentState is VacanciesLoaded) {
      emit(VacancyActionInProgress(currentState.vacancies, vacancyId, 'apply'));

      try {
        final dataManager = sl<DataManager>();
        final token = await dataManager.getAuthToken();
        final userId = await dataManager.getUserId();

        if (token != null && userId != null) {
          final result = await applyToVacancyUseCase.call(
            token: token,
            userId: userId,
            vacancyId: vacancyId,
          );

          if (result.isSuccess) {
            emit(VacancyActionSuccess(
              currentState.vacancies,
              'Application submitted successfully!',
            ));
            // Return to loaded state after a short delay
            await Future.delayed(const Duration(seconds: 2));
            emit(VacanciesLoaded(currentState.vacancies));
          } else {
            emit(VacancyActionError(
              currentState.vacancies,
              result.error ?? 'Failed to submit application',
            ));
          }
        } else {
          emit(VacancyActionError(
            currentState.vacancies,
            'Authentication required',
          ));
        }
      } catch (e) {
        emit(VacancyActionError(currentState.vacancies, e.toString()));
      }
    } else {
      // Handle case where state is not VacanciesLoaded
      emit(const VacanciesError('Vacancies not loaded. Please try again.'));
    }
  }

  Future<void> referVacancy(int vacancyId, String refereeEmail) async {
    final currentState = state;
    if (currentState is VacanciesLoaded) {
      emit(VacancyActionInProgress(currentState.vacancies, vacancyId, 'refer'));

      try {
        final dataManager = sl<DataManager>();
        final token = await dataManager.getAuthToken();
        final userId = await dataManager.getUserId();

        if (token != null && userId != null) {
          final result = await referVacancyUseCase.call(
            token: token,
            userId: userId,
            vacancyId: vacancyId,
            refereeEmail: refereeEmail,
          );

          if (result.isSuccess) {
            emit(VacancyActionSuccess(
              currentState.vacancies,
              'Referral submitted successfully!',
            ));
            // Return to loaded state after a short delay
            await Future.delayed(const Duration(seconds: 2));
            emit(VacanciesLoaded(currentState.vacancies));
          } else {
            emit(VacancyActionError(
              currentState.vacancies,
              result.error ?? 'Failed to submit referral',
            ));
          }
        } else {
          emit(VacancyActionError(
            currentState.vacancies,
            'Authentication required',
          ));
        }
      } catch (e) {
        emit(VacancyActionError(currentState.vacancies, e.toString()));
      }
    } else {
      // Handle case where state is not VacanciesLoaded
      emit(const VacanciesError('Vacancies not loaded. Please try again.'));
    }
  }

  Future<void> referVacancyWithDetails(
    int vacancyId, {
    required String name,
    required String email,
    required String phone,
    required String stateName,
    required String suburb,
    required String comment,
    required String preference,
  }) async {
    print(
        'DEBUG: VacanciesCubit.referVacancyWithDetails called with vacancyId: $vacancyId');
    print('DEBUG: Current state type: ${state.runtimeType}');

    final currentState = state;
    List<VacancyEntity> vacancies = [];

    // Get vacancies from current state if available, otherwise use empty list
    if (currentState is VacanciesLoaded) {
      vacancies = currentState.vacancies;
      print('DEBUG: State is VacanciesLoaded, proceeding with referral');
    } else if (currentState is VacancyActionInProgress) {
      vacancies = currentState.vacancies;
      print(
          'DEBUG: State is VacancyActionInProgress, proceeding with referral');
    } else if (currentState is VacancyActionSuccess) {
      vacancies = currentState.vacancies;
      print('DEBUG: State is VacancyActionSuccess, proceeding with referral');
    } else if (currentState is VacancyActionError) {
      vacancies = currentState.vacancies;
      print(
          'DEBUG: State is VacancyActionError, proceeding with referral using existing vacancies');
    } else {
      print('DEBUG: No vacancies in current state, proceeding with empty list');
    }

    emit(VacancyActionInProgress(vacancies, vacancyId, 'refer'));

    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (token != null && userId != null) {
        print('DEBUG: Got token and userId, calling use case');
        // Call the enhanced referVacancyUseCase with detailed information
        final result = await referVacancyUseCase.callWithDetails(
          token: token,
          userId: userId,
          vacancyId: vacancyId,
          name: name,
          email: email,
          phone: phone,
          stateName: stateName,
          suburb: suburb,
          comment: comment,
          preference: preference,
        );
        print('DEBUG: Use case result: ${result.isSuccess} - ${result.error}');

        if (result.isSuccess) {
          emit(VacancyActionSuccess(
            vacancies,
            'Referral submitted successfully!',
          ));
          // Return to loaded state after a short delay
          await Future.delayed(const Duration(seconds: 2));
          emit(VacanciesLoaded(vacancies));
        } else {
          emit(VacancyActionError(
            vacancies,
            result.error ?? 'Failed to submit referral',
          ));
        }
      } else {
        emit(VacancyActionError(
          vacancies,
          'Authentication required',
        ));
      }
    } catch (e) {
      emit(VacancyActionError(vacancies, e.toString()));
    }
  }

  void clearActionStatus() {
    final currentState = state;
    if (currentState is VacancyActionSuccess) {
      emit(VacanciesLoaded(currentState.vacancies));
    } else if (currentState is VacancyActionError) {
      emit(VacanciesLoaded(currentState.vacancies));
    }
  }
}
