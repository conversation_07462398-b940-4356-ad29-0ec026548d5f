part of 'vacancies_cubit.dart';

abstract class VacanciesState extends Equatable {
  const VacanciesState();

  @override
  List<Object?> get props => [];
}

class VacanciesInitial extends VacanciesState {}

class VacanciesLoading extends VacanciesState {}

class VacanciesLoaded extends VacanciesState {
  final List<VacancyEntity> vacancies;

  const VacanciesLoaded(this.vacancies);

  @override
  List<Object> get props => [vacancies];
}

class VacanciesError extends VacanciesState {
  final String message;

  const VacanciesError(this.message);

  @override
  List<Object> get props => [message];
}

class VacancyActionInProgress extends VacanciesState {
  final List<VacancyEntity> vacancies;
  final int vacancyId;
  final String action; // 'apply' or 'refer'

  const VacancyActionInProgress(this.vacancies, this.vacancyId, this.action);

  @override
  List<Object> get props => [vacancies, vacancyId, action];
}

class VacancyActionSuccess extends VacanciesState {
  final List<VacancyEntity> vacancies;
  final String message;

  const VacancyActionSuccess(this.vacancies, this.message);

  @override
  List<Object> get props => [vacancies, message];
}

class VacancyActionError extends VacanciesState {
  final List<VacancyEntity> vacancies;
  final String message;

  const VacancyActionError(this.vacancies, this.message);

  @override
  List<Object> get props => [vacancies, message];
}
