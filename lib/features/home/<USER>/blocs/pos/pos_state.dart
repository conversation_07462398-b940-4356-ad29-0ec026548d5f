part of 'pos_cubit.dart';

abstract class PosState extends Equatable {
  const PosState();

  @override
  List<Object?> get props => [];
}

class PosInitial extends PosState {}

class PosLoading extends PosState {}

class PosSuccess extends PosState {
  final String message;
  final bool received;

  const PosSuccess({
    required this.message,
    required this.received,
  });

  @override
  List<Object?> get props => [message, received];
}

class PosError extends PosState {
  final String message;

  const PosError(this.message);

  @override
  List<Object?> get props => [message];
}
