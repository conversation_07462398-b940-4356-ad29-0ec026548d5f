part of 'leave_cubit.dart';

abstract class LeaveState extends Equatable {
  const LeaveState();

  @override
  List<Object?> get props => [];
}

class LeaveInitial extends LeaveState {}

class LeaveLoading extends LeaveState {}

class LeaveLoaded extends LeaveState {
  final LeaveResponse response;
  final bool hasSelectedItems;

  const LeaveLoaded({
    required this.response,
    required this.hasSelectedItems,
  });

  @override
  List<Object?> get props => [response, hasSelectedItems];
}

class LeaveDeleting extends LeaveState {}

class LeaveDeleted extends LeaveState {}

class LeaveError extends LeaveState {
  final String message;

  const LeaveError(this.message);

  @override
  List<Object?> get props => [message];
}
