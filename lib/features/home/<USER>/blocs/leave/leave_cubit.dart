import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/models/leave_response.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/delete_leave_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_leave_usecase.dart';

part 'leave_state.dart';

class LeaveCubit extends Cubit<LeaveState> {
  final GetLeaveUseCase _getLeaveUseCase;
  final DeleteLeaveUseCase _deleteLeaveUseCase;

  LeaveCubit({
    required GetLeaveUseCase getLeaveUseCase,
    required DeleteLeaveUseCase deleteLeaveUseCase,
  })  : _getLeaveUseCase = getLeaveUseCase,
        _deleteLeaveUseCase = deleteLeaveUseCase,
        super(LeaveInitial());

  List<Leave> _leaves = [];
  bool _hasSelectedItems = false;

  void fetchLeaves() async {
    emit(LeaveLoading());

    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (token == null || userId == null) {
        emit(const LeaveError('Authentication error. Please login again.'));
        return;
      }

      final result = await _getLeaveUseCase.call(
        token: token,
        userId: userId,
      );

      if (result.isSuccess && result.data != null) {
        _leaves = result.data!.data?.leaves ?? [];
        _updateHasSelectedItems();
        emit(LeaveLoaded(
          response: result.data!,
          hasSelectedItems: _hasSelectedItems,
        ));
      } else {
        emit(LeaveError(result.error ?? 'Unknown error occurred'));
      }
    } catch (e) {
      emit(LeaveError('Error fetching leave data: $e'));
    }
  }

  void toggleLeaveSelection(int? leaveId) {
    if (leaveId == null) return;

    _leaves = _leaves.map((leave) {
      if (leave.leaveId == leaveId) {
        return leave.copyWith(isSelected: !(leave.isSelected ?? false));
      }
      return leave;
    }).toList();

    _updateHasSelectedItems();

    // Create updated response
    final updatedResponse = LeaveResponse(
      data: LeaveData(leaves: _leaves),
    );

    emit(LeaveLoaded(
      response: updatedResponse,
      hasSelectedItems: _hasSelectedItems,
    ));
  }

  void deleteSelectedLeaves() async {
    final selectedLeaveIds = _leaves
        .where((leave) => leave.isSelected == true)
        .map((leave) => leave.leaveId!)
        .toList();

    if (selectedLeaveIds.isEmpty) return;

    emit(LeaveDeleting());

    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (token == null || userId == null) {
        emit(const LeaveError('Authentication error. Please login again.'));
        return;
      }

      final result = await _deleteLeaveUseCase.call(
        token: token,
        userId: userId,
        leaveIds: selectedLeaveIds,
      );

      if (result.isSuccess) {
        // Remove deleted leaves from the list
        _leaves = _leaves
            .where((leave) => !selectedLeaveIds.contains(leave.leaveId))
            .toList();

        _updateHasSelectedItems();

        // Create updated response
        final updatedResponse = LeaveResponse(
          data: LeaveData(leaves: _leaves),
        );

        emit(LeaveDeleted());

        // Emit loaded state again with updated data
        emit(LeaveLoaded(
          response: updatedResponse,
          hasSelectedItems: _hasSelectedItems,
        ));
      } else {
        emit(LeaveError(result.error ?? 'Failed to delete leave'));
      }
    } catch (e) {
      emit(LeaveError('Error deleting leave: $e'));
    }
  }

  void _updateHasSelectedItems() {
    _hasSelectedItems = _leaves.any((leave) => leave.isSelected == true);
  }
}
