part of 'skills_cubit.dart';

abstract class SkillsState extends Equatable {
  const SkillsState();

  @override
  List<Object?> get props => [];
}

class SkillsLoading extends SkillsState {}

class SkillsLoaded extends SkillsState {
  final SkillsResponse response;
  final bool isEditMode;

  const SkillsLoaded(this.response, {this.isEditMode = false});

  @override
  List<Object?> get props => [response, isEditMode];
}

class SkillsSaving extends SkillsState {
  final SkillsResponse response;

  const SkillsSaving(this.response);

  @override
  List<Object?> get props => [response];
}

class SkillsSaved extends SkillsState {
  final SkillsResponse response;

  const SkillsSaved(this.response);

  @override
  List<Object?> get props => [response];
}

class SkillsError extends SkillsState {
  final String message;
  const SkillsError(this.message);

  @override
  List<Object?> get props => [message];
}
