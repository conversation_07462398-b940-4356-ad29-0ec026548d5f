import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/add_leave_usecase.dart';

part 'add_leave_state.dart';

class AddLeaveCubit extends Cubit<AddLeaveState> {
  final AddLeaveUseCase _addLeaveUseCase;

  AddLeaveCubit({
    required AddLeaveUseCase addLeaveUseCase,
  })  : _addLeaveUseCase = addLeaveUseCase,
        super(AddLeaveInitial());

  void submitLeaveRequest({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    emit(AddLeaveLoading());

    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (token == null || userId == null) {
        emit(const AddLeaveError('Authentication error. Please login again.'));
        return;
      }

      final result = await _addLeaveUseCase.call(
        token: token,
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );

      if (result.isSuccess) {
        emit(AddLeaveSuccess());
      } else {
        emit(AddLeaveError(result.error ?? 'Failed to submit leave request'));
      }
    } catch (e) {
      emit(AddLeaveError('Error submitting leave request: $e'));
    }
  }
}
