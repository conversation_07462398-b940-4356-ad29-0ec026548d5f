part of 'add_leave_cubit.dart';

abstract class AddLeaveState extends Equatable {
  const AddLeaveState();

  @override
  List<Object?> get props => [];
}

class AddLeaveInitial extends AddLeaveState {}

class AddLeaveLoading extends AddLeaveState {}

class AddLeaveSuccess extends AddLeaveState {}

class AddLeaveError extends AddLeaveState {
  final String message;

  const AddLeaveError(this.message);

  @override
  List<Object?> get props => [message];
}
