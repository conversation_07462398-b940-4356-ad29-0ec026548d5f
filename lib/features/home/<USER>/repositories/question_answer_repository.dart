import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

/// Repository for managing QuestionAnswer persistence
/// Isolates Realm database operations from the UI layer
class QuestionAnswerRepository {
  final RealmDatabase _realmDatabase;

  QuestionAnswerRepository({
    required RealmDatabase realmDatabase,
  }) : _realmDatabase = realmDatabase;

  /// Fetch saved QuestionAnswers for a specific question and question part
  Future<List<QuestionAnswer>> fetch({
    required int taskId,
    required int formId,
    required int questionId,
    int? questionPartId,
    String? questionPartMultiId,
  }) async {
    try {
      final realm = _realmDatabase.realm;

      // Find the task with the matching taskId
      final taskModel =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

      if (taskModel == null) {
        logger('Task not found in database for taskId: $taskId');
        return [];
      }

      // Find the form with the matching formId
      final formModel =
          taskModel.forms.where((form) => form.formId == formId).firstOrNull;

      if (formModel == null) {
        logger('Form not found in task for formId: $formId');
        return [];
      }

      // Filter QuestionAnswers for this specific question and questionPart
      final questionAnswers = formModel.questionAnswers
          .where((qa) =>
              qa.questionId == questionId &&
              qa.questionpartId == questionPartId)
          .where((qa) {
        // Apply filtering for multi-question instances based on questionPartMultiId
        if (questionPartMultiId != null && questionPartMultiId.contains('-')) {
          // For multi-question instances, filter by exact questionPartMultiId match
          return qa.questionPartMultiId == questionPartMultiId;
        } else {
          // For regular instances, include records with null or matching questionpartId
          return qa.questionPartMultiId == null ||
              qa.questionPartMultiId == questionPartId?.toString();
        }
      }).toList();

      logger(
          'Found ${questionAnswers.length} saved answers for question $questionId');

      // Convert models to entities
      return _mapQuestionAnswersToEntity(questionAnswers);
    } catch (e) {
      logger('Error fetching question answers: $e');
      return [];
    }
  }

  /// Save/update QuestionAnswers for a specific question and question part
  Future<bool> upsert({
    required List<QuestionAnswer> questionAnswers,
    required int taskId,
    required int formId,
    required int questionId,
    int? questionPartId,
    String? questionPartMultiId,
  }) async {
    if (questionAnswers.isEmpty) {
      logger('No question answers to save');
      return false;
    }

    try {
      final realm = _realmDatabase.realm;

      // Find the task with the matching taskId
      final taskModel =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

      if (taskModel == null) {
        logger('Task not found in database for taskId: $taskId');
        return false;
      }

      // Find the form with the matching formId
      final formModel =
          taskModel.forms.where((form) => form.formId == formId).firstOrNull;

      if (formModel == null) {
        logger('Form not found in task for formId: $formId');
        return false;
      }

      // Convert QuestionAnswer entities to models
      final questionAnswerModels = _mapQuestionAnswersToModel(questionAnswers);

      // Save to database in a write transaction
      realm.write(() {
        // Remove existing answers for this question and questionPart
        final existingAnswers = formModel.questionAnswers
            .where((qa) =>
                qa.questionId == questionId &&
                qa.questionpartId == questionPartId)
            .where((qa) {
          // Apply the same filtering logic as data loading to ensure proper data isolation
          if (questionPartMultiId != null &&
              questionPartMultiId.contains('-')) {
            // For multi-question instances, only remove records with exact questionPartMultiId match
            return qa.questionPartMultiId == questionPartMultiId;
          } else {
            // For regular instances, remove records with null or matching questionpartId
            return qa.questionPartMultiId == null ||
                qa.questionPartMultiId == questionPartId?.toString();
          }
        }).toList();

        logger('Removing ${existingAnswers.length} existing answers');

        for (final existingAnswer in existingAnswers) {
          formModel.questionAnswers.remove(existingAnswer);
        }

        // Add new answers
        for (final newAnswer in questionAnswerModels) {
          formModel.questionAnswers.add(newAnswer);
        }
      });

      logger(
          'Successfully saved ${questionAnswerModels.length} question answers to database');
      return true;
    } catch (e) {
      logger('Error saving question answers to database: $e');
      return false;
    }
  }

  /// Check if current form is a quiz form (form_type_id = 12)
  Future<bool> isQuizForm({
    required int taskId,
    required int formId,
  }) async {
    try {
      final realm = _realmDatabase.realm;

      // Find the task with the matching taskId
      final taskModel =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

      if (taskModel == null) return false;

      // Find the form with the matching formId
      final formModel =
          taskModel.forms.where((form) => form.formId == formId).firstOrNull;

      return formModel?.formTypeId == 12;
    } catch (e) {
      logger('Error checking form type: $e');
      return false;
    }
  }

  /// Get photos for measurements based on PhotoTagsThree
  Future<Map<int, List<String>>> getPhotosForMeasurements({
    required int taskId,
    required int questionId,
    int? questionPartId,
  }) async {
    // This method will be implemented when PhotoService is integrated
    // For now, return empty map to maintain interface
    return {};
  }

  /// Convert QuestionAnswer models to entities
  static List<QuestionAnswer> _mapQuestionAnswersToEntity(
      Iterable<QuestionAnswerModel> models) {
    return models
        .map((model) => QuestionAnswer(
              taskId: model.taskId,
              formId: model.formId,
              questionId: model.questionId,
              questionpartId: model.questionpartId,
              flip: model.flip,
              questionPartMultiId: model.questionPartMultiId,
              measurementId: model.measurementId,
              measurementTypeId: model.measurementTypeId,
              measurementOptionId: model.measurementOptionId,
              measurementOptionIds: model.measurementOptionIds,
              measurementTextResult: model.measurementTextResult,
              isComment: model.isComment,
              commentTypeId: model.commentTypeId,
            ))
        .toList();
  }

  /// Convert QuestionAnswer entities to models
  static List<QuestionAnswerModel> _mapQuestionAnswersToModel(
      List<QuestionAnswer> answers) {
    return answers
        .map((answer) => QuestionAnswerModel(
              taskId: answer.taskId?.toInt(),
              formId: answer.formId?.toInt(),
              questionId: answer.questionId?.toInt(),
              questionpartId: answer.questionpartId?.toInt(),
              flip: answer.flip,
              questionPartMultiId: answer.questionPartMultiId,
              measurementId: answer.measurementId?.toInt(),
              measurementTypeId: answer.measurementTypeId?.toInt(),
              measurementOptionId: answer.measurementOptionId?.toInt(),
              measurementOptionIds: answer.measurementOptionIds,
              measurementTextResult: answer.measurementTextResult,
              isComment: answer.isComment,
              commentTypeId: answer.commentTypeId?.toInt(),
            ))
        .toList();
  }
}
