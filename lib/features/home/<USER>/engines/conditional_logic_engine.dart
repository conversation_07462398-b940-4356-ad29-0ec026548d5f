import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

/// Engine for processing conditional logic in measurements
/// Handles showing/hiding of measurements based on user selections
class ConditionalLogicEngine {
  /// Process conditional logic for a measurement value change
  /// Returns a map of measurement ID to visibility boolean
  Map<int, bool> processConditionalLogic({
    required List<Measurement> measurements,
    required Map<int, dynamic> currentValues,
    required Map<int, bool> currentVisibility,
    required int changedMeasurementId,
    required dynamic newValue,
    required bool isMll,
    bool isInitialization = false,
  }) {
    final newVisibility = Map<int, bool>.from(currentVisibility);

    // Find the measurement that changed
    final changedMeasurement = measurements
        .where((m) => m.measurementId == changedMeasurementId)
        .firstOrNull;

    if (changedMeasurement == null) return newVisibility;

    // Reset all dependencies before applying new logic
    if (!isInitialization) {
      _resetConditionalDependencies(
        changedMeasurement,
        measurements,
        newVisibility,
        isMll,
      );
    }

    // Apply new conditional logic
    _applyConditionalLogic(
      changedMeasurement,
      newValue,
      measurements,
      newVisibility,
      isMll,
      isInitialization: isInitialization,
    );

    return newVisibility;
  }

  /// Get initial visibility for measurements based on conditional logic
  Map<int, bool> getInitialVisibility(List<Measurement> measurements) {
    final visibility = <int, bool>{};

    // Get all measurement IDs that are targets of conditional actions
    final targetMeasurementIds = <int>{};

    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      // Check measurement_conditions array
      final conditions = measurement.measurementConditions ?? [];
      for (final condition in conditions) {
        if (condition.actionMeasurementId != null) {
          targetMeasurementIds.add(condition.actionMeasurementId!.toInt());
        }
      }

      // Check measurement_conditions_multiple array
      final conditionsMultiple =
          measurement.measurementConditionsMultiple ?? [];
      for (final condition in conditionsMultiple) {
        if (condition.actionMeasurementId != null) {
          targetMeasurementIds.add(condition.actionMeasurementId!.toInt());
        }
      }
    }

    // Set initial visibility
    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      final measurementId = measurement.measurementId!.toInt();

      // Hide measurements that are targets of 'appear' actions by default
      if (targetMeasurementIds.contains(measurementId)) {
        // Check if this measurement is a target of 'appear' actions
        final isTargetOfAppear = _isTargetOfAppearAction(
          measurementId,
          measurements,
        );
        visibility[measurementId] = !isTargetOfAppear;
      } else {
        // Default to visible if not a target of conditional logic
        visibility[measurementId] = true;
      }
    }

    return visibility;
  }

  /// Check if a measurement can trigger conditional logic
  bool canTriggerConditionalLogic(Measurement measurement) {
    // Check if this measurement has any conditional logic defined
    final hasConditions =
        (measurement.measurementConditions?.isNotEmpty ?? false) ||
            (measurement.measurementConditionsMultiple?.isNotEmpty ?? false);

    // Only certain widget types can trigger conditional logic
    final canTrigger = [3, 4, 5, 6, 8].contains(measurement.measurementTypeId);

    return hasConditions && canTrigger;
  }

  /// Reset all conditional dependencies for a measurement that's about to change
  void _resetConditionalDependencies(
    Measurement measurement,
    List<Measurement> allMeasurements,
    Map<int, bool> visibility,
    bool isMll,
  ) {
    if (measurement.measurementId == null) return;

    // Get all target measurement IDs that this measurement can affect
    final targetMeasurementIds = <int>{};

    // Check measurement_conditions array
    if (!isMll && measurement.measurementConditions != null) {
      for (final condition in measurement.measurementConditions!) {
        if (condition.actionMeasurementId != null) {
          targetMeasurementIds.add(condition.actionMeasurementId!.toInt());
        }
      }
    }

    // Check measurement_conditions_multiple array
    if (isMll && measurement.measurementConditionsMultiple != null) {
      for (final condition in measurement.measurementConditionsMultiple!) {
        if (condition.actionMeasurementId != null) {
          targetMeasurementIds.add(condition.actionMeasurementId!.toInt());
        }
      }
    }

    // Hide all target widgets and cascade
    for (final targetId in targetMeasurementIds) {
      _hideWidgetAndCascade(targetId, allMeasurements, visibility, isMll);
    }
  }

  /// Apply conditional logic for a measurement value change
  void _applyConditionalLogic(
    Measurement measurement,
    dynamic selectedValue,
    List<Measurement> allMeasurements,
    Map<int, bool> visibility,
    bool isMll, {
    bool isInitialization = false,
  }) {
    if (selectedValue == null || measurement.measurementId == null) return;

    // Get the selected option ID based on widget type
    final selectedOptionId = _getSelectedOptionIdForConditionalLogic(
      measurement,
      selectedValue,
    );
    if (selectedOptionId == null) return;

    // Check conditions based on is_mll property
    if (!isMll) {
      // Use measurement_conditions array
      _processConditions(
        measurement.measurementConditions,
        measurement.measurementId!.toInt(),
        selectedOptionId,
        visibility,
        isInitialization: isInitialization,
      );
    } else {
      // Use measurement_conditions_multiple array
      _processConditions(
        measurement.measurementConditionsMultiple,
        measurement.measurementId!.toInt(),
        selectedOptionId,
        visibility,
        isInitialization: isInitialization,
      );
    }
  }

  /// Get the measurement option ID for conditional logic based on widget type
  int? _getSelectedOptionIdForConditionalLogic(
    Measurement measurement,
    dynamic selectedValue,
  ) {
    switch (measurement.measurementTypeId) {
      case 3: // Checkbox
        // For checkboxes, use 1 for true and 2 for false
        return selectedValue == true ? 1 : 2;

      case 4: // Dropdown
      case 5: // Dropdown
      case 8: // Radio button
        return _getSelectedOptionId(measurement, selectedValue);

      case 6: // Multi-select
        // For multi-select, conditional logic might need special handling
        // For now, return null as multi-select conditional logic is complex
        return null;

      default:
        return null;
    }
  }

  /// Get the measurement option ID for the selected value
  int? _getSelectedOptionId(Measurement measurement, dynamic selectedValue) {
    final options = measurement.measurementOptions;
    if (options == null) return null;

    try {
      final selectedOption = options.firstWhere(
        (option) => option.measurementOptionDescription == selectedValue,
      );
      return selectedOption.measurementOptionId?.toInt();
    } catch (e) {
      return null;
    }
  }

  /// Process measurement conditions array
  void _processConditions(
    List<MeasurementCondition>? conditions,
    int currentMeasurementId,
    int selectedOptionId,
    Map<int, bool> visibility, {
    bool isInitialization = false,
  }) {
    if (conditions == null) return;

    for (final condition in conditions) {
      // Check if this condition applies to the current measurement and selected option
      if (condition.measurementId?.toInt() == currentMeasurementId &&
          condition.measurementOptionId?.toInt() == selectedOptionId) {
        // Apply the action to the target measurement
        if (condition.actionMeasurementId != null && condition.action != null) {
          _applyConditionalAction(
            condition.actionMeasurementId!.toInt(),
            condition.action!,
            visibility,
          );
        }
      }
    }
  }

  /// Apply conditional action (show/hide) to a target measurement widget
  void _applyConditionalAction(
    int targetMeasurementId,
    String action,
    Map<int, bool> visibility,
  ) {
    // Update widget visibility based on action
    if (action.toLowerCase() == 'appear') {
      visibility[targetMeasurementId] = true;
    } else if (action.toLowerCase() == 'disappear') {
      visibility[targetMeasurementId] = false;
    }
  }

  /// Hide a widget and cascade the hiding to all its dependent widgets
  void _hideWidgetAndCascade(
    int measurementId,
    List<Measurement> allMeasurements,
    Map<int, bool> visibility,
    bool isMll,
  ) {
    // Hide the widget
    visibility[measurementId] = false;

    // Find all widgets that depend on this widget and hide them recursively
    final dependentMeasurementIds = <int>{};

    for (final measurement in allMeasurements) {
      if (measurement.measurementId?.toInt() == measurementId) continue;

      // Check measurement_conditions array
      if (!isMll && measurement.measurementConditions != null) {
        for (final condition in measurement.measurementConditions!) {
          if (condition.measurementId?.toInt() == measurementId &&
              condition.actionMeasurementId != null) {
            dependentMeasurementIds.add(condition.actionMeasurementId!.toInt());
          }
        }
      }

      // Check measurement_conditions_multiple array
      if (isMll && measurement.measurementConditionsMultiple != null) {
        for (final condition in measurement.measurementConditionsMultiple!) {
          if (condition.measurementId?.toInt() == measurementId &&
              condition.actionMeasurementId != null) {
            dependentMeasurementIds.add(condition.actionMeasurementId!.toInt());
          }
        }
      }
    }

    // Recursively hide dependent widgets
    for (final dependentId in dependentMeasurementIds) {
      if (visibility[dependentId] == true) {
        _hideWidgetAndCascade(dependentId, allMeasurements, visibility, isMll);
      }
    }
  }

  /// Check if a measurement is a target of any 'appear' action
  bool _isTargetOfAppearAction(
      int measurementId, List<Measurement> measurements) {
    for (final measurement in measurements) {
      // Check measurement_conditions array
      final conditions = measurement.measurementConditions ?? [];
      for (final condition in conditions) {
        if (condition.actionMeasurementId?.toInt() == measurementId &&
            condition.action?.toLowerCase() == 'appear') {
          return true;
        }
      }

      // Check measurement_conditions_multiple array
      final conditionsMultiple =
          measurement.measurementConditionsMultiple ?? [];
      for (final condition in conditionsMultiple) {
        if (condition.actionMeasurementId?.toInt() == measurementId &&
            condition.action?.toLowerCase() == 'appear') {
          return true;
        }
      }
    }
    return false;
  }
}
