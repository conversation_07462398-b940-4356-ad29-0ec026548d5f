import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../entities/tasks_request_entity.dart';
import '../entities/tasks_response_entity.dart';
import '../../data/repositories/home_repository.dart';

class GetTasksUseCase
    implements UseCase<Result<TasksResponseEntity>, TasksRequestEntity> {
  final HomeRepository _repository;
  GetTasksUseCase(this._repository);

  @override
  Future<Result<TasksResponseEntity>> call(
    TasksRequestEntity request, {
    bool isSync = false,
  }) async {
    return _repository.getTasks(request, isSync: isSync);
  }
}
