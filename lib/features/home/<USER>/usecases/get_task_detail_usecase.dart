import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/shared/models/result.dart';

class GetTaskDetailUseCase {
  final HomeRepository repository;

  GetTaskDetailUseCase(this.repository);

  Future<Result<TaskDetail>> call(int taskId) async {
    return await repository.getTaskDetail(taskId);
  }
}
