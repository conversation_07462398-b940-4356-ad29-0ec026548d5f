import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../entities/tasks_request_entity.dart';
import '../entities/previous_tasks_response_entity.dart';
import '../../data/repositories/home_repository.dart';

class GetPreviousTasksUseCase
    implements
        UseCase<Result<PreviousTasksResponseEntity>,
            PreviousTasksRequestEntity> {
  final HomeRepository _repository;
  GetPreviousTasksUseCase(this._repository);

  @override
  Future<Result<PreviousTasksResponseEntity>> call(
      PreviousTasksRequestEntity request) async {
    return _repository.getPreviousTasks(request);
  }
}
