import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/notification_req.dart';
import '../../data/models/notification_response.dart';
import '../../data/repositories/home_repository.dart';

/// UseCase for getting notification
class GetNotificationUseCase
    implements UseCase<Result<NotificationResponse>, NotificationReqParams> {
  /// Constructor
  GetNotificationUseCase(this._repository);

  final HomeRepository _repository;

  @override
  Future<Result<NotificationResponse>> call(
      NotificationReqParams request) async {
    return _repository.getAlerts(request);
  }
}
