import '../../../../shared/models/result.dart';
import '../../data/repositories/home_repository.dart';

class DeleteLeaveUseCase {
  final HomeRepository _repository;

  DeleteLeaveUseCase(this._repository);

  Future<Result<bool>> call({
    required String token,
    required String userId,
    required List<int> leaveIds,
  }) async {
    return await _repository.deleteLeave(
      token: token,
      userId: userId,
      leaveIds: leaveIds,
    );
  }
}
