import '../../../../shared/models/result.dart';
import '../../data/repositories/home_repository.dart';

class AddLeaveUseCase {
  final HomeRepository _repository;

  AddLeaveUseCase(this._repository);

  Future<Result<bool>> call({
    required String token,
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await _repository.addLeave(
      token: token,
      userId: userId,
      startDate: startDate,
      endDate: endDate,
    );
  }
}
