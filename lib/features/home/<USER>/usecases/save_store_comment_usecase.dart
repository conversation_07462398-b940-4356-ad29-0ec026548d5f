import 'package:storetrack_app/features/home/<USER>/models/store_comment_model.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/features/home/<USER>/entities/store_comment_entity.dart';
import 'package:storetrack_app/shared/models/result.dart';

class SaveStoreCommentUseCase {
  final HomeRepository repository;

  SaveStoreCommentUseCase(this.repository);

  Future<Result<bool>> call({
    required StoreCommentRequestEntity requestEntity,
  }) async {
    final request = StoreCommentRequest.fromEntity(requestEntity);
    return await repository.saveStoreComment(request: request);
  }
}
