import '../../../../shared/models/result.dart';
import '../../data/models/profile_response.dart';
import '../../data/repositories/home_repository.dart';

class GetProfileUseCase {
  final HomeRepository _repository;

  GetProfileUseCase(this._repository);

  Future<Result<ProfileResponse>> call({
    required String token,
    required String userId,
  }) async {
    return await _repository.getProfile(
      token: token,
      userId: userId,
    );
  }
}
