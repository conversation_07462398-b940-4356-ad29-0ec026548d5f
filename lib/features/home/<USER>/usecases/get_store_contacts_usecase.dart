import 'package:storetrack_app/shared/models/result.dart';
import '../../data/models/store_contact_model.dart';
import '../../data/repositories/home_repository.dart';

class GetStoreContactsUseCase {
  final HomeRepository repository;

  GetStoreContactsUseCase(this.repository);

  Future<Result<StoreContactResponse>> call({
    required String token,
    required String userId,
    required String storeId,
  }) async {
    return await repository.getStoreContacts(
      token: token,
      userId: userId,
      storeId: storeId,
    );
  }
}
