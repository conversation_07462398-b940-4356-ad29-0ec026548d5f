import 'package:storetrack_app/features/home/<USER>/models/store_comment_model.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/shared/models/result.dart';

class GetStoreCommentsUseCase {
  final HomeRepository repository;

  GetStoreCommentsUseCase(this.repository);

  Future<Result<StoreCommentsResponse>> call({
    required String taskId,
    required String userId,
    required String token,
  }) async {
    return await repository.getStoreComments(
      taskId: taskId,
      userId: userId,
      token: token,
    );
  }
}
