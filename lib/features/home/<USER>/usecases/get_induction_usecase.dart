import 'package:storetrack_app/shared/models/result.dart';
import '../../data/repositories/home_repository.dart';
import '../../data/models/induction_response.dart';

class GetInductionUseCase {
  final HomeRepository repository;

  GetInductionUseCase(this.repository);

  Future<Result<InductionResponse>> call({
    required String token,
    required String userId,
  }) async {
    return await repository.getInduction(
      token: token,
      userId: userId,
    );
  }
}
