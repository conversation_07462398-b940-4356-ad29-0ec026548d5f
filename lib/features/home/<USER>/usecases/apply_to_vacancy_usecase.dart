import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/shared/models/result.dart';

class ApplyToVacancyUseCase {
  final HomeRepository repository;

  ApplyToVacancyUseCase(this.repository);

  Future<Result<bool>> call({
    required String token,
    required String userId,
    required int vacancyId,
  }) async {
    return await repository.applyToVacancy(
      token: token,
      userId: userId,
      vacancyId: vacancyId,
    );
  }
}
