import 'package:storetrack_app/features/home/<USER>/entities/open_count_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/open_tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/open_count_repository.dart';
import 'package:storetrack_app/shared/models/result.dart';

class GetOpenCountUseCase {
  final OpenCountRepository repository;

  GetOpenCountUseCase(this.repository);

  Future<Result<OpenTaskResponseEntity>> call(
      OpenCountRequestEntity request) async {
    return await repository.getOpenCount(request);
  }
}
