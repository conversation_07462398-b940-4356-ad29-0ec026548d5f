import 'package:storetrack_app/shared/models/result.dart';
import '../../data/repositories/home_repository.dart';
import '../entities/pos_request_entity.dart';
import '../entities/pos_response_entity.dart';

class UpdatePosUseCase {
  final HomeRepository repository;

  UpdatePosUseCase(this.repository);

  Future<Result<PosResponseEntity>> call(UpdatePosRequestEntity request) async {
    return await repository.updatePos(request);
  }
}
