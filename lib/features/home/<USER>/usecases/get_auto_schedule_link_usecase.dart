import 'package:storetrack_app/core/usecase/usecase.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/shared/models/result.dart';

class GetAutoScheduleLinkUseCase
    extends UseCase<Result<String>, AutoScheduleLinkParams> {
  final HomeRepository repository;

  GetAutoScheduleLinkUseCase(this.repository);

  @override
  Future<Result<String>> call(AutoScheduleLinkParams params) async {
    return await repository.getAutoScheduleLink(
      token: params.token,
      userId: params.userId,
      dayOffset: params.dayOffset,
    );
  }
}

class AutoScheduleLinkParams {
  final String token;
  final String userId;
  final int dayOffset;

  AutoScheduleLinkParams({
    required this.token,
    required this.userId,
    this.dayOffset = 0,
  });
}
