import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../entities/checkin_request_entity.dart';
import '../entities/checkin_response_entity.dart';
import '../../data/repositories/home_repository.dart';

class SendCheckinUseCase
    implements UseCase<Result<CheckinResponseEntity>, CheckinRequestEntity> {
  final HomeRepository repository;

  SendCheckinUseCase(this.repository);

  @override
  Future<Result<CheckinResponseEntity>> call(
      CheckinRequestEntity params) async {
    return await repository.sendCheckin(params);
  }
}
