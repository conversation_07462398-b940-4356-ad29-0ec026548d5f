import 'package:storetrack_app/shared/models/result.dart';
import '../../data/repositories/home_repository.dart';

class SaveSkillsUseCase {
  final HomeRepository repository;

  SaveSkillsUseCase(this.repository);

  Future<Result<bool>> call({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> skills,
  }) async {
    return await repository.saveSkills(
      token: token,
      userId: userId,
      skills: skills,
    );
  }
}
