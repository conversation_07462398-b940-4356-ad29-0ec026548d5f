import 'package:storetrack_app/shared/models/result.dart';
import '../../data/repositories/home_repository.dart';
import '../../data/models/useful_links_response.dart';

class GetUsefulLinksUseCase {
  final HomeRepository repository;

  GetUsefulLinksUseCase(this.repository);

  Future<Result<UsefulLinksResponse>> call({
    required String token,
    required String userId,
  }) async {
    return await repository.getUsefulLinks(
      token: token,
      userId: userId,
    );
  }
}
