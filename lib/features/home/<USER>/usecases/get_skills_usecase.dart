import 'package:storetrack_app/shared/models/result.dart';
import '../../data/models/skills_response.dart';
import '../../data/repositories/home_repository.dart';

class GetSkillsUseCase {
  final HomeRepository repository;

  GetSkillsUseCase(this.repository);

  Future<Result<SkillsResponse>> call({
    required String token,
    required String userId,
  }) async {
    return await repository.getSkills(
      token: token,
      userId: userId,
    );
  }
}
