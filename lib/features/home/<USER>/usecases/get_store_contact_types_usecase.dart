import 'package:storetrack_app/shared/models/result.dart';
import '../../data/models/store_contact_model.dart';
import '../../data/repositories/home_repository.dart';

class GetStoreContactTypesUseCase {
  final HomeRepository repository;

  GetStoreContactTypesUseCase(this.repository);

  Future<Result<StoreContactTypesResponse>> call({
    required String token,
    required String userId,
    required String storeId,
  }) async {
    return await repository.getStoreContactTypes(
      token: token,
      userId: userId,
      storeId: storeId,
    );
  }
}
