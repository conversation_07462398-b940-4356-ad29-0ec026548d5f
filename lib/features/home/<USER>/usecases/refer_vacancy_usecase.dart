import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/shared/models/result.dart';

class ReferVacancyUseCase {
  final HomeRepository repository;

  ReferVacancyUseCase(this.repository);

  Future<Result<bool>> call({
    required String token,
    required String userId,
    required int vacancyId,
    required String refereeEmail,
  }) async {
    return await repository.referVacancy(
      token: token,
      userId: userId,
      vacancyId: vacancyId,
      refereeEmail: refereeEmail,
    );
  }

  Future<Result<bool>> callWithDetails({
    required String token,
    required String userId,
    required int vacancyId,
    required String name,
    required String email,
    required String phone,
    required String stateName,
    required String suburb,
    required String comment,
    required String preference,
  }) async {
    print('DEBUG: ReferVacancyUseCase.callWithDetails called');
    print(
        'DEBUG: Parameters - token: ${token.isNotEmpty ? "present" : "empty"}, userId: $userId, vacancyId: $vacancyId');

    final result = await repository.referVacancyWithDetails(
      token: token,
      userId: userId,
      vacancyId: vacancyId,
      name: name,
      email: email,
      phone: phone,
      stateName: stateName,
      suburb: suburb,
      comment: comment,
      preference: preference,
    );

    print('DEBUG: Repository result: ${result.isSuccess} - ${result.error}');
    return result;
  }
}
