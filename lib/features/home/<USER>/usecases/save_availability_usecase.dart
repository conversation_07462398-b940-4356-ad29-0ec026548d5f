import 'package:storetrack_app/shared/models/result.dart';
import '../../data/repositories/home_repository.dart';

class SaveAvailabilityUseCase {
  final HomeRepository repository;

  SaveAvailabilityUseCase(this.repository);

  Future<Result<bool>> call({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> days,
  }) async {
    return await repository.saveAvailability(
      token: token,
      userId: userId,
      days: days,
    );
  }
}
