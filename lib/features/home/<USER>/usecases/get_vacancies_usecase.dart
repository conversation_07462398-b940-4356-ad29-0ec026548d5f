import 'package:storetrack_app/features/home/<USER>/entities/vacancy_entity.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/shared/models/result.dart';

class GetVacanciesUseCase {
  final HomeRepository repository;

  GetVacanciesUseCase(this.repository);

  Future<Result<List<VacancyEntity>>> call({
    required String token,
    required String userId,
  }) async {
    return await repository.getVacancies(
      token: token,
      userId: userId,
    );
  }
}
