import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';

class PageIndicator extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final Function(int)? onPageTap;
  final Color activeColor;
  final Color inactiveColor;
  final double dotSize;
  final double spacing;
  final Color? inactiveBorderColor;
  final double borderWidth;

  const PageIndicator({
    super.key,
    required this.currentPage,
    required this.totalPages,
    this.onPageTap,
    this.activeColor = AppColors.primaryBlue,
    this.inactiveColor = AppColors.blackTint2,
    this.dotSize = 8.0,
    this.spacing = 8.0,
    this.inactiveBorderColor,
    this.borderWidth = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        totalPages,
        (index) => GestureDetector(
          onTap: onPageTap != null ? () => onPageTap!(index) : null,
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: spacing / 2),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: dotSize,
              height: dotSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index == currentPage ? activeColor : inactiveColor,
                border: index != currentPage && inactiveBorderColor != null
                    ? Border.all(
                        color: inactiveBorderColor!, width: borderWidth)
                    : null,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
