# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Flutter Commands
- `flutter pub get` - Install dependencies
- `flutter clean` - Clean build artifacts
- `flutter run` - Run the app in debug mode
- `flutter build apk` - Build Android APK
- `flutter build ios` - Build iOS app
- `flutter analyze` - Run static analysis
- `flutter test` - Run unit/widget tests

### Code Quality
- Use `flutter analyze` to check for linting issues
- Follow the lint rules defined in `analysis_options.yaml`
- Format code with `dart format .`

### Testing
- Run `flutter test` for unit and widget tests
- Test files are located in the `test/` directory
- Follow existing test patterns in `test/core/`, `test/features/`, etc.

## Architecture Overview

### Clean Architecture Structure
This app follows clean architecture with clear separation of concerns:

```
lib/
├── core/                 # Shared utilities and infrastructure
├── config/               # App configuration (routes, themes)
├── features/             # Feature modules
├── di/                   # Dependency injection
└── shared/              # Shared widgets and models
```

### Feature Module Structure
Each feature follows the same pattern:
```
features/{feature_name}/
├── data/                # Data layer (repositories, models, data sources)
├── domain/              # Business logic layer (entities, use cases)
└── presentation/        # UI layer (pages, widgets, cubits)
```

### Key Architectural Patterns

#### State Management
- **BLoC/Cubit**: Primary state management using `flutter_bloc`
- **State Classes**: Always use immutable state classes with copyWith methods
- **State Types**: Represent all states (initial, loading, success, error)

#### Dependency Injection
- **GetIt**: Service locator pattern in `di/service_locator.dart`
- **Registration**: All dependencies are registered during app initialization
- **Usage**: Access via `sl<Type>()` in presentation layer

#### Data Layer Patterns
- **Repository Pattern**: Abstract repositories in domain, implementations in data
- **Local Data Sources**: Realm database for offline storage
- **Remote Data Sources**: API client using Dio for network requests
- **Cache-First Strategy**: Load from local cache, then sync with server

### Core Technologies

#### Database & Storage
- **Realm**: Primary local database using `realm` package
- **Model Generation**: Realm models are generated (`.realm.dart` files)
- **Shared Preferences**: Simple key-value storage via `StorageService`

#### Networking
- **Dio**: HTTP client with interceptors and retry logic
- **API Client**: Centralized in `core/network/api_client.dart`
- **Token Management**: Automatic JWT token handling
- **Network Info**: Connection state monitoring

#### Navigation
- **Auto Route**: Declarative routing using `auto_route` package
- **Route Generation**: Routes defined in `config/routes/app_router.dart`
- **Generated Files**: `app_router.gr.dart` contains generated route classes

#### Authentication
- **MSAL**: Microsoft Authentication Library for OAuth2/OIDC
- **Token Storage**: Secure token management via `TokenManager`
- **JWT**: Custom JWT creation for API authentication

#### Sync System
- **Background Sync**: `SyncService` handles data synchronization
- **Process Core**: Complex server data processing via `ProcessCoreService`
- **Photo/Signature Sync**: Upload and download of media files
- **Conflict Resolution**: Server-first strategy for data conflicts

#### Services & Utilities
- **Location Service**: GPS tracking and location management
- **Camera Service**: Photo capture and image processing
- **Photo Service**: Image storage and optimization
- **Barcode Scanner**: QR/barcode scanning functionality

### Development Guidelines

#### Code Reuse & Consistency
- Always scan existing codebase before implementing new features
- Reuse existing Cubits, UseCases, widgets, and utilities
- Follow established naming conventions and folder structure
- Match visual language: layouts, spacing, typography, colors

#### State Management Best Practices
- Keep state classes immutable and expressive
- Use copyWith methods for state updates
- Separate UI logic from business logic (Cubits vs presentation)
- Handle all state transitions (loading, success, error)

#### Error Handling
- Use Result pattern for repository responses
- Handle network errors gracefully
- Provide meaningful error messages to users
- Log errors appropriately using `core/utils/logger.dart`

#### Testing Strategy
- Unit tests for Cubits and use cases
- Widget tests for UI components
- Integration tests for complete user flows
- Mock dependencies using GetIt overrides

### Form and Question System

#### Multi-Question Support
- Dynamic question parts based on answers
- Conditional logic engine for question flow
- Measurement validators for form inputs
- Question-answer repository for persistence

#### Photo & Signature Management
- Folder-based organization (PhotoFolder, SignatureFolder)
- Local and remote storage management
- Image optimization and thumbnails
- Upload status tracking

### Performance Considerations

#### Memory Management
- Use factory constructors for Cubits when appropriate
- Dispose of resources properly in widgets
- Optimize image loading and caching
- Monitor memory usage during photo operations

#### Database Optimization
- Use Realm queries efficiently
- Implement proper indexing for common queries
- Handle large datasets with pagination
- Use transactions for bulk operations

### Realm Database Schema

#### Key Models
- `TaskDetailModel`: Core task entity with relationships
- `FormModel`: Form structure and answers
- `PhotoModel`/`SignatureModel`: Media files with sync status
- `QuestionModel`/`MeasurementModel`: Form question definitions

#### Relationships
- Tasks contain multiple forms, photos, and signatures
- Forms contain questions and answers
- Photos/signatures are organized in folders
- Complex many-to-many relationships via intermediate models

### Sync and Data Processing

#### Process Core System
- Handles comprehensive server data updates
- Manages task deletion, form updates, photo sync
- Resolves conflicts between local and server data
- Maintains data integrity during complex operations

#### Background Processing
- Sequential sync operations to prevent conflicts
- Progress tracking for long-running operations
- Error recovery and retry mechanisms
- Offline capability with eventual consistency

### UI Patterns

#### Shared Widgets
- `CustomAppBar`: Consistent app bar across pages
- `LoadingWidget`: Standard loading indicators
- Various comment widgets for different data types
- Form input widgets with validation

#### Theme System
- Centralized theme configuration in `config/themes/`
- Consistent color scheme via `AppColors`
- Typography definitions in `AppTypography`
- Font management with Montserrat family

### Development Workflow

#### Before Starting Work
1. Run `flutter pub get` to ensure dependencies are updated
2. Check existing similar functionality in the codebase
3. Review the feature's Cubit/UseCase/Repository pattern
4. Understand data flow and state management needs

#### During Development
1. Follow clean architecture separation
2. Use existing widgets and styles when possible
3. Write unit tests for new business logic
4. Handle error states and edge cases

#### After Implementation
1. Run `flutter analyze` to check for issues
2. Execute `flutter test` to ensure tests pass
3. Verify UI consistency across different screens
4. Test offline/online sync scenarios where applicable

### Important Notes

#### Realm Model Generation
- After modifying Realm models, run `dart run realm generate`
- Generated files (`.realm.dart`) should be committed
- Schema changes may require database migration

#### Route Generation
- After modifying routes, run `dart run build_runner build`
- This updates `app_router.gr.dart` with new route definitions

#### State Management
- Always use Cubits for state management, not StatefulWidgets
- Emit new states rather than mutating existing ones
- Use BlocBuilder/BlocListener appropriately in widgets

#### API Integration
- All API calls should go through repository pattern
- Handle network errors and offline scenarios
- Use proper authentication headers via ApiClient

This codebase represents a mature, production-ready Flutter application with sophisticated offline capabilities, complex data synchronization, and a robust architecture suitable for enterprise field service management.