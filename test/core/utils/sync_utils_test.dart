import 'package:flutter_test/flutter_test.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/sync_utils.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';

void main() {
  group('SyncUtils Response Processing Tests', () {
    late RealmDatabase realmDatabase;

    setUp(() {
      realmDatabase = RealmDatabase.instance;
      // Clear any existing data
      realmDatabase.clearAllData();
    });

    tearDown(() {
      // Clean up after each test
      realmDatabase.clearAllData();
    });

    test('should process empty TasksResponse without errors', () async {
      // Arrange
      final emptyResponse = TasksResponseEntity(
        deleteTaskIds: [],
        updateTasksDocuments: [],
        updateTasksForms: [],
        updateTaskMembers: [],
        updateTasksTasks: [],
        updateTasksPhotos: [],
        addTasks: [],
        updateTasksSubmission: [],
        updateTasksSignatures: [],
      );

      // Act
      final result = await SyncUtils.processTasksResponse(emptyResponse);

      // Assert
      expect(result, isTrue);
    });

    test('should process add_tasks correctly', () async {
      // Arrange
      final newTask = TaskDetail(
        taskId: 123,
        projectId: 1,
        client: 'Test Client',
        storeName: 'Test Store',
        taskStatus: 'Confirmed',
        budget: 100,
      );

      final response = TasksResponseEntity(
        addTasks: [newTask],
      );

      // Act
      final result = await SyncUtils.processTasksResponse(response);

      // Assert
      expect(result, isTrue);

      // Verify task was added to database
      final realm = realmDatabase.realm;
      final savedTask = realm.query<TaskDetailModel>('taskId == \$0', [123]).firstOrNull;
      expect(savedTask, isNotNull);
      expect(savedTask!.client, equals('Test Client'));
      expect(savedTask.storeName, equals('Test Store'));
      expect(savedTask.taskStatus, equals('Confirmed'));
      expect(savedTask.budget, equals(100));
    });

    test('should process delete_task_ids correctly', () async {
      // Arrange - First add a task
      final realm = realmDatabase.realm;
      final taskModel = TaskDetailModel(123,
        taskId: 123,
        client: 'Test Client',
        storeName: 'Test Store',
      );
      
      realm.write(() {
        realm.add(taskModel);
      });

      // Verify task exists
      var existingTask = realm.query<TaskDetailModel>('taskId == \$0', [123]).firstOrNull;
      expect(existingTask, isNotNull);

      // Arrange response with delete instruction
      final response = TasksResponseEntity(
        deleteTaskIds: ['123'],
      );

      // Act
      final result = await SyncUtils.processTasksResponse(response);

      // Assert
      expect(result, isTrue);

      // Verify task was deleted
      existingTask = realm.query<TaskDetailModel>('taskId == \$0', [123]).firstOrNull;
      expect(existingTask, isNull);
    });

    test('should process update_task_members correctly', () async {
      // Arrange - First add a task
      final realm = realmDatabase.realm;
      final taskModel = TaskDetailModel(123,
        taskId: 123,
        client: 'Test Client',
      );
      
      realm.write(() {
        realm.add(taskModel);
      });

      // Arrange response with member updates
      final member = Taskmember(
        fullname: 'John Doe',
        email: '<EMAIL>',
        teamLead: 1,
        scheduleId: 456,
        taskId: 123,
      );

      final memberHelper = TaskMembersHelper(
        taskId: 123,
        taskMembers: [member],
      );

      final response = TasksResponseEntity(
        updateTaskMembers: [memberHelper],
      );

      // Act
      final result = await SyncUtils.processTasksResponse(response);

      // Assert
      expect(result, isTrue);

      // Verify member was added
      final updatedTask = realm.query<TaskDetailModel>('taskId == \$0', [123]).firstOrNull;
      expect(updatedTask, isNotNull);
      expect(updatedTask!.taskmembers.length, equals(1));
      expect(updatedTask.taskmembers.first.fullname, equals('John Doe'));
      expect(updatedTask.taskmembers.first.email, equals('<EMAIL>'));
    });

    test('should handle task status updates correctly', () async {
      // Arrange - First add a task
      final realm = realmDatabase.realm;
      final taskModel = TaskDetailModel(123,
        taskId: 123,
        client: 'Test Client',
        taskStatus: 'Confirmed',
        isOpen: true,
      );
      
      realm.write(() {
        realm.add(taskModel);
      });

      // Arrange response with task completion
      final updatedTask = TaskDetail(
        taskId: 123,
        taskStatus: 'Successful',
        budget: 150,
      );

      final response = TasksResponseEntity(
        updateTasksTasks: [updatedTask],
      );

      // Act
      final result = await SyncUtils.processTasksResponse(response);

      // Assert
      expect(result, isTrue);

      // Verify task status was updated
      final savedTask = realm.query<TaskDetailModel>('taskId == \$0', [123]).firstOrNull;
      expect(savedTask, isNotNull);
      expect(savedTask!.taskStatus, equals('Successful'));
      expect(savedTask.isOpen, isFalse); // Should be closed when successful
      expect(savedTask.syncPending, isFalse); // Should clear sync pending
      expect(savedTask.isSynced, isTrue); // Should mark as synced
      expect(savedTask.budget, equals(150));
    });
  });
}
