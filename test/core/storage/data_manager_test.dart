import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/storage/storage_service.dart';
import 'package:storetrack_app/features/auth/data/models/login_response.dart';

void main() {
  late DataManager dataManager;
  late StorageService storageService;

  setUp(() async {
    // Initialize SharedPreferences for testing
    SharedPreferences.setMockInitialValues({});
    final sharedPrefs = await SharedPreferences.getInstance();

    // Create the storage service and data manager
    storageService = StorageServiceImpl(sharedPrefs);
    dataManager = DataManagerImpl(storageService);
  });

  test('saveLoginResponse and getLoginResponse should work correctly',
      () async {
    // Create a test login response
    final loginResponse = LoginResponse(
      data: Data(
        userId: 123,
        token: 'test_token',
        isPriceCheckerUser: true,
        isAdminUniversal: false,
        dayCheckIn: true,
        pos: false,
        openTasks: true,
        vacancies: false,
        premAutoSchedule: true,
        premAvailability: false,
        premAutoSchedule4Weeks: true,
        watermarkImages: false,
      ),
    );

    // Save the login response
    await dataManager.saveLoginResponse(loginResponse);

    // Retrieve the login response
    final retrievedResponse = await dataManager.getLoginResponse();

    // Verify that the retrieved response matches the original
    expect(retrievedResponse?.data?.userId, equals(loginResponse.data?.userId));
    expect(retrievedResponse?.data?.token, equals(loginResponse.data?.token));
    expect(retrievedResponse?.data?.isPriceCheckerUser,
        equals(loginResponse.data?.isPriceCheckerUser));
    expect(retrievedResponse?.data?.isAdminUniversal,
        equals(loginResponse.data?.isAdminUniversal));
    expect(retrievedResponse?.data?.dayCheckIn,
        equals(loginResponse.data?.dayCheckIn));
    expect(retrievedResponse?.data?.pos, equals(loginResponse.data?.pos));
    expect(retrievedResponse?.data?.openTasks,
        equals(loginResponse.data?.openTasks));
    expect(retrievedResponse?.data?.vacancies,
        equals(loginResponse.data?.vacancies));
    expect(retrievedResponse?.data?.premAutoSchedule,
        equals(loginResponse.data?.premAutoSchedule));
    expect(retrievedResponse?.data?.premAvailability,
        equals(loginResponse.data?.premAvailability));
    expect(retrievedResponse?.data?.premAutoSchedule4Weeks,
        equals(loginResponse.data?.premAutoSchedule4Weeks));
    expect(retrievedResponse?.data?.watermarkImages,
        equals(loginResponse.data?.watermarkImages));
  });

  test('saveTaskOrder and getTaskOrder should work correctly', () async {
    // Create a test task order
    final taskOrder = ['task_1', 'task_2', 'task_3', 'task_4'];

    // Save the task order
    await dataManager.saveTaskOrder(taskOrder);

    // Retrieve the task order
    final retrievedOrder = await dataManager.getTaskOrder();

    // Verify that the retrieved order matches the original
    expect(retrievedOrder, equals(taskOrder));
    expect(retrievedOrder?.length, equals(4));
    expect(retrievedOrder?[0], equals('task_1'));
    expect(retrievedOrder?[1], equals('task_2'));
    expect(retrievedOrder?[2], equals('task_3'));
    expect(retrievedOrder?[3], equals('task_4'));
  });

  test('getTaskOrder should return null when no order is saved', () async {
    // Clear all data first
    await dataManager.clearAll();

    // Try to retrieve task order when none exists
    final retrievedOrder = await dataManager.getTaskOrder();

    // Verify that null is returned
    expect(retrievedOrder, isNull);
  });

  test('saveTaskOrder should handle empty list', () async {
    // Save an empty task order
    await dataManager.saveTaskOrder([]);

    // Retrieve the task order
    final retrievedOrder = await dataManager.getTaskOrder();

    // Verify that an empty list is returned
    expect(retrievedOrder, equals([]));
    expect(retrievedOrder?.length, equals(0));
  });
}
