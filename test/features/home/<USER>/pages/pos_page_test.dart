import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/pages/pos_page.dart';

void main() {
  group('PosPage Tests', () {
    testWidgets('PosPage should build without errors with null task',
        (WidgetTester tester) async {
      // Build the widget with null task
      await tester.pumpWidget(
        const MaterialApp(
          home: PosPage(
            task: null,
          ),
        ),
      );

      // Verify that the widget builds and shows the default connote number
      expect(find.text('CON123456789'), findsOneWidget);
      expect(find.text('POS Details'), findsOneWidget);
      expect(find.text('Tracking'), findsOneWidget);
      expect(find.text('Receive All'), findsOneWidget);
      expect(find.text('Not Received'), findsOneWidget);
      expect(find.text('Details: '), findsOneWidget);
      expect(find.text('Item'), findsOneWidget);
      expect(find.text('Number'), findsOneWidget);
      expect(find.text('Photo'), findsOneWidget);
    });

    testWidgets('PosPage should display sample data when no task is provided',
        (WidgetTester tester) async {
      // Build the widget with null task
      await tester.pumpWidget(
        const MaterialApp(
          home: PosPage(
            task: null,
          ),
        ),
      );

      // Verify that sample data is displayed
      expect(find.text('CON123456789'), findsOneWidget);
      expect(find.text('Sample Item 1'), findsOneWidget);
      expect(find.text('Sample Item 2'), findsOneWidget);
      expect(find.text('Sample Item 3'), findsOneWidget);
    });

    testWidgets('PosPage should display task data when task is provided',
        (WidgetTester tester) async {
      // Create a test task with POS items
      final testTask = TaskDetail(
        taskId: 123,
        connoteUrl: 'TEST_CONNOTE_456',
        posItems: [
          PosItem(itemName: 'Test Item 1', itemAmount: 10, photoUrl: null),
          PosItem(itemName: 'Test Item 2', itemAmount: 5, photoUrl: null),
        ],
      );

      // Build the widget with test task
      await tester.pumpWidget(
        MaterialApp(
          home: PosPage(
            task: testTask,
          ),
        ),
      );

      // Verify that task data is displayed
      expect(find.text('TEST_CONNOTE_456'), findsOneWidget);
      expect(find.text('Test Item 1'), findsOneWidget);
      expect(find.text('Test Item 2'), findsOneWidget);
      expect(find.text('10'), findsOneWidget);
      expect(find.text('5'), findsOneWidget);
    });

    testWidgets('PosPage should show dialog when task has no POS items',
        (WidgetTester tester) async {
      // Create a test task with no POS items
      final testTask = TaskDetail(
        taskId: 123,
        connoteUrl: 'TEST_CONNOTE_789',
        posItems: [],
      );

      // Build the widget with test task
      await tester.pumpWidget(
        MaterialApp(
          home: PosPage(
            task: testTask,
          ),
        ),
      );

      // Wait for the dialog to appear
      await tester.pumpAndSettle();

      // Verify that the dialog is displayed
      expect(find.text('No POS Items Found'), findsOneWidget);
      expect(find.text('Please try again later.'), findsOneWidget);
      expect(find.text('OK'), findsOneWidget);
    });

    testWidgets('PosPage buttons should be tappable',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        const MaterialApp(
          home: PosPage(
            task: null,
          ),
        ),
      );

      // Wait for the widget to be fully rendered
      await tester.pumpAndSettle();

      // Find and tap the tracking button
      final trackingButton = find.text('Tracking');
      expect(trackingButton, findsOneWidget);
      await tester.tap(trackingButton);
      await tester.pump();

      // Find AppButton widgets instead of text
      final appButtons = find.byType(AppButton);
      expect(appButtons, findsNWidgets(2)); // Should find 2 AppButton widgets

      // Tap the first AppButton (Receive All)
      await tester.tap(appButtons.first);
      await tester.pumpAndSettle(); // Wait for any async operations

      // Tap the second AppButton (Not Received)
      await tester.tap(appButtons.last);
      await tester.pumpAndSettle(); // Wait for any async operations

      // No exceptions should be thrown
    });

    testWidgets('PosPage should update button colors based on status',
        (WidgetTester tester) async {
      // Create a test task with POS items
      final testTask = TaskDetail(
        taskId: 123,
        connoteUrl: 'TEST_CONNOTE_STATUS',
        posItems: [
          PosItem(itemName: 'Test Item', itemAmount: 1, photoUrl: null),
        ],
        posReceived: 'true', // Initially received
      );

      // Build the widget with test task
      await tester.pumpWidget(
        MaterialApp(
          home: PosPage(
            task: testTask,
          ),
        ),
      );

      // Verify initial state shows received status
      expect(find.text('TEST_CONNOTE_STATUS'), findsOneWidget);
      expect(find.text('Test Item'), findsOneWidget);

      // Tap the "Not Received" button
      final notReceivedButton = find.text('Not Received');
      expect(notReceivedButton, findsOneWidget);
      await tester.tap(notReceivedButton);
      await tester.pumpAndSettle();

      // Verify the status changed
      expect(find.text('POS marked as not received'), findsOneWidget);
    });
  });
}
