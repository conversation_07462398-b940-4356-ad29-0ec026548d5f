import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/multi_select_widget.dart';

void main() {
  group('MultiSelectWidget Tests', () {
    late Measurement testMeasurement;
    late List<String> testValue;
    late Function(List<String>) testOnChanged;

    setUp(() {
      // Create test measurement with options
      testMeasurement = Measurement(
        measurementId: 1,
        measurementTypeId: 6, // Multi-select type
        measurementDescription: 'Test Multi-Select',
        measurementOptions: [
          MeasurementOption(
            measurementOptionId: 1,
            measurementOptionDescription: 'Option 1',
          ),
          MeasurementOption(
            measurementOptionId: 2,
            measurementOptionDescription: 'Option 2',
          ),
          MeasurementOption(
            measurementOptionId: 3,
            measurementOptionDescription: 'Option 3',
          ),
        ],
      );

      testValue = [];
      testOnChanged = (List<String> value) {
        testValue = value;
      };
    });

    testWidgets('MultiSelectWidget should build without errors',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiSelectWidget(
              measurement: testMeasurement,
              value: const [],
              onChanged: testOnChanged,
            ),
          ),
        ),
      );

      // Verify that the widget builds
      expect(find.byType(MultiSelectWidget), findsOneWidget);
      expect(find.text('Test Multi-Select'), findsOneWidget);
      expect(find.text('Select...'), findsOneWidget);
    });

    testWidgets('MultiSelectWidget should display selected option descriptions',
        (WidgetTester tester) async {
      // Test with pre-selected option IDs
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiSelectWidget(
              measurement: testMeasurement,
              value: const ['1', '3'], // Option IDs as strings
              onChanged: testOnChanged,
            ),
          ),
        ),
      );

      // Verify that selected descriptions are displayed
      expect(find.text('Option 1, Option 3'), findsOneWidget);
      expect(find.text('2 option(s) selected'), findsOneWidget);
    });

    testWidgets('MultiSelectWidget should open bottom sheet when tapped',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiSelectWidget(
              measurement: testMeasurement,
              value: const [],
              onChanged: testOnChanged,
            ),
          ),
        ),
      );

      // Tap on the widget to open bottom sheet
      await tester.tap(find.text('Select...'));
      await tester.pumpAndSettle();

      // Verify that bottom sheet is displayed
      expect(find.text('Test Multi-Select'),
          findsNWidgets(2)); // Title appears twice
      expect(find.text('Option 1'), findsOneWidget);
      expect(find.text('Option 2'), findsOneWidget);
      expect(find.text('Option 3'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Confirm'), findsOneWidget);
    });

    testWidgets('MultiSelectWidget should handle option selection',
        (WidgetTester tester) async {
      List<String> capturedValue = [];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiSelectWidget(
              measurement: testMeasurement,
              value: const [],
              onChanged: (value) {
                capturedValue = value;
              },
            ),
          ),
        ),
      );

      // Open bottom sheet
      await tester.tap(find.text('Select...'));
      await tester.pumpAndSettle();

      // Select Option 1
      await tester.tap(find.text('Option 1'));
      await tester.pumpAndSettle();

      // Select Option 3
      await tester.tap(find.text('Option 3'));
      await tester.pumpAndSettle();

      // Confirm selection
      await tester.tap(find.text('Confirm'));
      await tester.pumpAndSettle();

      // Verify that the correct option IDs were passed to onChanged
      expect(capturedValue, contains('1'));
      expect(capturedValue, contains('3'));
      expect(capturedValue.length, equals(2));
    });

    testWidgets(
        'MultiSelectWidget should show required indicator when required',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiSelectWidget(
              measurement: testMeasurement,
              value: const [],
              onChanged: testOnChanged,
              isRequired: true,
            ),
          ),
        ),
      );

      // Verify that required indicator is shown
      expect(find.byIcon(Icons.priority_high), findsOneWidget);
    });

    testWidgets('MultiSelectWidget should display error text when provided',
        (WidgetTester tester) async {
      const errorMessage = 'This field is required';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiSelectWidget(
              measurement: testMeasurement,
              value: const [],
              onChanged: testOnChanged,
              errorText: errorMessage,
            ),
          ),
        ),
      );

      // Verify that error text is displayed
      expect(find.text(errorMessage), findsOneWidget);
    });
  });
}
