import 'package:flutter_test/flutter_test.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/validators/measurement_validator.dart';

void main() {
  late MeasurementValidator validator;

  setUp(() {
    validator = MeasurementValidator();
  });

  group('MeasurementValidator', () {
    test('should return null for valid text field', () {
      final measurement = Measurement(
        measurementId: 1,
        measurementTypeId: 1, // Text field
        required: true,
        measurementDescription: 'Test field',
      );

      final result = validator.validate(measurement, 'Valid text');
      expect(result, isNull);
    });

    test('should return error for required field with empty value', () {
      final measurement = Measurement(
        measurementId: 1,
        measurementTypeId: 1, // Text field
        required: true,
        measurementDescription: 'Required field',
      );

      final result = validator.validate(measurement, '');
      expect(result, isNotNull);
      expect(result, contains('Required field is required'));
    });

    test('should validate checkbox value correctly', () {
      final measurement = Measurement(
        measurementId: 1,
        measurementTypeId: 3, // Checkbox
        required: true,
        measurementDescription: 'Test checkbox',
      );

      final resultTrue = validator.validate(measurement, true);
      expect(resultTrue, isNull);

      final resultFalse = validator.validate(measurement, false);
      expect(resultFalse, isNull);
    });

    test('should validate counter value with range', () {
      final measurement = Measurement(
        measurementId: 1,
        measurementTypeId: 7, // Counter
        required: true,
        measurementDescription: 'Test counter',
        rangeValidation: '1,10', // Format: min,max
      );

      // Valid value
      final validResult = validator.validate(measurement, 5);
      expect(validResult, isNull);

      // Below minimum
      final belowMinResult = validator.validate(measurement, 0);
      expect(belowMinResult, isNotNull);
      expect(belowMinResult, contains('must be at least'));

      // Above maximum
      final aboveMaxResult = validator.validate(measurement, 15);
      expect(aboveMaxResult, isNotNull);
      expect(
          aboveMaxResult,
          contains(
              'must be at least')); // The message includes both min and max
    });

    test('should check if value is empty correctly', () {
      final textMeasurement = Measurement(
        measurementId: 1,
        measurementTypeId: 1, // Text field
      );

      expect(validator.isEmpty(textMeasurement, ''), isTrue);
      expect(validator.isEmpty(textMeasurement, null), isTrue);
      expect(validator.isEmpty(textMeasurement, 'text'), isFalse);

      final listMeasurement = Measurement(
        measurementId: 2,
        measurementTypeId: 6, // Multi-select
      );

      expect(validator.isEmpty(listMeasurement, []), isTrue);
      expect(validator.isEmpty(listMeasurement, null), isTrue);
      expect(validator.isEmpty(listMeasurement, ['item']), isFalse);
    });

    test('should validate photo requirements', () {
      final measurement = Measurement(
        measurementId: 1,
        measurementTypeId: 1, // Text field
        required: true,
        measurementDescription: 'Test field',
        mandatoryPhototypesCount: 1, // Requires 1 photo
      );

      // Valid with photos
      final resultWithPhotos = validator.validate(
        measurement,
        'Some text',
        photos: ['photo1.jpg'],
      );
      expect(resultWithPhotos, isNull);

      // Invalid without photos
      final resultWithoutPhotos = validator.validate(
        measurement,
        'Some text',
        photos: [],
      );
      expect(resultWithoutPhotos, isNotNull);
      expect(resultWithoutPhotos, contains('photo is required'));
    });
  });
}
