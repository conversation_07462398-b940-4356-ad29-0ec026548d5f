import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:storetrack_app/shared/widgets/html_text_widget.dart';

void main() {
  group('HtmlTextWidget', () {
    testWidgets('renders plain text when no HTML tags',
        (WidgetTester tester) async {
      const plainText = 'This is plain text without HTML';

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: HtmlTextWidget(
              text: plainText,
            ),
          ),
        ),
      );

      expect(find.text(plainText), findsOneWidget);
    });

    testWidgets('renders HTML content when HTML tags are present',
        (WidgetTester tester) async {
      const htmlText = '<p>This is <strong>bold</strong> text</p>';

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: HtmlTextWidget(
              text: htmlText,
            ),
          ),
        ),
      );

      // The HTML widget should be present (we can't easily test the rendered HTML content)
      expect(find.byType(HtmlTextWidget), findsOneWidget);
    });

    testWidgets('returns empty widget when text is null',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: HtmlTextWidget(
              text: null,
            ),
          ),
        ),
      );

      expect(find.byType(SizedBox), findsOneWidget);
    });

    testWidgets('returns empty widget when text is empty',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: HtmlTextWidget(
              text: '',
            ),
          ),
        ),
      );

      expect(find.byType(SizedBox), findsOneWidget);
    });

    testWidgets('applies custom text style to plain text',
        (WidgetTester tester) async {
      const plainText = 'Styled plain text';
      const customStyle = TextStyle(fontSize: 20, color: Colors.red);

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: HtmlTextWidget(
              text: plainText,
              style: customStyle,
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.text(plainText));
      expect(textWidget.style?.fontSize, equals(20));
      expect(textWidget.style?.color, equals(Colors.red));
    });
  });
}
