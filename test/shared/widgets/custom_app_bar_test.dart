import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/config/themes/app_theme.dart';

void main() {
  group('CustomAppBar', () {
    testWidgets('should display title correctly', (WidgetTester tester) async {
      const title = 'Test Title';

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.light,
          home: Scaffold(
            appBar: const CustomAppBar(
              title: title,
              checkNetworkConnectivity: false, // Disable network check in tests
            ),
            body: Container(),
          ),
        ),
      );

      expect(find.text(title), findsOneWidget);
    });

    testWidgets('should show offline indicator when isOffline is true',
        (WidgetTester tester) async {
      const title = 'Test Title';

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.light,
          home: Scaffold(
            appBar: const CustomAppBar(
              title: title,
              isOffline: true,
              checkNetworkConnectivity: false, // Disable network check in tests
            ),
            body: Container(),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      expect(find.text('Working offline'), findsOneWidget);
      expect(find.text(title), findsOneWidget);
    });

    testWidgets('should not show offline indicator when isOffline is false',
        (WidgetTester tester) async {
      const title = 'Test Title';

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.light,
          home: Scaffold(
            appBar: const CustomAppBar(
              title: title,
              isOffline: false,
              checkNetworkConnectivity: false, // Disable network check in tests
            ),
            body: Container(),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      expect(find.text('Working offline'), findsNothing);
      expect(find.text(title), findsOneWidget);
    });

    testWidgets('should show back button when showBackButton is true',
        (WidgetTester tester) async {
      const title = 'Test Title';

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.light,
          home: Scaffold(
            appBar: const CustomAppBar(
              title: title,
              showBackButton: true,
              checkNetworkConnectivity: false, // Disable network check in tests
            ),
            body: Container(),
          ),
        ),
      );

      expect(find.byIcon(Icons.arrow_back_ios_new), findsOneWidget);
    });

    testWidgets('should not show back button when showBackButton is false',
        (WidgetTester tester) async {
      const title = 'Test Title';

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.light,
          home: Scaffold(
            appBar: const CustomAppBar(
              title: title,
              showBackButton: false,
              checkNetworkConnectivity: false, // Disable network check in tests
            ),
            body: Container(),
          ),
        ),
      );

      expect(find.byIcon(Icons.arrow_back_ios_new), findsNothing);
    });

    testWidgets('should call onBackPressed when back button is tapped',
        (WidgetTester tester) async {
      const title = 'Test Title';
      bool backPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.light,
          home: Scaffold(
            appBar: CustomAppBar(
              title: title,
              onBackPressed: () {
                backPressed = true;
              },
              checkNetworkConnectivity: false, // Disable network check in tests
            ),
            body: Container(),
          ),
        ),
      );

      await tester.tap(find.byIcon(Icons.arrow_back_ios_new));
      await tester.pumpAndSettle();

      expect(backPressed, isTrue);
    });
  });
}
