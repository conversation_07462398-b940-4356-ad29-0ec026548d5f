# Process Core Implementation Plan

## Overview

This document outlines the implementation plan to port the Java `processCore` method from `process_core.md` into our Flutter app. The method handles comprehensive server data synchronization including task deletion, document updates, form updates, photo updates, signature updates, and various other task-related data.

## Analysis of Current Architecture

### Existing Flutter App Structure
- **Database**: Realm Flutter for local storage
- **Architecture**: Clean architecture with presentation→domain→data layers
- **Sync Pattern**: Sequential API calls with local database updates
- **Models**: Comprehensive Realm models for TaskDetailModel and related entities
- **Mappers**: TaskDetailMapper for entity↔model conversion
- **Repository**: HomeRepository with cache-first strategy

### Current Sync Implementation
- `SyncService` handles sequential photo/signature sync
- `HomeRepository` implements fetch-and-cache pattern
- Local data source saves to Realm database
- Existing patterns for JSON processing and model mapping

## Implementation Plan

### Phase 1: Core Infrastructure Setup

#### 1.1 Create ProcessCore Service
- Create `ProcessCoreService` in `lib/core/services/`
- Implement singleton pattern similar to `SyncService`
- Add dependency injection setup in `service_locator.dart`

#### 1.2 Create Data Models for Server Response
- Create `ProcessCoreResponseEntity` to handle the JSON structure
- Add models for each array type:
  - `DeleteTaskIdsEntity`
  - `UpdateTasksDocumentsEntity`
  - `UpdateTasksFormsEntity`
  - `UpdateTasksTasksEntity`
  - `UpdateTasksPhotosEntity`
  - `UpdateTasksSignaturesEntity`
  - `UpdateTasksSubmissionEntity`
  - `UpdatePhotoTypesEntity`
  - `AddPhotoTypesEntity`
  - `DeletePhotoTypesEntity`
  - `UpdateSignatureTypesEntity`
  - `AddTasksEntity`

#### 1.3 Create Mappers
- Create `ProcessCoreMapper` for converting server entities to Realm models
- Implement helper methods for nested object mapping
- Follow existing mapper patterns from `TaskDetailMapper`

### Phase 2: Core Processing Methods

#### 2.1 Task Deletion Handler
```dart
Future<void> _processDeletedTasks(List<String> taskIds)
```
- Query Realm for tasks by ID
- Remove task and all related data
- Handle cascade deletion for photos, signatures, forms, etc.

#### 2.2 Document Update Handler
```dart
Future<void> _processDocumentUpdates(List<UpdateTasksDocumentsEntity> updates)
```
- Find local tasks by task ID
- Compare modification dates
- Update document models and file references
- Handle local file cleanup

#### 2.3 Form Update Handler
```dart
Future<void> _processFormUpdates(List<UpdateTasksFormsEntity> updates)
```
- Update form structures without overwriting answers
- Handle form addition/deletion
- Update form metadata and question structures

#### 2.4 Task Information Update Handler
```dart
Future<void> _processTaskUpdates(List<UpdateTasksTasksEntity> updates)
```
- Update task metadata (client, store, schedule info)
- Handle budget calculations
- Update POS items and followup tasks
- Manage task status and completion states

### Phase 3: Photo and Signature Processing

#### 3.1 Photo Update Handler
```dart
Future<void> _processPhotoUpdates(List<UpdateTasksPhotosEntity> updates)
```
- Compare photo modification dates
- Update common photos from server
- Preserve unsynced local photos
- Handle photo deletion logic

#### 3.2 Photo Type Management
```dart
Future<void> _processPhotoTypeUpdates(List<UpdatePhotoTypesEntity> updates)
Future<void> _processPhotoTypeAdditions(List<AddPhotoTypesEntity> additions)
Future<void> _processPhotoTypeDeletions(List<DeletePhotoTypesEntity> deletions)
```
- Update photo folder properties
- Add new photo types as folders
- Delete photo types and associated photos

#### 3.3 Signature Processing
```dart
Future<void> _processSignatureUpdates(List<UpdateTasksSignaturesEntity> updates)
```
- Mirror photo processing logic for signatures
- Handle signature type updates/additions/deletions

### Phase 4: Submission and Multi-Question Processing

#### 4.1 Submission Update Handler
```dart
Future<void> _processSubmissionUpdates(List<UpdateTasksSubmissionEntity> updates)
```
- Update form answers and completion status
- Handle multi-question part rebuilding
- Update task completion and sync states

#### 4.2 Multi-Question Processing
```dart
Future<void> _buildQuestionPartMultiFromAnswers(FormModel form, QuestionModel question)
Future<void> _updateQuestionPartsMultiToDb(QuestionModel question, List<QuestionPartModel> newParts)
```
- Rebuild question parts from answers
- Update database with new question parts

### Phase 5: Integration and Error Handling

#### 5.1 Main ProcessCore Method
```dart
Future<void> processCore(Map<String, dynamic> dataObject)
```
- Orchestrate all processing steps in correct order
- Handle transaction management
- Implement comprehensive error handling
- Add logging and progress tracking

#### 5.2 Error Handling Strategy
- Wrap each processing step in try-catch blocks
- Continue processing other sections if one fails
- Log detailed error information
- Maintain data consistency

#### 5.3 Performance Optimization
- Use Realm transactions efficiently
- Batch database operations where possible
- Implement progress callbacks for UI updates
- Add memory management for large datasets

## Implementation Details

### Database Transaction Strategy
```dart
realm.write(() {
  // Batch all related operations
  _processDeletedTasks(deletedTaskIds);
  _processDocumentUpdates(documentUpdates);
  // ... other operations
});
```

### File Management
- Use existing file utility patterns
- Handle local file cleanup after server updates
- Implement proper error handling for file operations

### Sync Integration
- Integrate with existing `SyncService`
- Add process core call to sync workflow
- Maintain compatibility with current sync patterns

## Testing Strategy

### Unit Tests
- Test each processing method independently
- Mock Realm database operations
- Test error handling scenarios

### Integration Tests
- Test complete processCore workflow
- Test with real server response data
- Verify data consistency after processing

### Performance Tests
- Test with large datasets
- Monitor memory usage
- Verify transaction performance

## Migration Considerations

### Data Migration
- Ensure backward compatibility with existing data
- Handle schema changes gracefully
- Implement data validation

### API Integration
- Identify server endpoint that provides processCore data
- Integrate with existing API client
- Handle network error scenarios

## Updated Implementation Summary

### Key Changes Made
The ProcessCore implementation has been updated to use your existing model classes instead of raw maps:

#### 1. Method Signature Changes
- `processCore(TasksResponseEntity responseEntity)` - Now accepts TasksResponseEntity instead of Map
- All processing methods now accept typed lists instead of dynamic arrays

#### 2. Model Class Usage
- **delete_task_ids**: `List<String>` (unchanged)
- **update_tasks_documents**: `List<TaskDetail>` instead of `List<dynamic>`
- **update_tasks_forms**: `List<TaskDetail>` instead of `List<dynamic>`
- **update_task_members**: `List<TaskMembersHelper>` instead of `List<dynamic>`
- **update_tasks_tasks**: `List<TaskDetail>` instead of `List<dynamic>`
- **update_tasks_photos**: `List<TaskDetail>` instead of `List<dynamic>`
- **add_tasks**: `List<TaskDetail>` instead of `List<dynamic>`
- **update_tasks_submission**: `List<TaskDetail>` instead of `List<dynamic>`
- **update_tasks_signatures**: `List<TaskDetail>` instead of `List<dynamic>`

#### 3. Entity to Model Conversion
- Replaced map-based creation methods with entity-based methods
- Added proper type conversion (`.toInt()`, `.toDouble()`) for numeric fields
- Used existing `TaskDetailMapper.toModel()` for new task creation
- Maintained all existing field mappings and business logic

#### 4. Type Safety Improvements
- Eliminated manual map parsing and casting
- Added null safety checks for all entity properties
- Proper handling of optional fields with null coalescing

#### 5. Maintained Functionality
- All original Java logic preserved
- Same processing order maintained
- Error handling and logging unchanged
- Budget calculation logic intact
- Multi-question rebuilding logic preserved

### Usage Example
```dart
// Before (with maps)
await processCore(jsonDataObject);

// After (with typed entities)
final responseEntity = TasksResponseEntity.fromJson(jsonDataObject);
await processCore(responseEntity);
```

This refactoring provides better type safety, eliminates manual JSON parsing, and leverages your existing model infrastructure while maintaining all the original functionality.

## Dart Implementation

### ProcessCoreService Implementation

```dart
import 'dart:io';
import 'package:realm/realm.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';

/// Service for processing comprehensive server data updates
///
/// This service handles the core data synchronization logic equivalent
/// to the Java processCore method, including:
/// - Task deletion
/// - Document updates
/// - Form updates
/// - Photo and signature processing
/// - Task information updates
/// - Submission data processing
class ProcessCoreService {
  ProcessCoreService._internal();

  static final ProcessCoreService _instance = ProcessCoreService._internal();
  factory ProcessCoreService() => _instance;

  final RealmDatabase _realmDatabase = sl<RealmDatabase>();

  /// Main processing method that handles server data updates
  ///
  /// [responseEntity] - TasksResponseEntity containing all update arrays from server
  Future<void> processCore(TasksResponseEntity responseEntity) async {
    logger('🔄 Starting processCore data synchronization');

    final List<String> taskIDsToCalculateBudget = [];
    final Set<String> tmpTaskIDsToBeDeleted = {};

    try {
      await _realmDatabase.realm.write(() async {
        // Process in the same order as Java implementation

        // (11-10) Process tasks to be deleted
        await _processDeletedTasks(responseEntity.deleteTaskIds, tmpTaskIDsToBeDeleted);

        // (11-2) Update task documents data
        await _processDocumentUpdates(responseEntity.updateTasksDocuments);

        // (11-3) Update tasks forms data
        await _processFormUpdates(responseEntity.updateTasksForms);

        // (11-6) Update task members
        await _processTaskMemberUpdates(responseEntity.updateTaskMembers);

        // (11-6) Update tasks information data
        await _processTaskInformationUpdates(responseEntity.updateTasksTasks, taskIDsToCalculateBudget);

        // (11-4) Update tasks photos data
        await _processPhotoUpdates(responseEntity.updateTasksPhotos);

        // Photo type management (11-7, 11-8, 11-9)
        // TODO: Add photo type management methods when photo type entities are available
        // await _processPhotoTypeUpdates(responseEntity.updatePhotoTypes);
        // await _processPhotoTypeAdditions(responseEntity.addPhotoTypes);
        // await _processPhotoTypeDeletions(responseEntity.deletePhotoTypes);

        // (11-1) Add new tasks
        await _processNewTasks(responseEntity.addTasks, taskIDsToCalculateBudget);

        // (11-5) Update task submission data (moved to end as per comment)
        await _processSubmissionUpdates(responseEntity.updateTasksSubmission, taskIDsToCalculateBudget);

        // Signature processing
        await _processSignatureUpdates(responseEntity.updateTasksSignatures);
        // TODO: Add signature type management when signature type entities are available
        // await _processSignatureTypeUpdates(responseEntity.updateSignatureTypes);
      });

      // Post-processing budget calculations
      await _calculateBudgetsForTasks(taskIDsToCalculateBudget);

      logger('✅ ProcessCore completed successfully');

    } catch (e) {
      logger('❌ Error in processCore: $e');
      rethrow;
    }
  }

  /// Process tasks to be deleted and record the IDs
  Future<void> _processDeletedTasks(
    List<String>? deleteTaskIds,
    Set<String> tmpTaskIDsToBeDeleted,
  ) async {
    if (deleteTaskIds == null || deleteTaskIds.isEmpty) return;

    logger('🗑️ Processing ${deleteTaskIds.length} tasks for deletion');

    for (final taskID in deleteTaskIds) {
      if (taskID.isEmpty) continue;

      tmpTaskIDsToBeDeleted.add(taskID);

      final localTask = _realmDatabase.realm
          .query<TaskDetailModel>('taskId == \$0', [int.tryParse(taskID)])
          .firstOrNull;

      if (localTask != null) {
        await _removeTaskDetailModelFromRealm(localTask);
        logger('🗑️ Deleted task: $taskID');
      }
    }
  }

  /// Update task documents data
  Future<void> _processDocumentUpdates(List<TaskDetail>? updateTasksDocuments) async {
    if (updateTasksDocuments == null || updateTasksDocuments.isEmpty) return;

    logger('📄 Processing ${updateTasksDocuments.length} document updates');

    for (final taskDetail in updateTasksDocuments) {
      try {
        final serverTaskId = taskDetail.taskId?.toString();
        if (serverTaskId == null) continue;

        final localTask = _realmDatabase.realm
            .query<TaskDetailModel>('taskId == \$0', [taskDetail.taskId?.toInt()])
            .firstOrNull;

        if (localTask != null) {
          await _updateTaskDocuments(localTask, taskDetail);
        }
      } catch (e) {
        logger('❌ Error processing document update: $e');
      }
    }
  }

  /// Update tasks forms data
  Future<void> _processFormUpdates(List<TaskDetail>? updateTasksForms) async {
    if (updateTasksForms == null || updateTasksForms.isEmpty) return;

    logger('📝 Processing ${updateTasksForms.length} form updates');

    for (final taskDetail in updateTasksForms) {
      try {
        final serverTaskId = taskDetail.taskId?.toString();
        if (serverTaskId == null) continue;

        final localTask = _realmDatabase.realm
            .query<TaskDetailModel>('taskId == \$0', [taskDetail.taskId?.toInt()])
            .firstOrNull;

        if (localTask != null) {
          await _updateTaskForms(localTask, taskDetail);
        }
      } catch (e) {
        logger('❌ Error processing form update: $e');
      }
    }
  }

  /// Helper method to remove task and all related data
  Future<void> _removeTaskDetailModelFromRealm(TaskDetailModel task) async {
    // Remove all related data first
    for (final photoFolder in task.photoFolder) {
      for (final photo in photoFolder.photos) {
        await _deleteLocalFile(photo.localPath);
      }
    }

    for (final signatureFolder in task.signatureFolder) {
      for (final signature in signatureFolder.signatures) {
        await _deleteLocalFile(signature.localPath);
      }
    }

    // Delete the task (Realm will handle cascade deletion)
    _realmDatabase.realm.delete(task);
  }

  /// Helper method to delete local files safely
  Future<void> _deleteLocalFile(String? filePath) async {
    if (filePath == null || filePath.isEmpty) return;

    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      logger('⚠️ Error deleting local file $filePath: $e');
    }
  }

  /// Update task documents with server data
  Future<void> _updateTaskDocuments(
    TaskDetailModel localTask,
    TaskDetail serverTaskDetail,
  ) async {
    // Clear existing documents
    localTask.documents.clear();

    // Add updated documents from server task detail
    if (serverTaskDetail.documents != null) {
      for (final docEntity in serverTaskDetail.documents!) {
        final document = _createDocumentFromEntity(docEntity);
        localTask.documents.add(document);
      }
    }

    // Update modification timestamp
    if (serverTaskDetail.modifiedTimeStampDocuments != null) {
      localTask.modifiedTimeStampDocuments = serverTaskDetail.modifiedTimeStampDocuments;
    }
  }

  /// Create DocumentModel from server entity
  DocumentModel _createDocumentFromEntity(Document docEntity) {
    final document = DocumentModel();
    document.projectId = docEntity.projectId?.toInt();
    document.documentId = docEntity.documentId?.toInt();
    document.documentTypeId = docEntity.documentTypeId?.toInt();
    document.documentName = docEntity.documentName;
    document.documentIconLink = docEntity.documentIconLink;
    document.modifiedTimeStampDocument = docEntity.modifiedTimeStampDocument;

    if (docEntity.files != null) {
      for (final fileEntity in docEntity.files!) {
        final fileElement = _createFileElementFromEntity(fileEntity);
        document.files.add(fileElement);
      }
    }

    return document;
  }

  /// Create FileElementModel from server entity
  FileElementModel _createFileElementFromEntity(FileElement fileEntity) {
    final fileElement = FileElementModel();
    fileElement.documentId = fileEntity.documentId?.toInt();
    fileElement.projectId = fileEntity.projectId?.toInt();
    fileElement.documentFileLink = fileEntity.documentFileLink;
    fileElement.fileId = fileEntity.fileId?.toInt();
    fileElement.modifiedTimeStampFile = fileEntity.modifiedTimeStampFile;

    return fileElement;
  }

  /// Update task forms with server data
  Future<void> _updateTaskForms(
    TaskDetailModel localTask,
    TaskDetail serverTaskDetail,
  ) async {
    // Update form counts
    localTask.ctFormsTotalCnt = serverTaskDetail.ctFormsTotalCnt?.toInt();
    localTask.ctFormsCompletedCnt = serverTaskDetail.ctFormsCompletedCnt?.toInt();

    if (serverTaskDetail.forms == null) return;

    for (final formEntity in serverTaskDetail.forms!) {
      final formId = formEntity.formId?.toInt();
      if (formId == null) continue;

      // Find existing form
      final existingForm = localTask.forms
          .where((f) => f.formId == formId)
          .firstOrNull;

      if (existingForm != null) {
        // Update existing form structure (not answers)
        _updateFormStructure(existingForm, formEntity);
      } else {
        // Add new form
        final newForm = _createFormFromEntity(formEntity);
        localTask.forms.add(newForm);
      }
    }

    // Remove forms that no longer exist on server
    final serverFormIds = serverTaskDetail.forms!
        .map((f) => f.formId?.toInt())
        .where((id) => id != null)
        .toSet();

    localTask.forms.removeWhere((form) =>
        !serverFormIds.contains(form.formId));

    // Update modification timestamp
    if (serverTaskDetail.modifiedTimeStampForms != null) {
      localTask.modifiedTimeStampForms = serverTaskDetail.modifiedTimeStampForms;
    }
  }

  /// Update form structure without overwriting answers
  void _updateFormStructure(FormModel form, Form formEntity) {
    form.formName = formEntity.formName;
    form.briefUrl = formEntity.briefUrl;
    form.isVisionForm = formEntity.isVisionForm;
    form.visionFormUrl = formEntity.visionFormUrl;
    form.isMandatory = formEntity.isMandatory;
    form.formAllowForward = formEntity.formAllowForward;
    form.modifiedTimeStampForm = formEntity.modifiedTimeStampForm;

    // Update questions structure
    if (formEntity.questions != null) {
      form.questions.clear();
      for (final questionEntity in formEntity.questions!) {
        final question = _createQuestionFromEntity(questionEntity);
        form.questions.add(question);
      }
    }
  }

  /// Create FormModel from server entity
  FormModel _createFormFromEntity(Form formEntity) {
    final form = FormModel();
    form.formId = formEntity.formId?.toInt();
    form.formInstanceId = formEntity.formInstanceId?.toInt();
    form.formName = formEntity.formName;
    form.briefUrl = formEntity.briefUrl;
    form.isVisionForm = formEntity.isVisionForm;
    form.visionFormUrl = formEntity.visionFormUrl;
    form.isMandatory = formEntity.isMandatory;
    form.formAllowForward = formEntity.formAllowForward;
    form.modifiedTimeStampForm = formEntity.modifiedTimeStampForm;

    // Add questions
    if (formEntity.questions != null) {
      for (final questionEntity in formEntity.questions!) {
        final question = _createQuestionFromEntity(questionEntity);
        form.questions.add(question);
      }
    }

    return form;
  }

  /// Create QuestionModel from server data
  QuestionModel _createQuestionFromMap(Map<String, dynamic> questionMap) {
    final question = QuestionModel();
    question.questionId = questionMap['question_id'];
    question.questionOrderId = questionMap['question_order_id'];
    question.questionDescription = questionMap['question_description'];
    question.isComment = questionMap['is_comment'];
    question.isCommentMandatory = questionMap['is_comment_mandatory'];
    question.hasSignature = questionMap['has_signature'];
    question.isSignatureMandatory = questionMap['is_signature_mandatory'];
    question.questionBrief = questionMap['question_brief'];
    question.isMulti = questionMap['is_multi'];
    question.multiMeasurementId = questionMap['multi_measurement_id'];
    question.isMultiOneAnswer = questionMap['is_multi_one_answer'];
    question.questionTypeId = questionMap['question_type_id'];

    final modifiedTimestamp = questionMap['modified_time_stamp_question'];
    if (modifiedTimestamp != null) {
      question.modifiedTimeStampQuestion = DateTime.tryParse(modifiedTimestamp.toString());
    }

    // Add question parts and measurements
    final questionParts = questionMap['question_parts'] as List<dynamic>?;
    if (questionParts != null) {
      for (final partData in questionParts) {
        final partMap = partData as Map<String, dynamic>;
        final questionPart = _createQuestionPartFromMap(partMap);
        question.questionParts.add(questionPart);
      }
    }

    final measurements = questionMap['measurements'] as List<dynamic>?;
    if (measurements != null) {
      for (final measurementData in measurements) {
        final measurementMap = measurementData as Map<String, dynamic>;
        final measurement = _createMeasurementFromMap(measurementMap);
        question.measurements.add(measurement);
      }
    }

    return question;
  }

  /// Create QuestionPartModel from server data
  QuestionPartModel _createQuestionPartFromMap(Map<String, dynamic> partMap) {
    final questionPart = QuestionPartModel();
    questionPart.projectid = partMap['project_id'];
    questionPart.questionpartId = partMap['questionpart_id'];
    questionPart.questionpartDescription = partMap['questionpart_description'];
    questionPart.price = partMap['price'];
    questionPart.companyId = partMap['company_id'];
    questionPart.itemImage = partMap['item_image'];
    questionPart.targeted = partMap['targeted'];

    final modifiedTimestamp = partMap['modified_time_stamp_questionpart'];
    if (modifiedTimestamp != null) {
      questionPart.modifiedTimeStampQuestionpart = DateTime.tryParse(modifiedTimestamp.toString());
    }

    return questionPart;
  }

  /// Create MeasurementModel from server data
  MeasurementModel _createMeasurementFromMap(Map<String, dynamic> measurementMap) {
    final measurement = MeasurementModel();
    measurement.measurementId = measurementMap['measurement_id'];
    measurement.measurementTypeId = measurementMap['measurement_type_id'];
    measurement.measurementDescription = measurementMap['measurement_description'];
    measurement.measurementTypeName = measurementMap['measurement_type_name'];
    measurement.defaultAction = measurementMap['default_action'];
    measurement.measurementDefaultsResult = measurementMap['measurement_defaults_result'];
    measurement.measurementOrderId = measurementMap['measurement_order_id'];
    measurement.mandatoryPhototypesCount = measurementMap['mandatory_phototypes_count'];
    measurement.optionalPhototypesCount = measurementMap['optional_phototypes_count'];
    measurement.measurementImage = measurementMap['measurement_image'];
    measurement.companyid = measurementMap['company_id'];
    measurement.validationTypeId = measurementMap['validation_type_id'];
    measurement.required = measurementMap['required'];
    measurement.rangeValidation = measurementMap['range_validation'];
    measurement.expressionValidation = measurementMap['expression_validation'];
    measurement.errorMessage = measurementMap['error_message'];

    // Add measurement options
    final options = measurementMap['measurement_options'] as List<dynamic>?;
    if (options != null) {
      for (final optionData in options) {
        final optionMap = optionData as Map<String, dynamic>;
        final option = _createMeasurementOptionFromMap(optionMap);
        measurement.measurementOptions.add(option);
      }
    }

    return measurement;
  }

  /// Create MeasurementOptionModel from server data
  MeasurementOptionModel _createMeasurementOptionFromMap(Map<String, dynamic> optionMap) {
    final option = MeasurementOptionModel();
    option.measurementId = optionMap['measurement_id'];
    option.measurementOptionId = optionMap['measurement_option_id'];
    option.measurementOptionDescription = optionMap['measurement_option_description'];
    option.budgetOffset = optionMap['budget_offset'];
    option.budgetOffsetType = optionMap['budget_offset_type'];
    option.isAnswer = optionMap['is_answer'];

    final modifiedTimestamp = optionMap['modified_time_stamp_measurementoption'];
    if (modifiedTimestamp != null) {
      option.modifiedTimeStampMeasurementoption = DateTime.tryParse(modifiedTimestamp.toString());
    }

    return option;
  }

  /// Process task member updates
  Future<void> _processTaskMemberUpdates(List<TaskMembersHelper>? updateTaskMembers) async {
    if (updateTaskMembers == null || updateTaskMembers.isEmpty) return;

    logger('👥 Processing ${updateTaskMembers.length} task member updates');

    for (final memberHelper in updateTaskMembers) {
      try {
        final taskId = memberHelper.taskId;
        if (taskId == null) continue;

        final localTask = _realmDatabase.realm
            .query<TaskDetailModel>('taskId == \$0', [taskId])
            .firstOrNull;

        if (localTask != null) {
          await _updateTaskMembers(localTask, memberHelper);
        }
      } catch (e) {
        logger('❌ Error processing task member update: $e');
      }
    }
  }

  /// Update task members with server data
  Future<void> _updateTaskMembers(
    TaskDetailModel localTask,
    TaskMembersHelper memberHelper,
  ) async {
    if (memberHelper.taskMembers == null) return;

    // Clear existing members
    localTask.taskmembers.clear();

    // Add updated members
    for (final memberEntity in memberHelper.taskMembers!) {
      final member = _createTaskMemberFromEntity(memberEntity);
      localTask.taskmembers.add(member);
    }

    // Update modification timestamp
    localTask.modifiedTimeStampMembers = DateTime.now();
  }

  /// Create TaskmemberModel from server entity
  TaskmemberModel _createTaskMemberFromEntity(Taskmember memberEntity) {
    final member = TaskmemberModel();
    member.fullname = memberEntity.fullname;
    member.teamLead = memberEntity.teamLead?.toInt();
    member.email = memberEntity.email;
    member.scheduleId = memberEntity.scheduleId?.toInt();
    member.taskId = memberEntity.taskId?.toInt();
    return member;
  }

  /// Process task information updates
  Future<void> _processTaskInformationUpdates(
    List<TaskDetail>? updateTasksTasks,
    List<String> taskIDsToCalculateBudget,
  ) async {
    if (updateTasksTasks == null || updateTasksTasks.isEmpty) return;

    logger('ℹ️ Processing ${updateTasksTasks.length} task information updates');

    for (final taskDetail in updateTasksTasks) {
      try {
        final taskId = taskDetail.taskId;
        if (taskId == null) continue;

        final localTask = _realmDatabase.realm
            .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()])
            .firstOrNull;

        if (localTask != null) {
          await _updateTaskInformation(localTask, taskDetail, taskIDsToCalculateBudget);
        }
      } catch (e) {
        logger('❌ Error processing task information update: $e');
      }
    }
  }

  /// Update task information with server data
  Future<void> _updateTaskInformation(
    TaskDetailModel localTask,
    TaskDetail serverTaskDetail,
    List<String> taskIDsToCalculateBudget,
  ) async {
    // Update client information
    localTask.clientId = serverTaskDetail.clientId?.toInt();
    localTask.client = serverTaskDetail.client;
    localTask.clientLogoUrl = serverTaskDetail.clientLogoUrl;

    // Delete local logo file if it exists
    await _deleteLocalFile(localTask.clientLogoUrl);

    // Update store information
    localTask.storeId = serverTaskDetail.storeId?.toInt();
    localTask.storeGroupId = serverTaskDetail.storeGroupId?.toInt();
    localTask.storeGroup = serverTaskDetail.storeGroup;
    localTask.storeName = serverTaskDetail.storeName;
    localTask.location = serverTaskDetail.location;
    localTask.suburb = serverTaskDetail.suburb;
    localTask.phone = serverTaskDetail.phone;
    localTask.latitude = serverTaskDetail.latitude?.toDouble();
    localTask.longitude = serverTaskDetail.longitude?.toDouble();

    // Update task cycle information
    localTask.cycleId = serverTaskDetail.cycleId?.toInt();
    localTask.cycle = serverTaskDetail.cycle;

    // Update budget and add to calculation list
    localTask.budget = serverTaskDetail.budget?.toInt();
    localTask.originalbudget = serverTaskDetail.originalbudget?.toInt();
    final taskIdStr = localTask.taskId.toString();
    if (!taskIDsToCalculateBudget.contains(taskIdStr)) {
      taskIDsToCalculateBudget.add(taskIdStr);
    }

    // Update task status and dates
    localTask.reOpened = serverTaskDetail.reOpened;
    localTask.reOpenedReason = serverTaskDetail.reOpenedReason;
    localTask.taskStatus = serverTaskDetail.taskStatus;
    localTask.submissionState = 0; // TASK_SUBMISSION_STATE_DEFAULT

    localTask.rangeStart = serverTaskDetail.rangeStart;
    localTask.rangeEnd = serverTaskDetail.rangeEnd;
    localTask.scheduledTimeStamp = serverTaskDetail.scheduledTimeStamp;
    localTask.expires = serverTaskDetail.expires;
    localTask.connoteUrl = serverTaskDetail.connoteUrl;

    // Update task completion status
    final taskStatus = localTask.taskStatus ?? '';
    localTask.isOpen = !(taskStatus.toLowerCase() == 'successful' ||
                        taskStatus.toLowerCase() == 'unsuccessful');

    localTask.syncPending = false;

    // Update POS items
    if (serverTaskDetail.posItems != null) {
      localTask.posItems.clear();
      for (final posEntity in serverTaskDetail.posItems!) {
        final posItem = _createPosItemFromEntity(posEntity);
        localTask.posItems.add(posItem);
      }
    }

    // Update followup tasks
    if (serverTaskDetail.followupTasks != null) {
      localTask.followupTasks.clear();
      for (final followupEntity in serverTaskDetail.followupTasks!) {
        final followupTask = _createFollowupTaskFromEntity(followupEntity);
        localTask.followupTasks.add(followupTask);
      }
    }

    // Update followup settings
    localTask.showFollowupIconMulti = serverTaskDetail.showFollowupIconMulti;
    localTask.followupSelectedMulti = serverTaskDetail.followupSelectedMulti;

    // Update task note and reschedule settings
    localTask.taskNote = serverTaskDetail.taskNote;
    localTask.disallowReschedule = serverTaskDetail.disallowReschedule;

    // Update modification timestamp
    localTask.modifiedTimeStampTask = serverTaskDetail.modifiedTimeStampTask;

    // Update additional settings
    localTask.posRequired = serverTaskDetail.posRequired;
    localTask.photoResPerc = serverTaskDetail.photoResPerc?.toInt();
    localTask.liveImagesOnly = serverTaskDetail.liveImagesOnly;
  }

  /// Create PosItemModel from server entity
  PosItemModel _createPosItemFromEntity(PosItem posEntity) {
    final posItem = PosItemModel();
    posItem.itemName = posEntity.itemName;
    posItem.itemAmount = posEntity.itemAmount?.toInt();
    posItem.photoUrl = posEntity.photoUrl;
    return posItem;
  }

  /// Create FollowupTaskModel from server entity
  FollowupTaskModel _createFollowupTaskFromEntity(FollowupTask followupEntity) {
    final followupTask = FollowupTaskModel();
    followupTask.showFollowupIcon = followupEntity.showFollowupIcon;
    followupTask.followupSelected = followupEntity.followupSelected;
    followupTask.selectedVisitDate = followupEntity.selectedVisitDate;
    followupTask.selectedFollowupTypeId = followupEntity.selectedFollowupTypeId?.toInt();
    followupTask.selectedFollowupItemId = followupEntity.selectedFollowupItemId?.toInt();
    followupTask.selectedBudget = followupEntity.selectedBudget?.toInt();
    followupTask.selectedFollowupType = followupEntity.selectedFollowupType;
    followupTask.selectedFollowupItem = followupEntity.selectedFollowupItem;
    followupTask.selectedScheduleNote = followupEntity.selectedScheduleNote;
    followupTask.followupNumber = followupEntity.followupNumber?.toInt();
    return followupTask;
  }

  /// Helper method to parse DateTime from various formats
  DateTime? _parseDateTime(dynamic dateValue) {
    if (dateValue == null) return null;
    if (dateValue is DateTime) return dateValue;
    if (dateValue is String) return DateTime.tryParse(dateValue);
    if (dateValue is int) return DateTime.fromMillisecondsSinceEpoch(dateValue);
    return null;
  }

  /// Process photo updates
  Future<void> _processPhotoUpdates(List<TaskDetail>? updateTasksPhotos) async {
    if (updateTasksPhotos == null || updateTasksPhotos.isEmpty) return;

    logger('📸 Processing ${updateTasksPhotos.length} photo updates');

    for (final taskDetail in updateTasksPhotos) {
      try {
        final taskId = taskDetail.taskId;
        if (taskId == null) continue;

        final localTask = _realmDatabase.realm
            .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()])
            .firstOrNull;

        if (localTask != null) {
          await _updateTaskPhotos(localTask, taskDetail);
        }
      } catch (e) {
        logger('❌ Error processing photo update: $e');
      }
    }
  }

  /// Update task photos with server data
  Future<void> _updateTaskPhotos(
    TaskDetailModel localTask,
    TaskDetail serverTaskDetail,
  ) async {
    if (serverTaskDetail.photoFolder == null) return;

    for (final folderEntity in serverTaskDetail.photoFolder!) {
      final folderId = folderEntity.folderId?.toInt();
      if (folderId == null) continue;

      final localPhotoFolder = localTask.photoFolder
          .where((f) => f.folderId == folderId)
          .firstOrNull;

      if (localPhotoFolder != null) {
        await _updatePhotoFolder(localPhotoFolder, folderEntity);
      }
    }

    // Mark synced photos for deletion if they no longer exist on server
    await _markDeletedPhotos(localTask, serverTaskDetail);

    // Update modification timestamp
    if (serverTaskDetail.modifiedTimeStampPhotos != null) {
      localTask.modifiedTimeStampPhotos = serverTaskDetail.modifiedTimeStampPhotos;
    }
  }

  /// Update photo folder with server data
  Future<void> _updatePhotoFolder(
    PhotoFolderModel localFolder,
    PhotoFolder folderEntity,
  ) async {
    if (folderEntity.photos == null) return;

    for (final photoEntity in folderEntity.photos!) {
      final photoId = photoEntity.photoId?.toInt();
      if (photoId == null) continue;

      final localPhoto = localFolder.photos
          .where((p) => p.photoId == photoId)
          .firstOrNull;

      if (localPhoto != null) {
        // Update existing photo if server has newer version
        await _updatePhotoIfNewer(localPhoto, photoEntity);
      } else {
        // Add new photo from server
        final newPhoto = _createPhotoFromEntity(photoEntity);
        localFolder.photos.add(newPhoto);
      }
    }

    // Update folder properties
    localFolder.folderName = folderEntity.folderName;
    localFolder.folderPictureAmount = folderEntity.folderPictureAmount?.toInt();
    localFolder.imageRec = folderEntity.imageRec;
    localFolder.attribute = folderEntity.attribute;
    localFolder.modifiedTimeStampPhototype = folderEntity.modifiedTimeStampPhototype;
  }

  /// Update photo if server version is newer
  Future<void> _updatePhotoIfNewer(
    PhotoModel localPhoto,
    Photo serverPhotoEntity,
  ) async {
    final serverModified = serverPhotoEntity.modifiedTimeStampPhoto;
    final localModified = localPhoto.modifiedTimeStampPhoto;

    if (serverModified != null &&
        (localModified == null || serverModified.isAfter(localModified))) {

      // Delete old local file
      await _deleteLocalFile(localPhoto.localPath);

      // Update photo with server data
      localPhoto.photoUrl = serverPhotoEntity.photoUrl;
      localPhoto.thumbnailUrl = serverPhotoEntity.thumbnailUrl;
      localPhoto.caption = serverPhotoEntity.caption;
      localPhoto.modifiedTimeStampPhoto = serverModified;
      localPhoto.cannotUploadMandatory = serverPhotoEntity.cannotUploadMandatory;
      localPhoto.imageRec = serverPhotoEntity.imageRec;

      // Clear local path since we now have server version
      localPhoto.localPath = null;
      localPhoto.isEdited = false;
    }
  }

  /// Create PhotoModel from server entity
  PhotoModel _createPhotoFromEntity(Photo photoEntity) {
    final photo = PhotoModel();
    photo.formId = photoEntity.formId?.toInt();
    photo.questionId = photoEntity.questionId?.toInt();
    photo.measurementId = photoEntity.measurementId?.toInt();
    photo.folderId = photoEntity.folderId?.toInt();
    photo.photoId = photoEntity.photoId?.toInt();
    photo.photoUrl = photoEntity.photoUrl;
    photo.thumbnailUrl = photoEntity.thumbnailUrl;
    photo.caption = photoEntity.caption;
    photo.modifiedTimeStampPhoto = photoEntity.modifiedTimeStampPhoto;
    photo.cannotUploadMandatory = photoEntity.cannotUploadMandatory;
    photo.userDeletedPhoto = photoEntity.userDeletedPhoto;
    photo.imageRec = photoEntity.imageRec;
    photo.questionpartId = photoEntity.questionpartId?.toInt();
    photo.questionPartMultiId = photoEntity.questionPartMultiId;
    photo.measurementPhototypeId = photoEntity.measurementPhototypeId?.toInt();
    photo.combineTypeId = photoEntity.combineTypeId?.toInt();
    photo.photoTagId = photoEntity.photoTagId?.toInt();
    photo.photoCombinetypeId = photoEntity.photoCombinetypeId?.toInt();
    return photo;
  }

  /// Mark photos for deletion if they no longer exist on server
  Future<void> _markDeletedPhotos(
    TaskDetailModel localTask,
    Map<String, dynamic> serverData,
  ) async {
    // Get all server photo IDs
    final Set<int> serverPhotoIds = {};
    final photoFolders = serverData['photo_folders'] as List<dynamic>?;
    if (photoFolders != null) {
      for (final folderData in photoFolders) {
        final folderMap = folderData as Map<String, dynamic>;
        final photos = folderMap['photos'] as List<dynamic>?;
        if (photos != null) {
          for (final photoData in photos) {
            final photoMap = photoData as Map<String, dynamic>;
            final photoId = photoMap['photo_id'] as int?;
            if (photoId != null) {
              serverPhotoIds.add(photoId);
            }
          }
        }
      }
    }

    // Mark local photos for deletion if they're synced but not on server
    for (final folder in localTask.photoFolder) {
      for (final photo in folder.photos) {
        if (photo.photoId != null &&
            !serverPhotoIds.contains(photo.photoId!) &&
            photo.isEdited != true) {
          photo.userDeletedPhoto = true;
        }
      }
    }
  }

  /// Process new tasks
  Future<void> _processNewTasks(
    List<TaskDetail>? addTasks,
    List<String> taskIDsToCalculateBudget,
  ) async {
    if (addTasks == null || addTasks.isEmpty) return;

    logger('➕ Processing ${addTasks.length} new tasks');

    for (final taskDetail in addTasks) {
      try {
        final newTask = await _createTaskFromServerEntity(taskDetail);

        _realmDatabase.realm.add(newTask);

        // Add to budget calculation list
        final taskIdStr = newTask.taskId.toString();
        if (!taskIDsToCalculateBudget.contains(taskIdStr)) {
          taskIDsToCalculateBudget.add(taskIdStr);
        }

        logger('➕ Added new task: ${newTask.taskId}');
      } catch (e) {
        logger('❌ Error processing new task: $e');
      }
    }
  }

  /// Create TaskDetailModel from server entity
  Future<TaskDetailModel> _createTaskFromServerEntity(TaskDetail taskEntity) async {
    // Use the existing TaskDetailMapper to convert entity to model
    final task = TaskDetailMapper.toModel(
      taskEntity,
      DateTime.now().millisecondsSinceEpoch, // Generate unique ID
    );

    // Set sync status for new tasks
    task.isSynced = false;
    task.syncPending = false;

    return task;
  }

  /// Calculate budgets for specified tasks
  Future<void> _calculateBudgetsForTasks(List<String> taskIds) async {
    if (taskIds.isEmpty) return;

    logger('💰 Calculating budgets for ${taskIds.length} tasks');

    for (final taskIdStr in taskIds) {
      try {
        final taskId = int.tryParse(taskIdStr);
        if (taskId == null) continue;

        final task = _realmDatabase.realm
            .query<TaskDetailModel>('taskId == \$0', [taskId])
            .firstOrNull;

        if (task != null) {
          await _calculateTaskBudget(task);
        }
      } catch (e) {
        logger('❌ Error calculating budget for task $taskIdStr: $e');
      }
    }
  }

  /// Calculate budget for a specific task
  Future<void> _calculateTaskBudget(TaskDetailModel task) async {
    // Implement budget calculation logic based on:
    // - Original budget
    // - Form completion status
    // - Answer-based budget modifications
    // - Photo requirements

    int calculatedBudget = task.originalbudget ?? task.budget ?? 0;

    // Add logic for budget calculations based on form answers
    // This would involve checking form completion, answer values, etc.

    _realmDatabase.realm.write(() {
      task.budget = calculatedBudget;
    });
  }

  /// Process submission updates (form answers)
  Future<void> _processSubmissionUpdates(
    List<TaskDetail>? updateTasksSubmission,
    List<String> taskIDsToCalculateBudget,
  ) async {
    if (updateTasksSubmission == null || updateTasksSubmission.isEmpty) return;

    logger('📋 Processing ${updateTasksSubmission.length} submission updates');

    for (final taskDetail in updateTasksSubmission) {
      try {
        final taskId = taskDetail.taskId;
        if (taskId == null) continue;

        final localTask = _realmDatabase.realm
            .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()])
            .firstOrNull;

        if (localTask != null) {
          await _updateTaskSubmission(localTask, taskDetail, taskIDsToCalculateBudget);
        }
      } catch (e) {
        logger('❌ Error processing submission update: $e');
      }
    }
  }

  /// Update task submission data
  Future<void> _updateTaskSubmission(
    TaskDetailModel localTask,
    TaskDetail serverTaskDetail,
    List<String> taskIDsToCalculateBudget,
  ) async {
    // Update form counts
    localTask.ctFormsTotalCnt = serverTaskDetail.ctFormsTotalCnt?.toInt();
    localTask.ctFormsCompletedCnt = serverTaskDetail.ctFormsCompletedCnt?.toInt();

    // Add to budget calculation list
    final taskIdStr = localTask.taskId.toString();
    if (!taskIDsToCalculateBudget.contains(taskIdStr)) {
      taskIDsToCalculateBudget.add(taskIdStr);
    }

    // Update task submission details
    localTask.comment = serverTaskDetail.comment;
    localTask.minutes = serverTaskDetail.minutes?.toInt();
    localTask.claimableKms = serverTaskDetail.claimableKms?.toInt();
    localTask.pages = serverTaskDetail.pages?.toInt();
    localTask.taskStatus = serverTaskDetail.taskStatus;
    localTask.submissionState = 0; // TASK_SUBMISSION_STATE_DEFAULT

    localTask.scheduledTimeStamp = serverTaskDetail.scheduledTimeStamp;
    localTask.submissionTimeStamp = serverTaskDetail.submissionTimeStamp;

    localTask.taskLatitude = serverTaskDetail.taskLatitude?.toDouble() ?? 0.0;
    localTask.taskLongitude = serverTaskDetail.taskLongitude?.toDouble() ?? 0.0;

    // Update form answers
    if (serverTaskDetail.forms != null) {
      for (final formEntity in serverTaskDetail.forms!) {
        final formId = formEntity.formId?.toInt();
        if (formId == null) continue;

        final localForm = localTask.forms
            .where((f) => f.formId == formId)
            .firstOrNull;

        if (localForm != null) {
          await _updateFormAnswers(localForm, formEntity);
        }
      }
    }

    // Update task completion status
    final taskStatus = localTask.taskStatus ?? '';
    localTask.isOpen = !(taskStatus.toLowerCase() == 'successful' ||
                        taskStatus.toLowerCase() == 'unsuccessful');

    localTask.syncPending = false;
  }

  /// Update form answers from server data
  Future<void> _updateFormAnswers(
    FormModel localForm,
    Form formEntity,
  ) async {
    // Clear existing answers
    localForm.questionAnswers.clear();

    // Add updated answers
    if (formEntity.questionAnswers != null) {
      for (final answerEntity in formEntity.questionAnswers!) {
        final answer = _createQuestionAnswerFromEntity(answerEntity);
        localForm.questionAnswers.add(answer);
      }
    }

    // Update form completion status
    localForm.formCompleted = formEntity.formCompleted ?? false;
    localForm.formEdited = formEntity.formEdited ?? false;

    // Rebuild multi-question parts if needed
    for (final question in localForm.questions) {
      if (question.isMulti == true) {
        await _rebuildMultiQuestionParts(localForm, question);
      }
    }
  }

  /// Create QuestionAnswerModel from server entity
  QuestionAnswerModel _createQuestionAnswerFromEntity(QuestionAnswer answerEntity) {
    final answer = QuestionAnswerModel();
    answer.taskId = answerEntity.taskId?.toInt();
    answer.formId = answerEntity.formId?.toInt();
    answer.questionId = answerEntity.questionId?.toInt();
    answer.questionpartId = answerEntity.questionpartId?.toInt();
    answer.flip = answerEntity.flip;
    answer.questionPartMultiId = answerEntity.questionPartMultiId;
    answer.measurementId = answerEntity.measurementId?.toInt();
    answer.measurementTypeId = answerEntity.measurementTypeId?.toInt();
    answer.measurementOptionId = answerEntity.measurementOptionId?.toInt();
    answer.measurementOptionIds = answerEntity.measurementOptionIds;
    answer.measurementTextResult = answerEntity.measurementTextResult;
    answer.isComment = answerEntity.isComment;
    answer.commentTypeId = answerEntity.commentTypeId?.toInt();
    return answer;
  }

  /// Rebuild multi-question parts from answers
  Future<void> _rebuildMultiQuestionParts(
    FormModel form,
    QuestionModel question,
  ) async {
    // This implements the logic from buildQuestionPartMultiFromAnswers
    // in the original Java code - rebuilding question parts based on answers

    final answers = form.questionAnswers
        .where((a) => a.questionId == question.questionId)
        .toList();

    // Clear existing question parts
    question.questionParts.clear();

    // Rebuild parts based on answers
    final Map<String, QuestionPartModel> partMap = {};

    for (final answer in answers) {
      final multiId = answer.questionPartMultiId;
      if (multiId != null && !partMap.containsKey(multiId)) {
        final questionPart = QuestionPartModel();
        questionPart.questionpartId = answer.questionpartId;
        questionPart.questionpartDescription = 'Multi Part $multiId';
        // Set other properties as needed

        partMap[multiId] = questionPart;
        question.questionParts.add(questionPart);
      }
    }
  }

  /// Process signature updates
  Future<void> _processSignatureUpdates(List<TaskDetail>? updateTasksSignatures) async {
    if (updateTasksSignatures == null || updateTasksSignatures.isEmpty) return;

    logger('✍️ Processing ${updateTasksSignatures.length} signature updates');

    for (final taskDetail in updateTasksSignatures) {
      try {
        final taskId = taskDetail.taskId;
        if (taskId == null) continue;

        final localTask = _realmDatabase.realm
            .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()])
            .firstOrNull;

        if (localTask != null) {
          await _updateTaskSignatures(localTask, taskDetail);
        }
      } catch (e) {
        logger('❌ Error processing signature update: $e');
      }
    }
  }

  /// Update task signatures with server data
  Future<void> _updateTaskSignatures(
    TaskDetailModel localTask,
    TaskDetail serverTaskDetail,
  ) async {
    if (serverTaskDetail.signatureFolder == null) return;

    for (final folderEntity in serverTaskDetail.signatureFolder!) {
      final folderId = folderEntity.folderId?.toInt();
      if (folderId == null) continue;

      final localSignatureFolder = localTask.signatureFolder
          .where((f) => f.folderId == folderId)
          .firstOrNull;

      if (localSignatureFolder != null) {
        await _updateSignatureFolder(localSignatureFolder, folderEntity);
      }
    }

    // Mark synced signatures for deletion if they no longer exist on server
    await _markDeletedSignatures(localTask, serverTaskDetail);

    // Update modification timestamp
    if (serverTaskDetail.modifiedTimeStampSignatures != null) {
      localTask.modifiedTimeStampSignatures = serverTaskDetail.modifiedTimeStampSignatures;
    }
  }

  /// Update signature folder with server data
  Future<void> _updateSignatureFolder(
    SignatureFolderModel localFolder,
    Map<String, dynamic> folderData,
  ) async {
    final signatures = folderData['signatures'] as List<dynamic>?;
    if (signatures == null) return;

    for (final signatureData in signatures) {
      final signatureMap = signatureData as Map<String, dynamic>;
      final signatureId = signatureMap['signature_id'];

      final localSignature = localFolder.signatures
          .where((s) => s.signatureId == signatureId)
          .firstOrNull;

      if (localSignature != null) {
        // Update existing signature if server has newer version
        await _updateSignatureIfNewer(localSignature, signatureMap);
      } else {
        // Add new signature from server
        final newSignature = _createSignatureFromMap(signatureMap);
        localFolder.signatures.add(newSignature);
      }
    }
  }

  /// Update signature if server version is newer
  Future<void> _updateSignatureIfNewer(
    SignatureModel localSignature,
    Map<String, dynamic> serverSignatureData,
  ) async {
    final serverModified = _parseDateTime(serverSignatureData['modified_time_stamp_signature']);
    final localModified = localSignature.modifiedTimeStampSignature;

    if (serverModified != null &&
        (localModified == null || serverModified.isAfter(localModified))) {

      // Delete old local file
      await _deleteLocalFile(localSignature.localPath);

      // Update signature with server data
      localSignature.signatureUrl = serverSignatureData['signature_url'];
      localSignature.thumbnailUrl = serverSignatureData['thumbnail_url'];
      localSignature.signedBy = serverSignatureData['signed_by'];
      localSignature.modifiedTimeStampSignature = serverModified;
      localSignature.cannotUploadMandatory = serverSignatureData['cannot_upload_mandatory'];

      // Clear local path since we now have server version
      localSignature.localPath = null;
      localSignature.isEdited = false;
    }
  }

  /// Create SignatureModel from server data
  SignatureModel _createSignatureFromMap(Map<String, dynamic> signatureMap) {
    final signature = SignatureModel();
    signature.signatureId = signatureMap['signature_id'];
    signature.formId = signatureMap['form_id'];
    signature.questionId = signatureMap['question_id'];
    signature.signatureUrl = signatureMap['signature_url'];
    signature.thumbnailUrl = signatureMap['thumbnail_url'];
    signature.signedBy = signatureMap['signed_by'];
    signature.modifiedTimeStampSignature = _parseDateTime(signatureMap['modified_time_stamp_signature']);
    signature.cannotUploadMandatory = signatureMap['cannot_upload_mandatory'];
    signature.userDeletedSignature = signatureMap['user_deleted_signature'];
    return signature;
  }

  /// Mark signatures for deletion if they no longer exist on server
  Future<void> _markDeletedSignatures(
    TaskDetailModel localTask,
    Map<String, dynamic> serverData,
  ) async {
    // Similar logic to _markDeletedPhotos but for signatures
    final Set<int> serverSignatureIds = {};
    final signatureFolders = serverData['signature_folders'] as List<dynamic>?;
    if (signatureFolders != null) {
      for (final folderData in signatureFolders) {
        final folderMap = folderData as Map<String, dynamic>;
        final signatures = folderMap['signatures'] as List<dynamic>?;
        if (signatures != null) {
          for (final signatureData in signatures) {
            final signatureMap = signatureData as Map<String, dynamic>;
            final signatureId = signatureMap['signature_id'] as int?;
            if (signatureId != null) {
              serverSignatureIds.add(signatureId);
            }
          }
        }
      }
    }

    // Mark local signatures for deletion if they're synced but not on server
    for (final folder in localTask.signatureFolder) {
      for (final signature in folder.signatures) {
        if (signature.signatureId != null &&
            !serverSignatureIds.contains(signature.signatureId!) &&
            signature.isEdited != true) {
          signature.userDeletedSignature = true;
        }
      }
    }
  }

  /// Process photo type updates
  Future<void> _processPhotoTypeUpdates(Map<String, dynamic> dataObject) async {
    final updatePhotoTypesArray = dataObject['update_phototypes'] as List<dynamic>?;
    if (updatePhotoTypesArray == null) return;

    logger('🏷️ Processing ${updatePhotoTypesArray.length} photo type updates');

    for (final photoTypeUpdate in updatePhotoTypesArray) {
      try {
        final updateMap = photoTypeUpdate as Map<String, dynamic>;
        final taskId = updateMap['task_id']?.toString();
        if (taskId == null) continue;

        final localTask = _realmDatabase.realm
            .query<TaskDetailModel>('taskId == \$0', [int.tryParse(taskId)])
            .firstOrNull;

        if (localTask != null) {
          await _updatePhotoTypes(localTask, updateMap);
        }
      } catch (e) {
        logger('❌ Error processing photo type update: $e');
      }
    }
  }

  /// Update photo types for a task
  Future<void> _updatePhotoTypes(
    TaskDetailModel localTask,
    Map<String, dynamic> serverData,
  ) async {
    final photoTypes = serverData['photo_types'] as List<dynamic>?;
    if (photoTypes == null) return;

    for (final photoTypeData in photoTypes) {
      final photoTypeMap = photoTypeData as Map<String, dynamic>;
      final photoTypeId = photoTypeMap['phototype_id'];

      final localPhotoFolder = localTask.photoFolder
          .where((f) => f.folderId == photoTypeId)
          .firstOrNull;

      if (localPhotoFolder != null) {
        // Update existing photo folder properties
        localPhotoFolder.attribute = photoTypeMap['mandatory'];
        localPhotoFolder.folderName = photoTypeMap['phototype_name'];
        localPhotoFolder.folderPictureAmount = photoTypeMap['phototype_picture_amount'];
      }
    }

    // Update modification timestamp
    final modifiedTimestamp = serverData['modified_time_stamp_phototypes'];
    if (modifiedTimestamp != null) {
      localTask.modifiedTimeStampPhototypes = DateTime.tryParse(modifiedTimestamp.toString());
    }
  }

  /// Process photo type additions
  Future<void> _processPhotoTypeAdditions(Map<String, dynamic> dataObject) async {
    final addPhotoTypesArray = dataObject['add_phototypes'] as List<dynamic>?;
    if (addPhotoTypesArray == null) return;

    logger('➕ Processing ${addPhotoTypesArray.length} photo type additions');

    for (final photoTypeAddition in addPhotoTypesArray) {
      try {
        final additionMap = photoTypeAddition as Map<String, dynamic>;
        final taskId = additionMap['task_id']?.toString();
        if (taskId == null) continue;

        final localTask = _realmDatabase.realm
            .query<TaskDetailModel>('taskId == \$0', [int.tryParse(taskId)])
            .firstOrNull;

        if (localTask != null) {
          await _addPhotoTypes(localTask, additionMap);
        }
      } catch (e) {
        logger('❌ Error processing photo type addition: $e');
      }
    }
  }

  /// Add new photo types to a task
  Future<void> _addPhotoTypes(
    TaskDetailModel localTask,
    Map<String, dynamic> serverData,
  ) async {
    final photoTypes = serverData['photo_types'] as List<dynamic>?;
    if (photoTypes == null) return;

    for (final photoTypeData in photoTypes) {
      final photoTypeMap = photoTypeData as Map<String, dynamic>;
      final photoTypeId = photoTypeMap['phototype_id'];

      // Check if photo type already exists
      final existingFolder = localTask.photoFolder
          .where((f) => f.folderId == photoTypeId)
          .firstOrNull;

      if (existingFolder == null) {
        // Create new photo folder
        final newPhotoFolder = PhotoFolderModel();
        newPhotoFolder.folderId = photoTypeId;
        newPhotoFolder.attribute = photoTypeMap['mandatory'];
        newPhotoFolder.folderName = photoTypeMap['phototype_name'];
        newPhotoFolder.folderPictureAmount = photoTypeMap['phototype_picture_amount'];
        newPhotoFolder.modifiedTimeStampPhototype =
            _parseDateTime(photoTypeMap['modified_time_stamp_phototype']);

        localTask.photoFolder.add(newPhotoFolder);
      }
    }

    // Update modification timestamp
    final modifiedTimestamp = serverData['modified_time_stamp_phototypes'];
    if (modifiedTimestamp != null) {
      localTask.modifiedTimeStampPhototypes = DateTime.tryParse(modifiedTimestamp.toString());
    }
  }

  /// Process photo type deletions
  Future<void> _processPhotoTypeDeletions(Map<String, dynamic> dataObject) async {
    final deletePhotoTypesArray = dataObject['delete_phototypes'] as List<dynamic>?;
    if (deletePhotoTypesArray == null) return;

    logger('🗑️ Processing ${deletePhotoTypesArray.length} photo type deletions');

    for (final photoTypeDeletion in deletePhotoTypesArray) {
      try {
        final deletionMap = photoTypeDeletion as Map<String, dynamic>;
        final taskId = deletionMap['task_id']?.toString();
        if (taskId == null) continue;

        final localTask = _realmDatabase.realm
            .query<TaskDetailModel>('taskId == \$0', [int.tryParse(taskId)])
            .firstOrNull;

        if (localTask != null) {
          await _deletePhotoTypes(localTask, deletionMap);
        }
      } catch (e) {
        logger('❌ Error processing photo type deletion: $e');
      }
    }
  }

  /// Delete photo types from a task
  Future<void> _deletePhotoTypes(
    TaskDetailModel localTask,
    Map<String, dynamic> serverData,
  ) async {
    final photoTypeIdsToDelete = serverData['phototype_ids_to_be_deleted'] as List<dynamic>?;
    if (photoTypeIdsToDelete == null) return;

    for (final photoTypeIdDynamic in photoTypeIdsToDelete) {
      final photoTypeId = photoTypeIdDynamic.toString();

      final folderToDelete = localTask.photoFolder
          .where((f) => f.folderId.toString() == photoTypeId)
          .firstOrNull;

      if (folderToDelete != null) {
        // Delete all photos in the folder first
        for (final photo in folderToDelete.photos) {
          await _deleteLocalFile(photo.localPath);
        }

        // Remove the folder
        localTask.photoFolder.remove(folderToDelete);
      }
    }
  }

  /// Process signature type updates
  Future<void> _processSignatureTypeUpdates(Map<String, dynamic> dataObject) async {
    final updateSignatureTypesArray = dataObject['update_signaturetypes'] as List<dynamic>?;
    if (updateSignatureTypesArray == null) return;

    logger('✍️ Processing ${updateSignatureTypesArray.length} signature type updates');

    for (final signatureTypeUpdate in updateSignatureTypesArray) {
      try {
        final updateMap = signatureTypeUpdate as Map<String, dynamic>;
        final taskId = updateMap['task_id']?.toString();
        if (taskId == null) continue;

        final localTask = _realmDatabase.realm
            .query<TaskDetailModel>('taskId == \$0', [int.tryParse(taskId)])
            .firstOrNull;

        if (localTask != null) {
          await _updateSignatureTypes(localTask, updateMap);
        }
      } catch (e) {
        logger('❌ Error processing signature type update: $e');
      }
    }
  }

  /// Update signature types for a task
  Future<void> _updateSignatureTypes(
    TaskDetailModel localTask,
    Map<String, dynamic> serverData,
  ) async {
    final signatureTypes = serverData['signature_types'] as List<dynamic>?;
    if (signatureTypes == null) return;

    for (final signatureTypeData in signatureTypes) {
      final signatureTypeMap = signatureTypeData as Map<String, dynamic>;
      final signatureTypeId = signatureTypeMap['signaturetype_id'];

      final localSignatureFolder = localTask.signatureFolder
          .where((f) => f.folderId == signatureTypeId)
          .firstOrNull;

      if (localSignatureFolder != null) {
        // Update existing signature folder properties
        localSignatureFolder.attribute = signatureTypeMap['mandatory'];
        localSignatureFolder.folderName = signatureTypeMap['signaturetype_name'];
      }
    }

    // Update modification timestamp
    final modifiedTimestamp = serverData['modified_time_stamp_signaturetypes'];
    if (modifiedTimestamp != null) {
      localTask.modifiedTimeStampSignaturetypes = DateTime.tryParse(modifiedTimestamp.toString());
    }
  }
}
```

### Usage Example

```dart
// In your sync service or wherever you receive the server data
final processCore = ProcessCoreService();

// Assuming you have the JSON response from server
final Map<String, dynamic> serverResponse = {
  'delete_task_ids': ['123', '456'],
  'update_tasks_documents': [...],
  'update_tasks_forms': [...],
  // ... other arrays
};

try {
  await processCore.processCore(serverResponse);
  logger('✅ Process core completed successfully');
} catch (e) {
  logger('❌ Process core failed: $e');
}
```

### Integration with Service Locator

Add to `service_locator.dart`:

```dart
Future<void> _initializeProcessCore() async {
  sl.registerLazySingleton<ProcessCoreService>(() => ProcessCoreService());
}

// Call this in initLocator()
await _initializeProcessCore();
```
}
```

## Next Steps

1. **Phase 1**: Set up core infrastructure and models
2. **Phase 2**: Implement basic processing methods
3. **Phase 3**: Add photo and signature processing
4. **Phase 4**: Implement submission and multi-question logic
5. **Phase 5**: Integration, testing, and optimization

## Dependencies

- Existing Realm models and mappers
- Current sync service architecture
- File management utilities
- Network and API client infrastructure
