---
description: 
globs: 
alwaysApply: true
---
1. ✅ Code Reuse First
Always scan the existing codebase before implementing anything new.

Reuse existing Cubits, UseCases, Widgets, constants, and utilities where applicable.

Follow established naming conventions, architecture layers, and folder structure.

2. 🎨 UI Consistency and Style
Match the visual language of the app:

Layout, spacing, and responsiveness

Typography, color schemes, shadows, and iconography

Use shared widgets and design tokens from the theme.

Avoid one-off styles or ad-hoc widget trees unless justified.

3. 🔄 Holistic Refactoring and Impact Analysis
After completing a feature or fix:

Identify all impacted areas and update them

Refactor or clean up any now-redundant logic or UI

Ensure state syncing and data flow integrity across components

4. ⚙️ Modular, Reusable Code
Follow SOLID and Clean Architecture principles

Write self-contained components and Cubits with clear responsibilities

Favor constructor injection and avoid tight coupling

5. 📦 State Management Best Practices
Keep state classes immutable and expressive

Represent all possible states (initial, loading, success, error)

Maintain separation of UI and business logic (use Cubits effectively)

6. 🧪 Testing and Safety
Include unit tests for Cubits and use cases

Add widget tests for user flows and conditional rendering

Avoid unhandled edge cases and silent failures

7. 🧼 Code Hygiene and Readability
Format code with dart format, and fix warnings from flutter analyze

Use meaningful names and consistent patterns

Avoid dead code and inline debug prints in production

8. 💻 Don't Shy Away from the Terminal
Use terminal commands confidently for common developer tasks:

flutter pub get to fetch dependencies

flutter pub add <package> to install new packages

flutter clean, flutter run, flutter analyze, flutter test, etc.

Automate repetitive tasks with scripts if helpful